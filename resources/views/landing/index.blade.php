@extends('layouts.landing')

@section('title', 'IELTS AI Scoring - Chấm điểm IELTS Writing tự động với AI')
@section('description', 'Hệ thống chấm điểm IELTS Writing tự động bằng AI. Chính xác 95%, n<PERSON><PERSON> chóng, tiết kiệm chi phí. Đánh giá chi tiết theo 4 tiêu chí chấm điểm IELTS.')

@section('content')
<!-- Hero Section -->
<section id="home" class="hero-section">
    <div class="hero-background">
        <div class="hero-particles"></div>
        <div class="hero-gradient"></div>
    </div>

    <div class="container">
        <div class="row align-items-center min-vh-100">
            <div class="col-lg-6" data-aos="fade-right">
                <div class="hero-content">
                    <div class="hero-badge">
                        <i class="fas fa-robot"></i>
                        <span>Powered by AI Technology</span>
                    </div>

                    <h1 class="hero-title">
                        Chấm điểm <span class="text-gradient">IELTS Writing</span>
                        tự động với <span class="text-gradient">AI</span>
                    </h1>

                    <p class="hero-subtitle">
                        Hệ thống AI tiên tiến nhất để chấm điểm IELTS Writing.
                        <strong>Chính xác 95%</strong>, nhanh chóng trong <strong>30 giây</strong>,
                        tiết kiệm <strong>80% chi phí</strong> so với chấm tay.
                    </p>

                    <div class="hero-stats">
                        <div class="stat-item">
                            <div class="stat-number">{{ number_format($stats['total_users']) }}+</div>
                            <div class="stat-label">Người dùng</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">{{ number_format($stats['total_attempts']) }}+</div>
                            <div class="stat-label">Bài thi đã chấm</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">{{ $stats['success_rate'] }}%</div>
                            <div class="stat-label">Độ chính xác</div>
                        </div>
                    </div>

                    <div class="hero-actions">
                        <a href="{{ route('register') }}" class="btn btn-gradient btn-lg">
                            <i class="fas fa-rocket"></i>
                            Dùng thử miễn phí
                        </a>
                        <a href="#features" class="btn btn-outline-gradient btn-lg">
                            <i class="fas fa-play"></i>
                            Xem demo
                        </a>
                    </div>

                    <div class="hero-trust">
                        <span class="trust-text">Được tin tưởng bởi</span>
                        <div class="trust-logos">
                            <div class="trust-logo">
                                <i class="fas fa-university"></i>
                                <span>Trường ĐH</span>
                            </div>
                            <div class="trust-logo">
                                <i class="fas fa-graduation-cap"></i>
                                <span>Trung tâm</span>
                            </div>
                            <div class="trust-logo">
                                <i class="fas fa-users"></i>
                                <span>Học viên</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-6" data-aos="fade-left">
                <div class="hero-visual">
                    <div class="hero-dashboard">
                        <div class="dashboard-header">
                            <div class="dashboard-title">
                                <i class="fas fa-brain"></i>
                                IELTS AI Scoring
                            </div>
                            <div class="dashboard-status">
                                <span class="status-dot"></span>
                                Đang phân tích...
                            </div>
                        </div>

                        <div class="dashboard-content">
                            <div class="score-display">
                                <div class="overall-score">
                                    <div class="score-number">7.5</div>
                                    <div class="score-label">Overall Band</div>
                                </div>

                                <div class="criteria-scores">
                                    <div class="criteria-item">
                                        <span class="criteria-name">Task Achievement</span>
                                        <div class="criteria-score">
                                            <div class="score-bar">
                                                <div class="score-fill" style="width: 75%"></div>
                                            </div>
                                            <span class="score-value">7.5</span>
                                        </div>
                                    </div>

                                    <div class="criteria-item">
                                        <span class="criteria-name">Coherence & Cohesion</span>
                                        <div class="criteria-score">
                                            <div class="score-bar">
                                                <div class="score-fill" style="width: 70%"></div>
                                            </div>
                                            <span class="score-value">7.0</span>
                                        </div>
                                    </div>

                                    <div class="criteria-item">
                                        <span class="criteria-name">Lexical Resource</span>
                                        <div class="criteria-score">
                                            <div class="score-bar">
                                                <div class="score-fill" style="width: 80%"></div>
                                            </div>
                                            <span class="score-value">8.0</span>
                                        </div>
                                    </div>

                                    <div class="criteria-item">
                                        <span class="criteria-name">Grammar & Accuracy</span>
                                        <div class="criteria-score">
                                            <div class="score-bar">
                                                <div class="score-fill" style="width: 75%"></div>
                                            </div>
                                            <span class="score-value">7.5</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="ai-insights">
                                <div class="insight-title">
                                    <i class="fas fa-lightbulb"></i>
                                    AI Insights
                                </div>
                                <div class="insight-list">
                                    <div class="insight-item">
                                        <i class="fas fa-check-circle text-success"></i>
                                        Excellent vocabulary range
                                    </div>
                                    <div class="insight-item">
                                        <i class="fas fa-exclamation-triangle text-warning"></i>
                                        Consider more complex sentences
                                    </div>
                                    <div class="insight-item">
                                        <i class="fas fa-info-circle text-info"></i>
                                        Strong task response
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Floating elements -->
                    <div class="floating-elements">
                        <div class="floating-card card-1">
                            <i class="fas fa-brain"></i>
                        </div>
                        <div class="floating-card card-2">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="floating-card card-3">
                            <i class="fas fa-award"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Features Section -->
<section id="features" class="section-padding bg-light">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center" data-aos="fade-up">
                <div class="section-header">
                    <h2 class="section-title">
                        Tại sao chọn <span class="text-gradient">IELTS AI Scoring</span>?
                    </h2>
                    <p class="section-subtitle">
                        Công nghệ AI tiên tiến mang đến trải nghiệm chấm điểm IELTS Writing
                        chính xác, nhanh chóng và tiết kiệm chi phí
                    </p>
                </div>
            </div>
        </div>

        <div class="row g-4">
            <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="100">
                <div class="feature-card">
                    <div class="feature-icon bg-gradient-primary">
                        <i class="fas fa-robot"></i>
                    </div>
                    <h4 class="feature-title">AI Tiên tiến</h4>
                    <p class="feature-description">
                        Sử dụng mô hình AI được huấn luyện trên hàng triệu bài thi IELTS thực tế,
                        đảm bảo độ chính xác cao nhất.
                    </p>
                    <div class="feature-stats">
                        <span class="stat-highlight">95% độ chính xác</span>
                    </div>
                </div>
            </div>

            <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="200">
                <div class="feature-card">
                    <div class="feature-icon bg-gradient-success">
                        <i class="fas fa-clock"></i>
                    </div>
                    <h4 class="feature-title">Nhanh chóng</h4>
                    <p class="feature-description">
                        Nhận kết quả chấm điểm chi tiết chỉ trong 30 giây,
                        nhanh hơn 1000 lần so với chấm tay truyền thống.
                    </p>
                    <div class="feature-stats">
                        <span class="stat-highlight">30 giây</span>
                    </div>
                </div>
            </div>

            <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="300">
                <div class="feature-card">
                    <div class="feature-icon bg-gradient-warning">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                    <h4 class="feature-title">Tiết kiệm</h4>
                    <p class="feature-description">
                        Chi phí chỉ bằng 20% so với chấm tay,
                        giúp bạn tiết kiệm đáng kể trong việc luyện thi.
                    </p>
                    <div class="feature-stats">
                        <span class="stat-highlight">Tiết kiệm 80%</span>
                    </div>
                </div>
            </div>

            <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="400">
                <div class="feature-card">
                    <div class="feature-icon bg-gradient-info">
                        <i class="fas fa-chart-bar"></i>
                    </div>
                    <h4 class="feature-title">Phân tích chi tiết</h4>
                    <p class="feature-description">
                        Đánh giá theo 4 tiêu chí chấm điểm IELTS chính thức với
                        phản hồi chi tiết và gợi ý cải thiện cụ thể.
                    </p>
                </div>
            </div>

            <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="500">
                <div class="feature-card">
                    <div class="feature-icon bg-gradient-secondary">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h4 class="feature-title">Bảo mật tuyệt đối</h4>
                    <p class="feature-description">
                        Dữ liệu được mã hóa và bảo vệ theo tiêu chuẩn quốc tế,
                        đảm bảo thông tin cá nhân luôn an toàn.
                    </p>
                </div>
            </div>

            <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="600">
                <div class="feature-card">
                    <div class="feature-icon bg-gradient-dark">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <h4 class="feature-title">Đa nền tảng</h4>
                    <p class="feature-description">
                        Sử dụng mọi lúc mọi nơi trên máy tính, tablet, điện thoại
                        với giao diện thân thiện và dễ sử dụng.
                    </p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Comparison Section -->
<section id="comparison" class="section-padding">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center" data-aos="fade-up">
                <div class="section-header">
                    <h2 class="section-title">
                        <span class="text-gradient">AI Scoring</span> vs Chấm tay truyền thống
                    </h2>
                    <p class="section-subtitle">
                        So sánh chi tiết giữa công nghệ AI và phương pháp chấm điểm truyền thống
                    </p>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-10 mx-auto">
                <div class="comparison-table" data-aos="fade-up" data-aos-delay="200">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Tiêu chí</th>
                                    <th class="ai-column">
                                        <div class="column-header">
                                            <i class="fas fa-robot"></i>
                                            AI Scoring
                                        </div>
                                    </th>
                                    <th class="traditional-column">
                                        <div class="column-header">
                                            <i class="fas fa-user"></i>
                                            Chấm tay
                                        </div>
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="criteria">
                                        <i class="fas fa-clock"></i>
                                        Thời gian
                                    </td>
                                    <td class="ai-column">
                                        <span class="highlight success">30 giây</span>
                                    </td>
                                    <td class="traditional-column">
                                        <span class="highlight warning">2-7 ngày</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="criteria">
                                        <i class="fas fa-dollar-sign"></i>
                                        Chi phí
                                    </td>
                                    <td class="ai-column">
                                        <span class="highlight success">5,000 VNĐ</span>
                                    </td>
                                    <td class="traditional-column">
                                        <span class="highlight warning">250,000 VNĐ</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="criteria">
                                        <i class="fas fa-bullseye"></i>
                                        Độ chính xác
                                    </td>
                                    <td class="ai-column">
                                        <span class="highlight success">95%</span>
                                    </td>
                                    <td class="traditional-column">
                                        <span class="highlight info">85-90%</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="criteria">
                                        <i class="fas fa-chart-line"></i>
                                        Tính nhất quán
                                    </td>
                                    <td class="ai-column">
                                        <span class="highlight success">100%</span>
                                        <small>Không bị ảnh hưởng cảm xúc</small>
                                    </td>
                                    <td class="traditional-column">
                                        <span class="highlight warning">70-80%</span>
                                        <small>Phụ thuộc tâm trạng giám khảo</small>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="criteria">
                                        <i class="fas fa-comments"></i>
                                        Phản hồi chi tiết
                                    </td>
                                    <td class="ai-column">
                                        <span class="highlight success">Rất chi tiết</span>
                                        <small>Phân tích từng câu, từng đoạn</small>
                                    </td>
                                    <td class="traditional-column">
                                        <span class="highlight info">Tổng quan</span>
                                        <small>Nhận xét chung</small>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="criteria">
                                        <i class="fas fa-calendar"></i>
                                        Khả năng tiếp cận
                                    </td>
                                    <td class="ai-column">
                                        <span class="highlight success">24/7</span>
                                        <small>Mọi lúc mọi nơi</small>
                                    </td>
                                    <td class="traditional-column">
                                        <span class="highlight warning">Hạn chế</span>
                                        <small>Phụ thuộc lịch giám khảo</small>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Technology Section -->
<section id="technology" class="section-padding bg-light">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center" data-aos="fade-up">
                <div class="section-header">
                    <h2 class="section-title">
                        Công nghệ <span class="text-gradient">AI tiên tiến</span>
                    </h2>
                    <p class="section-subtitle">
                        Hệ thống sử dụng các công nghệ AI và Machine Learning hiện đại nhất
                        để đảm bảo độ chính xác cao nhất
                    </p>
                </div>
            </div>
        </div>

        <div class="row g-4">
            <div class="col-lg-6" data-aos="fade-right">
                <div class="tech-visual">
                    <div class="tech-diagram">
                        <div class="tech-layer" data-aos="fade-up" data-aos-delay="100">
                            <div class="layer-icon">
                                <i class="fas fa-file-alt"></i>
                            </div>
                            <div class="layer-content">
                                <h5>Input Processing</h5>
                                <p>Phân tích và xử lý văn bản đầu vào</p>
                            </div>
                        </div>

                        <div class="tech-arrow">
                            <i class="fas fa-arrow-down"></i>
                        </div>

                        <div class="tech-layer" data-aos="fade-up" data-aos-delay="200">
                            <div class="layer-icon">
                                <i class="fas fa-brain"></i>
                            </div>
                            <div class="layer-content">
                                <h5>NLP Analysis</h5>
                                <p>Xử lý ngôn ngữ tự nhiên với Transformer</p>
                            </div>
                        </div>

                        <div class="tech-arrow">
                            <i class="fas fa-arrow-down"></i>
                        </div>

                        <div class="tech-layer" data-aos="fade-up" data-aos-delay="300">
                            <div class="layer-icon">
                                <i class="fas fa-cogs"></i>
                            </div>
                            <div class="layer-content">
                                <h5>ML Scoring</h5>
                                <p>Mô hình học máy chấm điểm 4 tiêu chí</p>
                            </div>
                        </div>

                        <div class="tech-arrow">
                            <i class="fas fa-arrow-down"></i>
                        </div>

                        <div class="tech-layer" data-aos="fade-up" data-aos-delay="400">
                            <div class="layer-icon">
                                <i class="fas fa-chart-bar"></i>
                            </div>
                            <div class="layer-content">
                                <h5>Result Output</h5>
                                <p>Kết quả chi tiết và gợi ý cải thiện</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-6" data-aos="fade-left">
                <div class="tech-features">
                    <div class="tech-feature" data-aos="fade-up" data-aos-delay="100">
                        <div class="tech-feature-icon">
                            <i class="fas fa-language"></i>
                        </div>
                        <div class="tech-feature-content">
                            <h4>Natural Language Processing</h4>
                            <p>
                                Sử dụng mô hình Transformer tiên tiến để hiểu sâu về ngữ nghĩa,
                                ngữ pháp và cấu trúc văn bản tiếng Anh.
                            </p>
                            <ul class="tech-specs">
                                <li>BERT/GPT-based models</li>
                                <li>Contextual understanding</li>
                                <li>Semantic analysis</li>
                            </ul>
                        </div>
                    </div>

                    <div class="tech-feature" data-aos="fade-up" data-aos-delay="200">
                        <div class="tech-feature-icon">
                            <i class="fas fa-database"></i>
                        </div>
                        <div class="tech-feature-content">
                            <h4>Big Data Training</h4>
                            <p>
                                Được huấn luyện trên hơn 1 triệu bài thi IELTS thực tế
                                với điểm số từ các giám khảo chính thức.
                            </p>
                            <ul class="tech-specs">
                                <li>1M+ real IELTS essays</li>
                                <li>Official examiner scores</li>
                                <li>Continuous learning</li>
                            </ul>
                        </div>
                    </div>

                    <div class="tech-feature" data-aos="fade-up" data-aos-delay="300">
                        <div class="tech-feature-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <div class="tech-feature-content">
                            <h4>Quality Assurance</h4>
                            <p>
                                Hệ thống kiểm tra chất lượng đa tầng với validation
                                từ các chuyên gia IELTS hàng đầu.
                            </p>
                            <ul class="tech-specs">
                                <li>Multi-layer validation</li>
                                <li>Expert verification</li>
                                <li>Continuous monitoring</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- TOPID.VN Business Section -->
<section id="business" class="business-section">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6" data-aos="fade-right">
                <div class="business-content">
                    <div class="brand-badge">
                        <i class="fas fa-crown"></i>
                        <span>TOPID.VN</span>
                    </div>
                    <h2 class="business-title">
                        Nền tảng chấm IELTS AI cho
                        <span class="highlight">Trung tâm Anh ngữ</span>
                    </h2>
                    <p class="business-subtitle">
                        Tăng cường thương hiệu và thu hút học viên với công nghệ chấm thi IELTS Writing tự động,
                        chính xác và chuyên nghiệp từ TOPID.VN
                    </p>

                    <div class="benefits-grid">
                        <div class="benefit-item" data-aos="fade-up" data-aos-delay="100">
                            <div class="benefit-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="benefit-content">
                                <h5>Thu hút học viên mới</h5>
                                <p>Cung cấp dịch vụ chấm thi miễn phí để thu hút và chuyển đổi khách hàng tiềm năng thành học viên chính thức</p>
                            </div>
                        </div>

                        <div class="benefit-item" data-aos="fade-up" data-aos-delay="200">
                            <div class="benefit-icon">
                                <i class="fas fa-award"></i>
                            </div>
                            <div class="benefit-content">
                                <h5>Nâng cao thương hiệu</h5>
                                <p>Khẳng định vị thế tiên phong với công nghệ AI hiện đại, tạo ấn tượng mạnh với học viên và phụ huynh</p>
                            </div>
                        </div>

                        <div class="benefit-item" data-aos="fade-up" data-aos-delay="300">
                            <div class="benefit-icon">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div class="benefit-content">
                                <h5>Tăng tỷ lệ chuyển đổi</h5>
                                <p>Học viên trải nghiệm dịch vụ chất lượng cao sẽ tin tưởng và đăng ký khóa học tại trung tâm</p>
                            </div>
                        </div>

                        <div class="benefit-item" data-aos="fade-up" data-aos-delay="400">
                            <div class="benefit-icon">
                                <i class="fas fa-rocket"></i>
                            </div>
                            <div class="benefit-content">
                                <h5>Tiết kiệm chi phí</h5>
                                <p>Giảm 70% chi phí marketing, tự động hóa quy trình tư vấn và chăm sóc học viên tiềm năng</p>
                            </div>
                        </div>

                        <div class="benefit-item" data-aos="fade-up" data-aos-delay="500">
                            <div class="benefit-icon">
                                <i class="fas fa-bullhorn"></i>
                            </div>
                            <div class="benefit-content">
                                <h5>Marketing tự động</h5>
                                <p>Hệ thống tự động thu thập thông tin, phân loại và nurture leads thành học viên chất lượng</p>
                            </div>
                        </div>

                        <div class="benefit-item" data-aos="fade-up" data-aos-delay="600">
                            <div class="benefit-icon">
                                <i class="fas fa-handshake"></i>
                            </div>
                            <div class="benefit-content">
                                <h5>Tăng độ tin cậy</h5>
                                <p>Cung cấp đánh giá khách quan, minh bạch giúp học viên tin tưởng vào chất lượng giảng dạy</p>
                            </div>
                        </div>
                    </div>

                    <div class="business-actions" data-aos="fade-up" data-aos-delay="700">
                        <a href="mailto:<EMAIL>" class="btn-business btn-primary-business">
                            <i class="fas fa-handshake"></i>
                            Hợp tác ngay
                        </a>
                        <a href="tel:+***********" class="btn-business btn-outline-business">
                            <i class="fas fa-phone"></i>
                            Tư vấn miễn phí
                        </a>
                    </div>
                </div>
            </div>

            <div class="col-lg-6" data-aos="fade-left">
                <div class="business-visual">
                    <div class="stats-showcase">
                        <div class="stat-item" data-aos="zoom-in" data-aos-delay="100">
                            <div class="stat-number">95%</div>
                            <div class="stat-label">Độ chính xác</div>
                            <div class="stat-desc">So với giáo viên chuyên nghiệp</div>
                        </div>
                        <div class="stat-item" data-aos="zoom-in" data-aos-delay="200">
                            <div class="stat-number">24/7</div>
                            <div class="stat-label">Sẵn sàng phục vụ</div>
                            <div class="stat-desc">Chấm thi mọi lúc, mọi nơi</div>
                        </div>
                        <div class="stat-item" data-aos="zoom-in" data-aos-delay="300">
                            <div class="stat-number">1000+</div>
                            <div class="stat-label">Học viên/tháng</div>
                            <div class="stat-desc">Tiềm năng thu hút</div>
                        </div>
                    </div>

                    <div class="features-showcase">
                        <div class="feature-highlight" data-aos="slide-left" data-aos-delay="400">
                            <i class="fas fa-brain"></i>
                            <div>
                                <strong>AI Technology</strong>
                                <span>Công nghệ AI tiên tiến nhất</span>
                            </div>
                        </div>
                        <div class="feature-highlight" data-aos="slide-left" data-aos-delay="500">
                            <i class="fas fa-mobile-alt"></i>
                            <div>
                                <strong>Mobile Friendly</strong>
                                <span>Tối ưu cho mọi thiết bị</span>
                            </div>
                        </div>
                        <div class="feature-highlight" data-aos="slide-left" data-aos-delay="600">
                            <i class="fas fa-shield-alt"></i>
                            <div>
                                <strong>Secure & Reliable</strong>
                                <span>Bảo mật và ổn định cao</span>
                            </div>
                        </div>
                        <div class="feature-highlight" data-aos="slide-left" data-aos-delay="700">
                            <i class="fas fa-chart-bar"></i>
                            <div>
                                <strong>Analytics Dashboard</strong>
                                <span>Báo cáo chi tiết, thống kê</span>
                            </div>
                        </div>
                    </div>

                    <div class="roi-calculator" data-aos="fade-up" data-aos-delay="800">
                        <h4><i class="fas fa-calculator"></i> Tính toán ROI</h4>
                        <div class="roi-item">
                            <span>Chi phí marketing hiện tại:</span>
                            <strong>50 triệu/tháng</strong>
                        </div>
                        <div class="roi-item">
                            <span>Tiết kiệm với TOPID.VN:</span>
                            <strong class="text-success">5 triệu</strong>
                        </div>
                        <div class="roi-item">
                            <span>Tăng học viên mới:</span>
                            <strong class="text-primary">+300%</strong>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- IELTS Speaking AI Section -->
<section id="speaking-ai" class="speaking-section">
    <div class="container">
        <div class="row text-center mb-5">
            <div class="col-12" data-aos="fade-up">
                <div class="section-badge">
                    <i class="fas fa-microphone"></i>
                    <span>Coming Soon</span>
                </div>
                <h2 class="section-title">
                    Chấm điểm <span class="text-gradient">IELTS Speaking</span>
                    với AI tiên tiến
                </h2>
                <p class="section-subtitle">
                    Sắp ra mắt hệ thống chấm điểm IELTS Speaking tự động đầu tiên tại Việt Nam.
                    Phân tích giọng nói, phát âm, ngữ pháp và từ vựng một cách chính xác như giám khảo thực thụ.
                </p>
            </div>
        </div>

        <div class="row align-items-center">
            <div class="col-lg-6" data-aos="fade-right">
                <div class="speaking-visual">
                    <div class="speaking-demo">
                        <div class="demo-header">
                            <div class="demo-controls">
                                <div class="control-btn record-btn">
                                    <i class="fas fa-microphone"></i>
                                </div>
                                <div class="control-btn play-btn">
                                    <i class="fas fa-play"></i>
                                </div>
                                <div class="control-btn stop-btn">
                                    <i class="fas fa-stop"></i>
                                </div>
                            </div>
                            <div class="demo-title">IELTS Speaking Test</div>
                        </div>

                        <div class="demo-content">
                            <div class="question-display">
                                <h4>Part 2: Describe a memorable journey</h4>
                                <p>You should say:</p>
                                <ul>
                                    <li>Where you went</li>
                                    <li>Who you went with</li>
                                    <li>What you did there</li>
                                    <li>And explain why it was memorable</li>
                                </ul>
                            </div>

                            <div class="audio-visualizer">
                                <div class="wave-bar" style="height: 20px;"></div>
                                <div class="wave-bar" style="height: 35px;"></div>
                                <div class="wave-bar" style="height: 50px;"></div>
                                <div class="wave-bar" style="height: 30px;"></div>
                                <div class="wave-bar" style="height: 45px;"></div>
                                <div class="wave-bar" style="height: 25px;"></div>
                                <div class="wave-bar" style="height: 40px;"></div>
                                <div class="wave-bar" style="height: 55px;"></div>
                                <div class="wave-bar" style="height: 35px;"></div>
                                <div class="wave-bar" style="height: 20px;"></div>
                            </div>

                            <div class="real-time-analysis">
                                <div class="analysis-item">
                                    <span class="analysis-label">Pronunciation:</span>
                                    <div class="analysis-bar">
                                        <div class="analysis-fill" style="width: 85%;"></div>
                                    </div>
                                    <span class="analysis-score">8.5</span>
                                </div>
                                <div class="analysis-item">
                                    <span class="analysis-label">Fluency:</span>
                                    <div class="analysis-bar">
                                        <div class="analysis-fill" style="width: 78%;"></div>
                                    </div>
                                    <span class="analysis-score">7.8</span>
                                </div>
                                <div class="analysis-item">
                                    <span class="analysis-label">Grammar:</span>
                                    <div class="analysis-bar">
                                        <div class="analysis-fill" style="width: 82%;"></div>
                                    </div>
                                    <span class="analysis-score">8.2</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-6" data-aos="fade-left">
                <div class="speaking-features">
                    <div class="feature-item" data-aos="fade-up" data-aos-delay="100">
                        <div class="feature-icon">
                            <i class="fas fa-waveform-lines"></i>
                        </div>
                        <div class="feature-content">
                            <h4>Phân tích giọng nói AI</h4>
                            <p>Công nghệ nhận diện giọng nói tiên tiến, phân tích phát âm, ngữ điệu và độ trôi chảy với độ chính xác cao</p>
                        </div>
                    </div>

                    <div class="feature-item" data-aos="fade-up" data-aos-delay="200">
                        <div class="feature-icon">
                            <i class="fas fa-brain"></i>
                        </div>
                        <div class="feature-content">
                            <h4>Đánh giá thời gian thực</h4>
                            <p>Nhận feedback tức thì về pronunciation, fluency, grammar và vocabulary trong quá trình nói</p>
                        </div>
                    </div>

                    <div class="feature-item" data-aos="fade-up" data-aos-delay="300">
                        <div class="feature-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="feature-content">
                            <h4>Báo cáo chi tiết</h4>
                            <p>Phân tích sâu từng tiêu chí chấm điểm với gợi ý cải thiện cụ thể và bài tập luyện tập</p>
                        </div>
                    </div>

                    <div class="feature-item" data-aos="fade-up" data-aos-delay="400">
                        <div class="feature-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="feature-content">
                            <h4>Mô phỏng thi thật</h4>
                            <p>Trải nghiệm thi Speaking như thật với 3 phần thi đầy đủ và thời gian chuẩn IELTS</p>
                        </div>
                    </div>
                </div>

                <div class="speaking-cta" data-aos="fade-up" data-aos-delay="500">
                    <h4>Đăng ký nhận thông báo ra mắt</h4>
                    <p>Là người đầu tiên trải nghiệm tính năng Speaking AI</p>
                    <div class="notify-form">
                        <div class="input-group">
                            <input type="email" class="form-control" placeholder="Nhập email của bạn">
                            <button class="btn btn-gradient" type="button">
                                <i class="fas fa-bell"></i>
                                Thông báo cho tôi
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

@endsection

@push('styles')
<style>
/* Hero Section Styles */
.hero-section {
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
    overflow: hidden;
    padding-top: 80px;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: -1;
}

.hero-gradient {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
        rgba(102, 126, 234, 0.1) 0%,
        rgba(118, 75, 162, 0.1) 50%,
        rgba(255, 255, 255, 0.95) 100%);
}

.hero-particles {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="particles" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(102,126,234,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23particles)"/></svg>');
    animation: float 20s ease-in-out infinite;
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
    padding: 8px 16px;
    border-radius: 50px;
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    border: 1px solid rgba(102, 126, 234, 0.2);
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 900;
    line-height: 1.2;
    margin-bottom: 1.5rem;
    color: var(--text-primary);
}

.hero-subtitle {
    font-size: 1.25rem;
    line-height: 1.6;
    color: var(--text-secondary);
    margin-bottom: 2rem;
}

.hero-stats {
    display: flex;
    gap: 2rem;
    margin-bottom: 2.5rem;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 2rem;
    font-weight: 800;
    color: #667eea;
    line-height: 1;
}

.stat-label {
    font-size: 0.9rem;
    color: var(--text-muted);
    font-weight: 500;
}

.hero-actions {
    display: flex;
    gap: 1rem;
    margin-bottom: 3rem;
    flex-wrap: wrap;
}

.hero-trust {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.trust-text {
    color: var(--text-muted);
    font-size: 0.9rem;
    font-weight: 500;
}

.trust-logos {
    display: flex;
    gap: 1.5rem;
}

.trust-logo {
    display: flex;
    align-items: center;
    gap: 6px;
    color: var(--text-muted);
    font-size: 0.85rem;
}

/* Hero Visual */
.hero-visual {
    position: relative;
    padding: 2rem;
}

.hero-dashboard {
    background: white;
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-xl);
    overflow: hidden;
    position: relative;
    z-index: 2;
}

.dashboard-header {
    background: var(--primary-gradient);
    color: white;
    padding: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.dashboard-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 700;
}

.dashboard-status {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 0.9rem;
}

.status-dot {
    width: 8px;
    height: 8px;
    background: #4ade80;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.dashboard-content {
    padding: 2rem;
}

.overall-score {
    text-align: center;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-radius: var(--border-radius);
}

.score-number {
    font-size: 3rem;
    font-weight: 900;
    color: #667eea;
    line-height: 1;
}

.score-label {
    color: var(--text-muted);
    font-weight: 600;
    margin-top: 0.5rem;
}

.criteria-scores {
    margin-bottom: 2rem;
}

.criteria-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.criteria-name {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.9rem;
}

.criteria-score {
    display: flex;
    align-items: center;
    gap: 12px;
}

.score-bar {
    width: 100px;
    height: 6px;
    background: #e2e8f0;
    border-radius: 3px;
    overflow: hidden;
}

.score-fill {
    height: 100%;
    background: var(--primary-gradient);
    border-radius: 3px;
    transition: width 2s ease;
}

.score-value {
    font-weight: 700;
    color: #667eea;
    font-size: 0.9rem;
    min-width: 30px;
}

.ai-insights {
    background: #f8fafc;
    border-radius: var(--border-radius);
    padding: 1.5rem;
}

.insight-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.insight-item {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 8px;
    font-size: 0.9rem;
    color: var(--text-secondary);
}

/* Floating Elements */
.floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    z-index: 1;
}

.floating-card {
    position: absolute;
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: #667eea;
    box-shadow: var(--shadow-lg);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.card-1 {
    top: 10%;
    right: 10%;
    animation: float1 6s ease-in-out infinite;
}

.card-2 {
    top: 60%;
    left: 5%;
    animation: float2 8s ease-in-out infinite;
}

.card-3 {
    bottom: 20%;
    right: 20%;
    animation: float3 7s ease-in-out infinite;
}

/* Feature Cards */
.feature-card {
    background: white;
    border-radius: var(--border-radius-lg);
    padding: 2.5rem 2rem;
    text-align: center;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
    transition: var(--transition);
    height: 100%;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.feature-icon {
    width: 80px;
    height: 80px;
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
    margin: 0 auto 1.5rem;
}

.feature-title {
    font-size: 1.3rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.feature-description {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.feature-stats {
    margin-top: auto;
}

.stat-highlight {
    background: var(--primary-gradient);
    color: white;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
}

/* Section Header */
.section-header {
    margin-bottom: 4rem;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--text-primary);
    margin-bottom: 1rem;
    line-height: 1.3;
}

.section-subtitle {
    font-size: 1.1rem;
    color: var(--text-secondary);
    line-height: 1.6;
    max-width: 600px;
    margin: 0 auto;
}

/* Animations */
@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

@keyframes float1 {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-15px) rotate(5deg); }
}

@keyframes float2 {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(-5deg); }
}

@keyframes float3 {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-10px) rotate(3deg); }
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Responsive */
@media (max-width: 992px) {
    .hero-title {
        font-size: 2.5rem;
    }

    .hero-stats {
        gap: 1.5rem;
    }

    .hero-visual {
        margin-top: 3rem;
        padding: 1rem;
    }

    .section-title {
        font-size: 2rem;
    }
}

@media (max-width: 768px) {
    .hero-title {
        font-size: 2rem;
    }

    .hero-subtitle {
        font-size: 1.1rem;
    }

    .hero-stats {
        gap: 1rem;
        justify-content: center;
    }

    .hero-actions {
        justify-content: center;
    }

    .trust-logos {
        gap: 1rem;
    }

    .floating-card {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }
}

@media (max-width: 576px) {
    .hero-title {
        font-size: 1.75rem;
    }

    .stat-number {
        font-size: 1.5rem;
    }

    .hero-actions {
        flex-direction: column;
        align-items: center;
    }

    .hero-actions .btn {
        width: 100%;
        max-width: 300px;
    }

    .dashboard-content {
        padding: 1.5rem;
    }

    .overall-score {
        padding: 1rem;
    }

    .score-number {
        font-size: 2.5rem;
    }
}

/* Comparison Table Styles */
.comparison-table {
    background: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
    border: 1px solid var(--border-color);
}

.comparison-table .table {
    margin: 0;
    font-size: 0.95rem;
}

.comparison-table thead th {
    background: var(--bg-light);
    border: none;
    padding: 1.5rem 1rem;
    font-weight: 700;
    color: var(--text-primary);
    text-align: center;
    vertical-align: middle;
}

.comparison-table thead th:first-child {
    text-align: left;
    background: white;
}

.column-header {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    font-size: 1rem;
}

.ai-column {
    background: rgba(102, 126, 234, 0.05);
    border-left: 3px solid #667eea;
}

.traditional-column {
    background: rgba(255, 107, 107, 0.05);
    border-left: 3px solid #ff6b6b;
}

.comparison-table tbody td {
    padding: 1.25rem 1rem;
    border-top: 1px solid var(--border-color);
    vertical-align: middle;
}

.criteria {
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 10px;
}

.criteria i {
    color: var(--text-muted);
    width: 16px;
}

.highlight {
    padding: 6px 12px;
    border-radius: 20px;
    font-weight: 700;
    font-size: 0.9rem;
    display: inline-block;
    margin-bottom: 4px;
}

.highlight.success {
    background: #d4edda;
    color: #155724;
}

.highlight.warning {
    background: #fff3cd;
    color: #856404;
}

.highlight.info {
    background: #d1ecf1;
    color: #0c5460;
}

.comparison-table small {
    display: block;
    color: var(--text-muted);
    font-size: 0.8rem;
    margin-top: 4px;
}

/* Technology Section Styles */
.tech-visual {
    padding: 2rem;
}

.tech-diagram {
    background: white;
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);
}

.tech-layer {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
    background: var(--bg-light);
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
    transition: var(--transition);
}

.tech-layer:hover {
    transform: translateX(10px);
    box-shadow: var(--shadow-md);
}

.layer-icon {
    width: 60px;
    height: 60px;
    background: var(--primary-gradient);
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    flex-shrink: 0;
}

.layer-content h5 {
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.layer-content p {
    color: var(--text-secondary);
    margin: 0;
    font-size: 0.9rem;
}

.tech-arrow {
    text-align: center;
    color: var(--text-muted);
    font-size: 1.5rem;
    margin: 0.5rem 0;
    animation: bounce 2s infinite;
}

.tech-features {
    padding: 1rem 0;
}

.tech-feature {
    display: flex;
    gap: 1.5rem;
    margin-bottom: 3rem;
    padding: 2rem;
    background: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
    transition: var(--transition);
}

.tech-feature:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.tech-feature-icon {
    width: 80px;
    height: 80px;
    background: var(--primary-gradient);
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
    flex-shrink: 0;
}

.tech-feature-content h4 {
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 1rem;
    font-size: 1.2rem;
}

.tech-feature-content p {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 1rem;
}

.tech-specs {
    list-style: none;
    padding: 0;
    margin: 0;
}

.tech-specs li {
    padding: 0.25rem 0;
    color: var(--text-muted);
    font-size: 0.9rem;
    position: relative;
    padding-left: 1.5rem;
}

.tech-specs li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: #4ade80;
    font-weight: bold;
}

/* Additional Animations */
@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

/* Responsive for new sections */
@media (max-width: 992px) {
    .comparison-table {
        font-size: 0.85rem;
    }

    .comparison-table thead th,
    .comparison-table tbody td {
        padding: 1rem 0.75rem;
    }

    .tech-feature {
        flex-direction: column;
        text-align: center;
    }

    .tech-feature-icon {
        margin: 0 auto;
    }
}

@media (max-width: 768px) {
    .comparison-table thead th,
    .comparison-table tbody td {
        padding: 0.75rem 0.5rem;
    }

    .criteria {
        flex-direction: column;
        gap: 5px;
        text-align: center;
    }

    .tech-layer {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .tech-visual {
        padding: 1rem;
    }

    .tech-diagram {
        padding: 1.5rem;
    }
}

@media (max-width: 576px) {
    .comparison-table {
        font-size: 0.8rem;
    }

    .highlight {
        font-size: 0.8rem;
        padding: 4px 8px;
    }

    .column-header {
        flex-direction: column;
        gap: 4px;
        font-size: 0.9rem;
    }

    .tech-feature {
        padding: 1.5rem;
        margin-bottom: 2rem;
    }

    .tech-feature-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }
}

/* TOPID.VN Business Section */
.business-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    position: relative;
    overflow: hidden;
    padding: 100px 0;
}

.business-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="80" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="1.5" fill="rgba(255,255,255,0.08)"/><circle cx="90" cy="40" r="1" fill="rgba(255,255,255,0.06)"/></svg>');
    opacity: 0.4;
    animation: float 20s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

.business-content {
    position: relative;
    z-index: 2;
}

.brand-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.75rem;
    background: rgba(255, 255, 255, 0.15);
    padding: 0.75rem 1.5rem;
    border-radius: 50px;
    font-weight: 700;
    margin-bottom: 2rem;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
}

.brand-badge i {
    color: #ffd700;
    font-size: 1.2rem;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.business-title {
    font-size: 3rem;
    font-weight: 900;
    margin-bottom: 1.5rem;
    line-height: 1.2;
    text-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.business-title .highlight {
    color: #ffd700;
    text-shadow: 0 4px 8px rgba(255, 215, 0, 0.3);
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.business-subtitle {
    font-size: 1.3rem;
    opacity: 0.95;
    margin-bottom: 3rem;
    line-height: 1.7;
    font-weight: 400;
}

.benefits-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
    margin-bottom: 3rem;
}

.benefit-item {
    display: flex;
    gap: 1.25rem;
    background: rgba(255, 255, 255, 0.1);
    padding: 2rem;
    border-radius: 20px;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.15);
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
}

.benefit-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,255,255,0.1), transparent);
    opacity: 0;
    transition: opacity 0.4s ease;
}

.benefit-item:hover {
    transform: translateY(-8px);
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 20px 40px rgba(0,0,0,0.2);
}

.benefit-item:hover::before {
    opacity: 1;
}

.benefit-icon {
    width: 60px;
    height: 60px;
    background: rgba(255, 215, 0, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: #ffd700;
    flex-shrink: 0;
    box-shadow: 0 8px 16px rgba(255, 215, 0, 0.2);
}

.benefit-content h5 {
    font-weight: 700;
    margin-bottom: 0.75rem;
    color: #ffd700;
    font-size: 1.1rem;
}

.benefit-content p {
    margin: 0;
    opacity: 0.9;
    line-height: 1.6;
    font-size: 0.95rem;
}

.business-actions {
    display: flex;
    gap: 1.5rem;
    flex-wrap: wrap;
}

.btn-business {
    padding: 1rem 2.5rem;
    border-radius: 15px;
    font-weight: 700;
    display: inline-flex;
    align-items: center;
    gap: 0.75rem;
    transition: all 0.4s ease;
    text-decoration: none;
    border: none;
    font-size: 1rem;
    position: relative;
    overflow: hidden;
}

.btn-business::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s ease;
}

.btn-business:hover::before {
    left: 100%;
}

.btn-primary-business {
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    color: #333;
    box-shadow: 0 8px 25px rgba(255, 215, 0, 0.3);
}

.btn-primary-business:hover {
    background: linear-gradient(45deg, #ffed4e, #fff59d);
    color: #333;
    text-decoration: none;
    transform: translateY(-3px);
    box-shadow: 0 15px 35px rgba(255, 215, 0, 0.4);
}

.btn-outline-business {
    background: transparent;
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
}

.btn-outline-business:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    text-decoration: none;
    transform: translateY(-3px);
    border-color: rgba(255, 255, 255, 0.5);
    box-shadow: 0 10px 25px rgba(255, 255, 255, 0.1);
}

.business-visual {
    position: relative;
    z-index: 2;
}

.stats-showcase {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
    margin-bottom: 2.5rem;
}

.stat-item {
    background: rgba(255, 255, 255, 0.1);
    padding: 2rem 1.5rem;
    border-radius: 20px;
    text-align: center;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.15);
    transition: all 0.4s ease;
}

.stat-item:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 15px 30px rgba(0,0,0,0.2);
}

.stat-number {
    display: block;
    font-size: 2.5rem;
    font-weight: 900;
    color: #ffd700;
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.stat-label {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.stat-desc {
    font-size: 0.85rem;
    opacity: 0.8;
}

.features-showcase {
    display: flex;
    flex-direction: column;
    gap: 1.25rem;
    margin-bottom: 2.5rem;
}

.feature-highlight {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    background: rgba(255, 255, 255, 0.1);
    padding: 1.5rem 2rem;
    border-radius: 15px;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.15);
    transition: all 0.4s ease;
}

.feature-highlight:hover {
    transform: translateX(10px);
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.feature-highlight i {
    color: #ffd700;
    font-size: 1.5rem;
    width: 40px;
    text-align: center;
}

.feature-highlight div strong {
    display: block;
    font-weight: 700;
    margin-bottom: 0.25rem;
    color: #ffd700;
}

.feature-highlight div span {
    font-size: 0.9rem;
    opacity: 0.9;
}

.roi-calculator {
    background: rgba(255, 255, 255, 0.1);
    padding: 2rem;
    border-radius: 20px;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.15);
}

.roi-calculator h4 {
    color: #ffd700;
    margin-bottom: 1.5rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.roi-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.roi-item:last-child {
    border-bottom: none;
}

.roi-item span {
    opacity: 0.9;
}

.roi-item strong {
    font-weight: 700;
}

.text-success {
    color: #4ade80 !important;
}

.text-primary {
    color: #60a5fa !important;
}

/* Responsive Design */
@media (max-width: 992px) {
    .business-title {
        font-size: 2.5rem;
    }

    .benefits-grid {
        grid-template-columns: 1fr;
    }

    .stats-showcase {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .business-section {
        padding: 60px 0;
    }

    .business-title {
        font-size: 2rem;
    }

    .business-subtitle {
        font-size: 1.1rem;
    }

    .business-actions {
        flex-direction: column;
    }

    .btn-business {
        justify-content: center;
        width: 100%;
    }

    .benefit-item {
        flex-direction: column;
        text-align: center;
        padding: 1.5rem;
    }

    .feature-highlight {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .roi-item {
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
    }
}

/* IELTS Speaking AI Section */
.speaking-section {
    background: linear-gradient(135deg, #0f172a 0%, #1e293b 25%, #312e81 50%, #581c87 75%, #7c2d12 100%);
    color: white;
    padding: 100px 0;
    position: relative;
    overflow: hidden;
}

.speaking-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 80%, rgba(251, 191, 36, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 50% 50%, rgba(139, 92, 246, 0.05) 0%, transparent 70%);
    pointer-events: none;
}

.speaking-section::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.2);
    pointer-events: none;
}

.section-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.75rem;
    background: linear-gradient(45deg, rgba(251, 191, 36, 0.2), rgba(245, 158, 11, 0.2));
    padding: 1rem 2rem;
    border-radius: 50px;
    font-weight: 700;
    font-size: 1.1rem;
    margin-bottom: 2rem;
    backdrop-filter: blur(20px);
    border: 2px solid rgba(251, 191, 36, 0.3);
    box-shadow: 0 8px 32px rgba(251, 191, 36, 0.2);
    text-transform: uppercase;
    letter-spacing: 1px;
    animation: glow 2s ease-in-out infinite alternate;
}

.section-badge i {
    color: #fbbf24;
    font-size: 1.2rem;
    text-shadow: 0 0 10px rgba(251, 191, 36, 0.5);
}

@keyframes glow {
    from {
        box-shadow: 0 8px 32px rgba(251, 191, 36, 0.2);
    }
    to {
        box-shadow: 0 8px 32px rgba(251, 191, 36, 0.4);
    }
}

.speaking-section .section-title {
    font-size: 3.5rem;
    font-weight: 900;
    margin-bottom: 1.5rem;
    line-height: 1.2;
    text-shadow: 0 4px 8px rgba(0,0,0,0.3);
    color: white;
}

.speaking-section .section-subtitle {
    font-size: 1.4rem;
    font-weight: 400;
    line-height: 1.7;
    color: rgba(255, 255, 255, 0.95);
    text-shadow: 0 2px 4px rgba(0,0,0,0.2);
    max-width: 800px;
    margin: 0 auto 3rem;
}

.text-gradient {
    background: linear-gradient(45deg, #fbbf24, #f59e0b);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: none;
    font-weight: 900;
    position: relative;
}

.text-gradient::after {
    content: attr(data-text);
    position: absolute;
    top: 0;
    left: 0;
    background: linear-gradient(45deg, #fbbf24, #f59e0b);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 0 20px rgba(251, 191, 36, 0.5);
    z-index: -1;
}

.speaking-visual {
    position: relative;
    z-index: 2;
}

.speaking-demo {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 2rem;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.15);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.demo-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.demo-controls {
    display: flex;
    gap: 1rem;
}

.control-btn {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.record-btn {
    background: linear-gradient(45deg, #ef4444, #dc2626);
    animation: pulse 2s infinite;
}

.play-btn {
    background: linear-gradient(45deg, #10b981, #059669);
}

.stop-btn {
    background: linear-gradient(45deg, #6b7280, #4b5563);
}

.control-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(239, 68, 68, 0); }
    100% { box-shadow: 0 0 0 0 rgba(239, 68, 68, 0); }
}

.demo-title {
    font-weight: 700;
    font-size: 1.2rem;
    color: #fbbf24;
}

.demo-content {
    space-y: 2rem;
}

.question-display {
    background: rgba(255, 255, 255, 0.05);
    padding: 1.5rem;
    border-radius: 15px;
    margin-bottom: 2rem;
}

.question-display h4 {
    color: #fbbf24;
    margin-bottom: 1rem;
    font-weight: 700;
}

.question-display ul {
    margin: 1rem 0 0 1.5rem;
    opacity: 0.9;
}

.question-display li {
    margin-bottom: 0.5rem;
}

.audio-visualizer {
    display: flex;
    align-items: end;
    justify-content: center;
    gap: 4px;
    height: 60px;
    margin: 2rem 0;
}

.wave-bar {
    width: 6px;
    background: linear-gradient(to top, #fbbf24, #f59e0b);
    border-radius: 3px;
    animation: wave 1.5s ease-in-out infinite;
    opacity: 0.8;
}

.wave-bar:nth-child(2) { animation-delay: 0.1s; }
.wave-bar:nth-child(3) { animation-delay: 0.2s; }
.wave-bar:nth-child(4) { animation-delay: 0.3s; }
.wave-bar:nth-child(5) { animation-delay: 0.4s; }
.wave-bar:nth-child(6) { animation-delay: 0.5s; }
.wave-bar:nth-child(7) { animation-delay: 0.6s; }
.wave-bar:nth-child(8) { animation-delay: 0.7s; }
.wave-bar:nth-child(9) { animation-delay: 0.8s; }
.wave-bar:nth-child(10) { animation-delay: 0.9s; }

@keyframes wave {
    0%, 100% { transform: scaleY(0.5); }
    50% { transform: scaleY(1); }
}

.real-time-analysis {
    space-y: 1rem;
}

.analysis-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.analysis-label {
    min-width: 100px;
    font-weight: 600;
    font-size: 0.9rem;
}

.analysis-bar {
    flex: 1;
    height: 8px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    overflow: hidden;
}

.analysis-fill {
    height: 100%;
    background: linear-gradient(90deg, #10b981, #fbbf24);
    border-radius: 4px;
    transition: width 2s ease;
}

.analysis-score {
    min-width: 40px;
    font-weight: 700;
    color: #fbbf24;
}

.speaking-features {
    position: relative;
    z-index: 2;
}

.feature-item {
    display: flex;
    gap: 1.5rem;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.feature-item:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.1);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.feature-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(45deg, #fbbf24, #f59e0b);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    flex-shrink: 0;
    box-shadow: 0 8px 16px rgba(251, 191, 36, 0.3);
}

.feature-content h4 {
    font-weight: 700;
    margin-bottom: 0.75rem;
    color: #fbbf24;
}

.feature-content p {
    opacity: 0.9;
    line-height: 1.6;
    margin: 0;
}

.speaking-cta {
    background: rgba(255, 255, 255, 0.1);
    padding: 2rem;
    border-radius: 20px;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.15);
    text-align: center;
    margin-top: 2rem;
}

.speaking-cta h4 {
    color: #fbbf24;
    margin-bottom: 0.75rem;
    font-weight: 700;
}

.speaking-cta p {
    opacity: 0.9;
    margin-bottom: 1.5rem;
}

.notify-form .input-group {
    display: flex;
    gap: 0;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.notify-form .form-control {
    border: none;
    padding: 1rem 1.5rem;
    background: rgba(255, 255, 255, 0.9);
    color: #333;
    font-weight: 500;
}

.notify-form .form-control:focus {
    box-shadow: none;
    background: white;
}

.notify-form .btn-gradient {
    background: linear-gradient(45deg, #fbbf24, #f59e0b);
    border: none;
    padding: 1rem 2rem;
    color: white;
    font-weight: 700;
    transition: all 0.3s ease;
}

.notify-form .btn-gradient:hover {
    background: linear-gradient(45deg, #f59e0b, #d97706);
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(251, 191, 36, 0.4);
}

/* Responsive Design for Speaking Section */
@media (max-width: 992px) {
    .speaking-section {
        padding: 60px 0;
    }

    .speaking-section .section-title {
        font-size: 2.8rem;
    }

    .speaking-section .section-subtitle {
        font-size: 1.2rem;
    }

    .demo-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .demo-controls {
        justify-content: center;
    }
}

@media (max-width: 768px) {
    .speaking-section .section-title {
        font-size: 2.2rem;
    }

    .speaking-section .section-subtitle {
        font-size: 1.1rem;
        padding: 0 1rem;
    }

    .section-badge {
        font-size: 0.9rem;
        padding: 0.75rem 1.5rem;
    }

    .control-btn {
        width: 40px;
        height: 40px;
    }

    .feature-item {
        flex-direction: column;
        text-align: center;
    }

    .analysis-item {
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
    }

    .analysis-bar {
        width: 100%;
    }

    .notify-form .input-group {
        flex-direction: column;
        border-radius: 12px;
    }

    .notify-form .form-control,
    .notify-form .btn-gradient {
        border-radius: 12px;
    }
}
</style>
@endpush
