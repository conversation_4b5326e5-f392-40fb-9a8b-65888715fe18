<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\ScoringController;
use App\Http\Controllers\Admin\AdminController;
use App\Http\Controllers\Admin\UserController;
use App\Http\Controllers\LandingController;
use App\Http\Controllers\ProfileController;

// Public routes
Route::get('/', [LandingController::class, 'index'])->name('home');

// Authentication routes
Auth::routes();

// Protected routes
Route::middleware('auth')->group(function () {
    Route::get('/dashboard', [HomeController::class, 'index'])->name('dashboard');
    Route::get('/home', [HomeController::class, 'index'])->name('home.dashboard');

    // Scoring routes
    Route::prefix('scoring')->name('scoring.')->group(function () {
        Route::get('/create', [ScoringController::class, 'create'])->name('create');
        Route::post('/store', [ScoringController::class, 'store'])->name('store');
        Route::get('/show/{attempt}', [ScoringController::class, 'show'])->name('show');
        Route::post('/rescore/{attempt}', [ScoringController::class, 'rescore'])->name('rescore');
        Route::get('/history', [ScoringController::class, 'history'])->name('history');
        Route::delete('/delete/{attempt}', [ScoringController::class, 'destroy'])->name('destroy');
    });

    // Profile routes
    Route::prefix('profile')->name('profile.')->group(function () {
        Route::get('/', [ProfileController::class, 'show'])->name('show');
        Route::get('/edit', [ProfileController::class, 'edit'])->name('edit');
        Route::put('/update', [ProfileController::class, 'update'])->name('update');
        Route::get('/change-password', [ProfileController::class, 'changePasswordForm'])->name('change-password');
        Route::post('/change-password', [ProfileController::class, 'changePassword'])->name('change-password.update');
    });

    // Test route
    Route::get('/test-profile', function () {
        return view('test-profile');
    })->middleware('auth');
});

// Admin routes
Route::middleware(['auth', 'admin'])->prefix('admin')->name('admin.')->group(function () {
    Route::get('/', [AdminController::class, 'index'])->name('index');
    Route::resource('users', UserController::class);
    Route::post('users/{user}/add-credits', [UserController::class, 'addCredits'])->name('users.add-credits');

    // API Test route
    Route::get('/test-api', function() {
        $scorer = new \App\Services\IELTSScorer();
        $result = $scorer->testConnection();
        return response()->json($result);
    })->name('test-api');

    // Test scoring route
    Route::get('/test-scoring', function() {
        $scorer = new \App\Services\IELTSScorer();

        $testEssay = "Technology has revolutionized the way we learn and access information. In my opinion, while technology has made learning more accessible and convenient, it has also created some challenges that need to be addressed.

On the positive side, technology has democratized education. Online platforms like Khan Academy and Coursera provide free access to high-quality educational content from top universities. Students in remote areas can now access the same resources as those in major cities. Additionally, interactive learning tools and multimedia content make complex concepts easier to understand.

However, technology may also be making students overly dependent on instant information. When answers are just a Google search away, students might not develop critical thinking skills or the patience required for deep learning. Furthermore, the constant distractions from social media and entertainment can reduce focus and attention spans.

In conclusion, technology is a powerful tool that has transformed education for the better, but it should be used thoughtfully to ensure students develop both digital literacy and traditional learning skills.";

        try {
            $result = $scorer->scoreEssayComprehensive(
                $testEssay,
                'task2',
                'Some people believe that technology has made learning easier and more accessible, while others argue that it has made students lazy and less capable of deep thinking. Discuss both views and give your own opinion.',
                40
            );

            return response()->json([
                'success' => true,
                'result' => $result
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    })->name('test-scoring');
});
