<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\EssayQuestion;
use App\Models\ScoringAttempt;
use App\Services\IELTSScorer;
use App\Services\HashidService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class ScoringController extends Controller
{
    protected $scorer;

    public function __construct(IELTSScorer $scorer)
    {
        $this->scorer = $scorer;
        $this->middleware('auth');
    }

    public function create(Request $request)
    {
        $questionId = $request->get('question_id');
        $question = null;

        if ($questionId) {
            $question = EssayQuestion::findOrFail($questionId);
        }

        $questions = EssayQuestion::active()->get();

        return view('scoring.create', compact('question', 'questions'));
    }

    public function store(Request $request)
    {
        try {
            $request->validate([
                'essay_content' => 'required|string|min:50',
                'task_type' => 'required|in:task1_academic,task1_general,task2',
                'essay_question_id' => 'nullable|exists:essay_questions,id',
                'custom_question' => 'nullable|string',
                'time_limit' => 'integer|min:10|max:60',
            ]);
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'error' => true,
                'message' => 'Dữ liệu không hợp lệ: ' . implode(', ', $e->validator->errors()->all())
            ], 422);
        }

        $user = Auth::user();

        // Check if user has enough credits
        if ($user->getAvailableCredits() < 1) {
            return response()->json([
                'error' => true,
                'message' => 'Bạn không có đủ lượt chấm thi. Vui lòng liên hệ admin để nạp thêm credits.'
            ], 400);
        }

        DB::beginTransaction();

        try {
            // Create scoring attempt
            $attempt = ScoringAttempt::create([
                'user_id' => $user->id,
                'essay_question_id' => $request->essay_question_id,
                'essay_content' => $request->essay_content,
                'task_type' => $request->task_type,
                'time_limit' => $request->time_limit ?? 40,
                'word_count' => str_word_count($request->essay_content),
                'essay_length' => strlen($request->essay_content),
                'status' => 'pending',
            ]);

            // Get essay question
            $essayQuestion = $request->custom_question;
            if ($request->essay_question_id) {
                $questionModel = EssayQuestion::find($request->essay_question_id);
                $essayQuestion = $questionModel ? $questionModel->question : $request->custom_question;
            }

            // Use credits
            $user->useCredits(1);

            // Score the essay
            \Log::info('Starting essay scoring', [
                'user_id' => $user->id,
                'attempt_id' => $attempt->id,
                'task_type' => $request->task_type,
                'word_count' => str_word_count($request->essay_content)
            ]);

            try {
                $result = $this->scorer->scoreEssayComprehensive(
                    $request->essay_content,
                    $request->task_type,
                    $essayQuestion,
                    $request->time_limit ?? 40
                );
            } catch (\Exception $apiError) {
                \Log::error('API scoring failed', [
                    'user_id' => $user->id,
                    'attempt_id' => $attempt->id,
                    'api_error' => $apiError->getMessage()
                ]);

                // Update attempt with error
                $attempt->update([
                    'status' => 'failed',
                    'error_message' => 'API Error: ' . $apiError->getMessage()
                ]);

                return redirect()->route('scoring.show', $attempt->getHashid())
                    ->with('error', 'Scoring failed: ' . $apiError->getMessage());
            }

            \Log::info('Essay scoring completed', [
                'user_id' => $user->id,
                'attempt_id' => $attempt->id,
                'overall_score' => $result['overall_band_score'] ?? 'N/A'
            ]);

            // Update attempt with results
            $attempt->update([
                'overall_band_score' => $result['overall_band_score'],
                'task_achievement' => $result['criteria_scores']['task_achievement'] ?? null,
                'coherence_cohesion' => $result['criteria_scores']['coherence_cohesion'] ?? null,
                'lexical_resource' => $result['criteria_scores']['lexical_resource'] ?? null,
                'grammar_accuracy' => $result['criteria_scores']['grammar_accuracy'] ?? null,
                'detailed_feedback' => $result['detailed_feedback'] ?? null,
                'highlighted_corrections' => $result['highlighted_corrections'] ?? null,
                'criteria_analysis' => $result['criteria_analysis'] ?? null,
                'statistics' => $result['statistics'] ?? null,
                'status' => 'completed',
            ]);

            DB::commit();

            return response()->json([
                'success' => true,
                'attempt_id' => $attempt->id,
                'result' => $result,
                'redirect_url' => route('scoring.show', $attempt->getHashid())
            ]);

        } catch (\Exception $e) {
            DB::rollback();

            // Update attempt as failed
            if (isset($attempt)) {
                $attempt->update([
                    'status' => 'failed',
                    'error_message' => $e->getMessage()
                ]);
            }

            // Log the error for debugging
            \Log::error('Scoring failed', [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // Return user-friendly error message
            $errorMessage = 'Có lỗi xảy ra khi chấm bài. Vui lòng thử lại sau.';

            // Check for specific API errors
            if (strpos($e->getMessage(), 'rate limit') !== false) {
                $errorMessage = 'Hệ thống đang quá tải. Vui lòng thử lại sau vài phút.';
            } elseif (strpos($e->getMessage(), 'Network Error') !== false) {
                $errorMessage = 'Lỗi kết nối mạng. Vui lòng kiểm tra kết nối internet và thử lại.';
            } elseif (strpos($e->getMessage(), 'API Error') !== false) {
                $errorMessage = 'Lỗi từ hệ thống AI. Vui lòng thử lại sau.';
            }

            return response()->json([
                'error' => true,
                'message' => $errorMessage
            ], 500);
        }
    }

    public function show($attempt)
    {
        $isPublicView = false;

        // Check if it's a hashid or numeric ID
        if (is_numeric($attempt)) {
            // It's a numeric ID (old format or direct access)
            $attempt = ScoringAttempt::findOrFail($attempt);

            // Check authentication and ownership for numeric IDs
            if (!Auth::check()) {
                abort(403, 'Please login to view this result');
            }

            if ($attempt->user_id !== Auth::id() && !Auth::user()->isAdmin()) {
                abort(403, 'You do not have permission to view this result');
            }
        } else {
            // It's a hashid, decode it
            $attemptId = HashidService::decodeAttempt($attempt);

            if (!$attemptId) {
                abort(404, 'Invalid share link');
            }

            $attempt = ScoringAttempt::with(['user', 'essayQuestion'])->find($attemptId);

            if (!$attempt) {
                abort(404, 'Scoring result not found');
            }

            if (!$attempt->canBeShared()) {
                abort(404, 'This scoring result cannot be shared');
            }

            // This is a public view via hashid
            $isPublicView = true;
        }

        return view('scoring.show', compact('attempt', 'isPublicView'));
    }



    public function history()
    {
        $attempts = ScoringAttempt::where('user_id', Auth::id())
            ->with('essayQuestion')
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return view('scoring.history', compact('attempts'));
    }

    /**
     * Delete a scoring attempt
     */
    public function destroy(ScoringAttempt $attempt)
    {
        // Check if user owns this attempt
        if ($attempt->user_id !== auth()->id()) {
            abort(403, 'Unauthorized action.');
        }

        try {
            $attempt->delete();

            return redirect()->route('scoring.history')
                ->with('success', 'Bài thi đã được xóa thành công!');
        } catch (\Exception $e) {
            return redirect()->route('scoring.history')
                ->with('error', 'Có lỗi xảy ra khi xóa bài thi. Vui lòng thử lại!');
        }
    }

    public function rescore(Request $request, $hashid)
    {
        try {
            // Decode hashid to get attempt
            $hashidService = new HashidService();
            $attemptId = $hashidService->decode($hashid);

            if (!$attemptId) {
                return response()->json([
                    'success' => false,
                    'message' => 'Không tìm thấy bài thi.'
                ], 404);
            }

            $attempt = ScoringAttempt::findOrFail($attemptId);
            $user = Auth::user();

            // Check if user owns this attempt
            if ($attempt->user_id !== $user->id) {
                return response()->json([
                    'success' => false,
                    'message' => 'Bạn không có quyền chấm lại bài thi này.'
                ], 403);
            }

            // Check if attempt is completed
            if (!$attempt->isCompleted()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Chỉ có thể chấm lại những bài thi đã hoàn thành.'
                ], 400);
            }

            // Check if user has enough credits
            if ($user->getAvailableCredits() < 1) {
                return response()->json([
                    'success' => false,
                    'message' => 'Bạn không có đủ lượt chấm thi. Vui lòng liên hệ admin để nạp thêm credits.'
                ], 400);
            }

            DB::beginTransaction();

            try {
                // Use credits
                $user->useCredits(1);

                // Get essay question
                $essayQuestion = $attempt->essayQuestion ? $attempt->essayQuestion->question : 'Custom question';

                // Score the essay again
                \Log::info('Starting essay rescoring', [
                    'user_id' => $user->id,
                    'attempt_id' => $attempt->id,
                    'task_type' => $attempt->task_type,
                    'word_count' => $attempt->word_count
                ]);

                $result = $this->scorer->scoreEssayComprehensive(
                    $attempt->essay_content,
                    $attempt->task_type,
                    $essayQuestion,
                    $attempt->time_limit ?? 40
                );

                \Log::info('Essay rescoring completed', [
                    'user_id' => $user->id,
                    'attempt_id' => $attempt->id,
                    'old_score' => $attempt->overall_band_score,
                    'new_score' => $result['overall_band_score'] ?? 'N/A'
                ]);

                // Update attempt with new results
                $attempt->update([
                    'overall_band_score' => $result['overall_band_score'],
                    'task_achievement' => $result['criteria_scores']['task_achievement'] ?? null,
                    'coherence_cohesion' => $result['criteria_scores']['coherence_cohesion'] ?? null,
                    'lexical_resource' => $result['criteria_scores']['lexical_resource'] ?? null,
                    'grammar_accuracy' => $result['criteria_scores']['grammar_accuracy'] ?? null,
                    'detailed_feedback' => $result['detailed_feedback'] ?? null,
                    'highlighted_corrections' => $result['highlighted_corrections'] ?? null,
                    'criteria_analysis' => $result['criteria_analysis'] ?? null,
                    'statistics' => $result['statistics'] ?? null,
                    'rescored_at' => now(),
                ]);

                DB::commit();

                return response()->json([
                    'success' => true,
                    'message' => 'Chấm lại điểm thành công!',
                    'result' => $result
                ]);

            } catch (\Exception $apiError) {
                DB::rollback();

                \Log::error('API rescoring failed', [
                    'user_id' => $user->id,
                    'attempt_id' => $attempt->id,
                    'api_error' => $apiError->getMessage()
                ]);

                return response()->json([
                    'success' => false,
                    'message' => 'Lỗi API: ' . $apiError->getMessage()
                ], 500);
            }

        } catch (\Exception $e) {
            DB::rollback();

            \Log::error('Rescoring failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra: ' . $e->getMessage()
            ], 500);
        }
    }
}
