<?php

namespace App\Services;

use Exception;

/**
 * IELTS Writing Scorer Class
 * Provides comprehensive scoring for IELTS Writing tasks using OpenAI API
 */
class IELTSScorer {

    private $apiUrl;
    private $apiKey;
    private $model;

    public function __construct() {
        $this->apiUrl = config('services.openai.api_url');
        $this->apiKey = config('services.openai.api_key');
        $this->model = config('services.openai.model');

        // Log configuration for debugging
        \Log::info('IELTSScorer initialized', [
            'api_url' => $this->apiUrl,
            'model' => $this->model,
            'api_key_length' => strlen($this->apiKey ?? '')
        ]);
    }

    /**
     * Test API connection
     */
    public function testConnection() {
        try {
            $testPrompt = "Test message. Please respond with 'API connection successful'.";

            $data = [
                'model' => $this->model,
                'messages' => [
                    [
                        'role' => 'user',
                        'content' => $testPrompt
                    ]
                ],
                'max_tokens' => 50,
                'temperature' => 0.1
            ];

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $this->apiUrl);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Authorization: Bearer ' . $this->apiKey,
                'Content-Type: application/json'
            ]);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $curlError = curl_error($ch);
            curl_close($ch);

            if ($curlError) {
                return ['success' => false, 'error' => 'Network Error: ' . $curlError];
            }

            if ($httpCode === 200) {
                $decodedResponse = json_decode($response, true);
                if ($decodedResponse && isset($decodedResponse['choices'][0]['message']['content'])) {
                    return ['success' => true, 'response' => $decodedResponse['choices'][0]['message']['content']];
                }
            }

            return ['success' => false, 'error' => 'HTTP ' . $httpCode . ': ' . $response];

        } catch (Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * Comprehensive IELTS essay scoring with strict standards
     *
     * @param string $essay The essay text to score
     * @param string $taskType Type of task (task1_academic, task1_general, task2)
     * @param string $essayQuestion The complete essay question
     * @param int $timeLimit Time limit in minutes
     * @return array Detailed scoring results with criteria breakdown
     */
    public function scoreEssayComprehensive($essay, $taskType, $essayQuestion, $timeLimit = 40) {
        try {
            // Validate input
            if (empty($essay) || empty($taskType) || empty($essayQuestion)) {
                throw new Exception('Essay text, task type, and essay question are required');
            }

            // Generate comprehensive scoring prompt
            $scoringPrompt = $this->generateComprehensiveScoringPrompt($essay, $taskType, $essayQuestion, $timeLimit);

            // Call OpenAI API
            $response = $this->callOpenAI($scoringPrompt);

            // Parse and structure the response
            $scoringResult = $this->parseComprehensiveResponse($response);

            // Add metadata
            $scoringResult['metadata'] = [
                'task_type' => $taskType,
                'word_count' => str_word_count($essay),
                'scored_at' => date('Y-m-d H:i:s'),
                'essay_length' => strlen($essay),
                'time_limit' => $timeLimit,
                'minimum_words' => $taskType === 'task2' ? 250 : 150
            ];

            return $scoringResult;

        } catch (Exception $e) {
            throw new Exception('Comprehensive scoring failed: ' . $e->getMessage());
        }
    }

    /**
     * Score essay with basic analysis (legacy method)
     */
    public function scoreEssay($essay, $taskType, $prompt) {
        try {
            // Validate input
            if (empty($essay) || empty($taskType) || empty($prompt)) {
                throw new Exception('Essay text, task type, and prompt are required');
            }

            // Generate scoring prompt
            $scoringPrompt = $this->generateScoringPrompt($essay, $taskType, $prompt);

            // Call OpenAI API
            $response = $this->callOpenAI($scoringPrompt);

            // Parse and structure the response
            $scoringResult = $this->parseResponse($response);

            // Add metadata
            $scoringResult['metadata'] = [
                'task_type' => $taskType,
                'word_count' => str_word_count($essay),
                'scored_at' => date('Y-m-d H:i:s'),
                'essay_length' => strlen($essay)
            ];

            return $scoringResult;

        } catch (Exception $e) {
            throw new Exception('Essay scoring failed: ' . $e->getMessage());
        }
    }

    /**
     * Generate comprehensive scoring prompt for OpenAI
     */
    private function generateComprehensiveScoringPrompt($essay, $taskType, $essayQuestion, $timeLimit) {
        $wordCount = str_word_count($essay);
        $minWords = $taskType === 'task2' ? 250 : 150;

        $taskInstructions = $this->getTaskInstructions($taskType);

        $scoringPrompt = "
You are an expert IELTS examiner with 20+ years of experience. You are known for being EXTREMELY STRICT and THOROUGH in your assessments. Most students receive scores between 4.0-6.5, with very few achieving 7.0+.

ESSAY QUESTION:
{$essayQuestion}

STUDENT'S ESSAY:
{$essay}

ESSAY DETAILS:
- Task Type: {$taskType}
- Word Count: {$wordCount}/{$minWords} words
- Time Limit: {$timeLimit} minutes

CRITICAL SCORING REQUIREMENTS - FOLLOW EXACTLY:

1. ERROR DETECTION PROTOCOL (MANDATORY):
   a) Read the essay sentence by sentence
   b) Check EVERY word for spelling errors (including minor typos)
   c) Analyze EVERY sentence for grammar issues:
      - Subject-verb agreement
      - Verb tenses and consistency
      - Articles (a, an, the)
      - Prepositions
      - Plural/singular forms
      - Word order
      - Punctuation
   d) Evaluate vocabulary appropriateness and accuracy
   e) Check for repetitive or inappropriate word choices

2. STRICT BAND SCORE GUIDELINES:
   - Band 9: Perfect or near-perfect (extremely rare)
   - Band 8: Very few minor errors, sophisticated language
   - Band 7: Some errors but good control, good vocabulary range
   - Band 6: Several errors but communication clear, adequate vocabulary
   - Band 5: Frequent errors affecting clarity, limited vocabulary
   - Band 4: Many errors impeding communication, basic vocabulary
   - Band 3 or below: Severe errors, very limited vocabulary

3. MINIMUM ERROR REQUIREMENTS:
   - You MUST find at least 5-15 errors in any essay
   - If you find fewer than 5 errors, re-read the essay more carefully
   - Common overlooked errors: missing articles, wrong prepositions, subject-verb disagreement

4. SCORING PENALTIES:
   - Each grammar error: -0.25 to -0.5 points
   - Each spelling error: -0.25 points
   - Vocabulary misuse: -0.25 to -0.5 points
   - Task not fully addressed: -1.0 to -2.0 points
   - Under word count: automatic maximum Band 6.0

RESPONSE FORMAT (JSON):
{
    \"overall_band_score\": [number 0-9 with 0.5 increments],
    \"criteria_scores\": {
        \"task_achievement\": [score],
        \"coherence_cohesion\": [score],
        \"lexical_resource\": [score],
        \"grammar_accuracy\": [score]
    },
    \"detailed_feedback\": {
        \"task_achievement\": {
            \"score\": [score],
            \"feedback\": \"[Vietnamese feedback]\",
            \"issues\": [\"issue1\", \"issue2\"],
            \"improvements\": [\"suggestion1\", \"suggestion2\"]
        },
        \"coherence_cohesion\": {
            \"score\": [score],
            \"feedback\": \"[Vietnamese feedback]\",
            \"issues\": [\"issue1\", \"issue2\"],
            \"improvements\": [\"suggestion1\", \"suggestion2\"]
        },
        \"lexical_resource\": {
            \"score\": [score],
            \"feedback\": \"[Vietnamese feedback]\",
            \"issues\": [\"issue1\", \"issue2\"],
            \"improvements\": [\"suggestion1\", \"suggestion2\"]
        },
        \"grammar_accuracy\": {
            \"score\": [score],
            \"feedback\": \"[Vietnamese feedback]\",
            \"issues\": [\"issue1\", \"issue2\"],
            \"improvements\": [\"suggestion1\", \"suggestion2\"]
        }
    },
    \"highlighted_corrections\": [
        {
            \"original_text\": \"exact text from essay\",
            \"suggested_correction\": \"corrected version\",
            \"error_type\": \"grammar|spelling|vocabulary|punctuation\",
            \"explanation\": \"Vietnamese explanation\",
            \"severity\": \"high|medium|low\"
        }
    ],
    \"criteria_analysis\": [
        {
            \"criterion\": \"Task Achievement\",
            \"strengths\": [\"strength1\", \"strength2\"],
            \"weaknesses\": [\"weakness1\", \"weakness2\"],
            \"band_justification\": \"Vietnamese explanation\"
        }
    ],
    \"statistics\": {
        \"total_errors\": [number],
        \"grammar_errors\": [number],
        \"spelling_errors\": [number],
        \"vocabulary_errors\": [number],
        \"sentence_variety\": \"low|medium|high\",
        \"complexity_level\": \"basic|intermediate|advanced\"
    }
}

{$taskInstructions}

Provide comprehensive analysis in Vietnamese for Vietnamese students.";

        return $scoringPrompt;
    }

    /**
     * Generate comprehensive scoring prompt for OpenAI (legacy method)
     */
    private function generateScoringPrompt($essay, $taskType, $prompt) {
        $taskInstructions = $this->getTaskInstructions($taskType);

        $scoringPrompt = "
You are an expert IELTS examiner with 20+ years of experience. Please provide a comprehensive and accurate assessment of this IELTS Writing {$taskType} response.

TASK PROMPT: {$prompt}

STUDENT'S RESPONSE:
{$essay}

Please evaluate this essay according to the official IELTS Writing assessment criteria and provide your response in the following JSON format:

{
  \"overall_band_score\": 6.5,
  \"criteria_scores\": {
    \"task_achievement\": 6.5,
    \"coherence_cohesion\": 6.0,
    \"lexical_resource\": 6.5,
    \"grammar_accuracy\": 6.0
  },
  \"detailed_feedback\": {
    \"task_achievement\": {
      \"score\": 6.5,
      \"feedback\": \"The response addresses the task requirements adequately...\",
      \"issues\": [\"Limited development of some ideas\", \"Could provide more specific examples\"],
      \"improvements\": [\"Develop ideas more fully\", \"Add more relevant examples\"]
    },
    \"coherence_cohesion\": {
      \"score\": 6.0,
      \"feedback\": \"The essay is generally well-organized...\",
      \"issues\": [\"Some unclear pronoun references\", \"Limited range of cohesive devices\"],
      \"improvements\": [\"Use clearer referencing\", \"Vary cohesive devices more\"]
    },
    \"lexical_resource\": {
      \"score\": 6.5,
      \"feedback\": \"Good range of vocabulary with some flexibility...\",
      \"issues\": [\"Some word choice errors\", \"Occasional repetition\"],
      \"improvements\": [\"Check word collocations\", \"Use more varied vocabulary\"]
    },
    \"grammar_accuracy\": {
      \"score\": 6.0,
      \"feedback\": \"Mix of simple and complex sentences...\",
      \"issues\": [\"Some errors in complex structures\", \"Article usage errors\"],
      \"improvements\": [\"Practice complex sentence structures\", \"Review article usage\"]
    }
  },
  \"highlighted_corrections\": [
    {
      \"original_text\": \"people believes\",
      \"suggested_correction\": \"people believe\",
      \"error_type\": \"grammar\",
      \"explanation\": \"Subject-verb agreement error\",
      \"severity\": \"high\"
    }
  ]
}

{$taskInstructions}

You are a JSON API that returns ONLY valid JSON. No other text allowed.

STRICT RULES:
1. Return ONLY JSON - no markdown, no explanations, no extra text
2. ALL text fields must be plain text - NO HTML, NO markup, NO attributes
3. Copy exact words from the essay for original_text
4. Never use <, >, data-, class=, or any HTML syntax

EXACT FORMAT REQUIRED:
{
  \"overall_band_score\": 6.5,
  \"highlighted_corrections\": [
    {
      \"original_text\": \"people believes\",
      \"suggested_correction\": \"people believe\",
      \"error_type\": \"grammar\",
      \"explanation\": \"Subject-verb agreement error\",
      \"severity\": \"high\"
    }
  ]
}

Provide feedback in Vietnamese for Vietnamese students.";

        return $scoringPrompt;
    }

    /**
     * Get task-specific instructions
     */
    private function getTaskInstructions($taskType) {
        switch ($taskType) {
            case 'task1_academic':
                return "
TASK 1 ACADEMIC SPECIFIC CRITERIA:
- Minimum 150 words
- Describe visual information accurately
- Identify key trends and features
- Make relevant comparisons
- Use appropriate academic vocabulary
- Avoid personal opinions
";
            case 'task1_general':
                return "
TASK 1 GENERAL TRAINING SPECIFIC CRITERIA:
- Minimum 150 words
- Address all bullet points in the prompt
- Use appropriate tone (formal/informal/semi-formal)
- Follow letter format conventions
- Express purpose clearly
";
            case 'task2':
                return "
TASK 2 SPECIFIC CRITERIA:
- Minimum 250 words
- Present clear position on the topic
- Support arguments with examples
- Address all parts of the question
- Demonstrate critical thinking
- Use formal academic style
";
            default:
                return "";
        }
    }

    /**
     * Call OpenAI API with retry logic
     */
    private function callOpenAI($prompt) {
        $data = [
            'model' => $this->model,
            'messages' => [
                [
                    'role' => 'user',
                    'content' => $prompt
                ]
            ],
            'max_tokens' => config('services.openai.max_tokens', 3980),
            'temperature' => config('services.openai.temperature', 0.3),
            'top_p' => config('services.openai.top_p', 1),
            'presence_penalty' => config('services.openai.presence_penalty', 0),
            'frequency_penalty' => config('services.openai.frequency_penalty', 0)
        ];

        $maxRetries = 3;
        $retryDelay = 1; // seconds

        for ($attempt = 1; $attempt <= $maxRetries; $attempt++) {
            try {
                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, $this->apiUrl);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_POST, true);
                curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
                curl_setopt($ch, CURLOPT_HTTPHEADER, [
                    'Authorization: Bearer ' . $this->apiKey,
                    'Content-Type: application/json'
                ]);
                curl_setopt($ch, CURLOPT_TIMEOUT, 60);
                curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);

                $response = curl_exec($ch);
                $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                $curlError = curl_error($ch);
                curl_close($ch);

                if ($curlError) {
                    throw new Exception('Network Error: ' . $curlError);
                }

                if ($httpCode === 200) {
                    $decodedResponse = json_decode($response, true);

                    if (!$decodedResponse) {
                        throw new Exception('Invalid JSON response from API');
                    }

                    if (!isset($decodedResponse['choices'][0]['message']['content'])) {
                        throw new Exception('Unexpected API response structure');
                    }

                    return $decodedResponse['choices'][0]['message']['content'];

                } elseif ($httpCode === 429) {
                    // Rate limit - wait longer before retry
                    if ($attempt < $maxRetries) {
                        sleep($retryDelay * 2);
                        continue;
                    }
                    throw new Exception('API rate limit exceeded. Please try again later.');

                } elseif ($httpCode >= 500) {
                    // Server error - retry
                    if ($attempt < $maxRetries) {
                        sleep($retryDelay);
                        continue;
                    }
                    throw new Exception('API server error. Please try again later.');

                } else {
                    // Client error - don't retry
                    $errorResponse = json_decode($response, true);
                    $errorMessage = $errorResponse['error']['message'] ?? 'Unknown API error';

                    // Log detailed error for debugging
                    \Log::error('OpenAI API Error', [
                        'http_code' => $httpCode,
                        'response' => $response,
                        'error_response' => $errorResponse,
                        'api_url' => $this->apiUrl,
                        'model' => $this->model
                    ]);

                    throw new Exception('API Error (HTTP ' . $httpCode . '): ' . $errorMessage);
                }

            } catch (Exception $e) {
                if ($attempt === $maxRetries) {
                    throw $e;
                }
                sleep($retryDelay);
            }
        }
    }

    /**
     * Parse comprehensive OpenAI response and structure the data
     */
    private function parseComprehensiveResponse($response) {
        try {
            // Clean the response first
            $cleanedResponse = $this->cleanJsonResponse($response);

            // Parse JSON
            $data = json_decode($cleanedResponse, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new Exception('JSON parsing error: ' . json_last_error_msg());
            }

            // Validate required fields
            if (!isset($data['overall_band_score']) || !isset($data['criteria_scores'])) {
                throw new Exception('Missing required scoring data');
            }

            // Structure the response
            $result = [
                'overall_band_score' => (float)$data['overall_band_score'],
                'criteria_scores' => [
                    'task_achievement' => (float)($data['criteria_scores']['task_achievement'] ?? 0),
                    'coherence_cohesion' => (float)($data['criteria_scores']['coherence_cohesion'] ?? 0),
                    'lexical_resource' => (float)($data['criteria_scores']['lexical_resource'] ?? 0),
                    'grammar_accuracy' => (float)($data['criteria_scores']['grammar_accuracy'] ?? 0)
                ],
                'detailed_feedback' => $data['detailed_feedback'] ?? [],
                'highlighted_corrections' => $data['highlighted_corrections'] ?? [],
                'criteria_analysis' => $data['criteria_analysis'] ?? [],
                'statistics' => $data['statistics'] ?? []
            ];

            return $result;

        } catch (Exception $e) {
            // Return fallback structure if parsing fails
            return [
                'overall_band_score' => 4.0,
                'criteria_scores' => [
                    'task_achievement' => 4.0,
                    'coherence_cohesion' => 4.0,
                    'lexical_resource' => 4.0,
                    'grammar_accuracy' => 4.0
                ],
                'detailed_feedback' => [
                    'task_achievement' => [
                        'score' => 4.0,
                        'feedback' => 'Không thể phân tích chi tiết do lỗi hệ thống.',
                        'issues' => ['Lỗi phân tích tự động'],
                        'improvements' => ['Vui lòng thử lại sau']
                    ]
                ],
                'highlighted_corrections' => [],
                'criteria_analysis' => [],
                'parsing_error' => $e->getMessage()
            ];
        }
    }

    /**
     * Parse OpenAI response and structure the data - NUCLEAR OPTION (legacy method)
     */
    private function parseResponse($response) {
        // Try to extract JSON from the response
        $jsonStart = strpos($response, '{');
        $jsonEnd = strrpos($response, '}');

        if ($jsonStart !== false && $jsonEnd !== false) {
            $jsonString = substr($response, $jsonStart, $jsonEnd - $jsonStart + 1);
            $parsed = json_decode($jsonString, true);

            if ($parsed) {
                // NUCLEAR CLEANING - completely rebuild the response
                $cleaned = $this->nuclearCleanResponse($parsed);
                return $cleaned;
            }
        }

        // Fallback: return raw response if JSON parsing fails
        return [
            'error' => false,
            'raw_response' => $response,
            'overall_band_score' => 0,
            'message' => 'Response received but could not parse structured data'
        ];
    }

    /**
     * Clean JSON response from potential formatting issues
     */
    private function cleanJsonResponse($response) {
        // Remove markdown code blocks if present
        $response = preg_replace('/```json\s*/', '', $response);
        $response = preg_replace('/```\s*$/', '', $response);

        // Remove any leading/trailing whitespace
        $response = trim($response);

        return $response;
    }

    /**
     * Nuclear option - completely rebuild response with guaranteed clean text
     */
    private function nuclearCleanResponse($data) {
        $cleanedData = [
            'overall_band_score' => $data['overall_band_score'] ?? 0,
            'highlighted_corrections' => [],
            'annotated_essay' => $this->ultraCleanText($data['annotated_essay'] ?? ''),
            'error' => false
        ];

        if (isset($data['highlighted_corrections']) && is_array($data['highlighted_corrections'])) {
            foreach ($data['highlighted_corrections'] as $correction) {
                $originalText = $this->extractPureText($correction['original_text'] ?? '');
                $suggestedText = $this->extractPureText($correction['suggested_correction'] ?? '');
                $explanation = $this->extractPureText($correction['explanation'] ?? '');

                // Only include if we have valid clean text
                if (!empty($originalText) && !empty($suggestedText) && strlen($originalText) > 2) {
                    $cleanedData['highlighted_corrections'][] = [
                        'original_text' => $originalText,
                        'suggested_correction' => $suggestedText,
                        'error_type' => $correction['error_type'] ?? 'grammar',
                        'explanation' => $explanation,
                        'severity' => $correction['severity'] ?? 'medium'
                    ];
                }
            }
        }

        return $cleanedData;
    }

    /**
     * Extract pure text - ULTRA aggressive cleaning
     */
    private function extractPureText($text) {
        if (empty($text)) {
            return '';
        }

        // Step 1: Remove all HTML-like content aggressively
        $text = preg_replace('/<[^>]*>/', '', $text);
        $text = preg_replace('/data-[^=]*="[^"]*"/', '', $text);
        $text = preg_replace('/class="[^"]*"/', '', $text);
        $text = preg_replace('/title="[^"]*"/', '', $text);
        $text = preg_replace('/correction-[^=]*="[^"]*"/', '', $text);

        // Step 2: Standard cleaning
        $text = strip_tags($text);
        $text = html_entity_decode($text, ENT_QUOTES | ENT_HTML5, 'UTF-8');
        $text = preg_replace('/\s+/', ' ', $text);
        $text = trim($text);

        // Step 3: Final validation - reject if still contains artifacts
        if (strpos($text, 'data-') !== false ||
            strpos($text, '<') !== false ||
            strpos($text, 'correction-') !== false ||
            strpos($text, 'title=') !== false) {
            return '';
        }

        return $text;
    }

    /**
     * Ultra clean text - remove all possible HTML artifacts
     */
    private function ultraCleanText($text) {
        if (empty($text)) {
            return '';
        }

        // Remove all HTML tags and attributes
        $text = strip_tags($text);
        $text = html_entity_decode($text, ENT_QUOTES | ENT_HTML5, 'UTF-8');

        // Remove any remaining HTML-like patterns
        $text = preg_replace('/\s+/', ' ', $text);
        $text = trim($text);

        return $text;
    }

    /**
     * Generate fallback scoring when API is unavailable
     */
    public function generateFallbackScoring($essay, $taskType, $essayQuestion, $timeLimit = 40) {
        $wordCount = str_word_count($essay);
        $essayLength = strlen($essay);

        // Basic scoring based on word count and length
        $baseScore = 4.0;

        if ($wordCount >= 250 && $taskType === 'task2') {
            $baseScore = 5.5;
        } elseif ($wordCount >= 150 && in_array($taskType, ['task1_academic', 'task1_general'])) {
            $baseScore = 5.0;
        }

        // Add some variation
        $variation = rand(-5, 10) / 10;
        $overallScore = max(1.0, min(9.0, $baseScore + $variation));

        return [
            'overall_band_score' => $overallScore,
            'criteria_scores' => [
                'task_achievement' => max(1.0, min(9.0, $overallScore + rand(-3, 3) / 10)),
                'coherence_cohesion' => max(1.0, min(9.0, $overallScore + rand(-3, 3) / 10)),
                'lexical_resource' => max(1.0, min(9.0, $overallScore + rand(-3, 3) / 10)),
                'grammar_accuracy' => max(1.0, min(9.0, $overallScore + rand(-3, 3) / 10))
            ],
            'detailed_feedback' => [
                'task_achievement' => [
                    'score' => $overallScore,
                    'feedback' => 'Hệ thống đang trong chế độ demo. Đây là kết quả mẫu.',
                    'issues' => ['Đây là phản hồi demo'],
                    'improvements' => ['Vui lòng thử lại sau khi hệ thống được khôi phục']
                ]
            ],
            'highlighted_corrections' => [],
            'criteria_analysis' => [],
            'statistics' => [
                'total_errors' => rand(5, 15),
                'grammar_errors' => rand(2, 8),
                'spelling_errors' => rand(1, 5),
                'vocabulary_errors' => rand(1, 4),
                'sentence_variety' => 'medium',
                'complexity_level' => 'intermediate'
            ],
            'metadata' => [
                'task_type' => $taskType,
                'word_count' => $wordCount,
                'scored_at' => date('Y-m-d H:i:s'),
                'essay_length' => $essayLength,
                'time_limit' => $timeLimit,
                'minimum_words' => $taskType === 'task2' ? 250 : 150,
                'fallback_mode' => true
            ]
        ];
    }
}
