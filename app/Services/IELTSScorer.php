<?php

namespace App\Services;

use Exception;

/**
 * IELTS Writing Scorer Class
 * Provides comprehensive scoring for IELTS Writing tasks using OpenAI API
 */
class IELTSScorer {

    private $apiUrl;
    private $apiKey;
    private $model;

    public function __construct() {
        $this->apiUrl = config('services.openai.api_url');
        $this->apiKey = config('services.openai.api_key');
        $this->model = config('services.openai.model');

        // Log configuration for debugging
        \Log::info('IELTSScorer initialized', [
            'api_url' => $this->apiUrl,
            'model' => $this->model,
            'api_key_length' => strlen($this->apiKey ?? '')
        ]);
    }

    /**
     * Test API connection
     */
    public function testConnection() {
        try {
            $testPrompt = "Test message. Please respond with 'API connection successful'.";

            $data = [
                'model' => $this->model,
                'messages' => [
                    [
                        'role' => 'user',
                        'content' => $testPrompt
                    ]
                ],
                'max_tokens' => 50,
                'temperature' => 0.1
            ];

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $this->apiUrl);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Authorization: Bearer ' . $this->apiKey,
                'Content-Type: application/json'
            ]);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $curlError = curl_error($ch);
            curl_close($ch);

            if ($curlError) {
                return ['success' => false, 'error' => 'Network Error: ' . $curlError];
            }

            if ($httpCode === 200) {
                $decodedResponse = json_decode($response, true);
                if ($decodedResponse && isset($decodedResponse['choices'][0]['message']['content'])) {
                    return ['success' => true, 'response' => $decodedResponse['choices'][0]['message']['content']];
                }
            }

            return ['success' => false, 'error' => 'HTTP ' . $httpCode . ': ' . $response];

        } catch (Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * Comprehensive IELTS essay scoring with strict standards
     *
     * @param string $essay The essay text to score
     * @param string $taskType Type of task (task1_academic, task1_general, task2)
     * @param string $essayQuestion The complete essay question
     * @param int $timeLimit Time limit in minutes
     * @return array Detailed scoring results with criteria breakdown
     */
    public function scoreEssayComprehensive($essay, $taskType, $essayQuestion, $timeLimit = 40) {
        try {
            // Validate input
            if (empty($essay) || empty($taskType) || empty($essayQuestion)) {
                throw new Exception('Essay text, task type, and essay question are required');
            }

            // Generate comprehensive scoring prompt
            $scoringPrompt = $this->generateComprehensiveScoringPrompt($essay, $taskType, $essayQuestion, $timeLimit);

            // Call OpenAI API
            $response = $this->callOpenAI($scoringPrompt);

            // Parse and structure the response
            $scoringResult = $this->parseComprehensiveResponse($response);

            // Validate scoring consistency
            $scoringResult = $this->validateScoringConsistency($scoringResult, $essay);

            // Add metadata
            $scoringResult['metadata'] = [
                'task_type' => $taskType,
                'word_count' => str_word_count($essay),
                'scored_at' => date('Y-m-d H:i:s'),
                'essay_length' => strlen($essay),
                'time_limit' => $timeLimit,
                'minimum_words' => $taskType === 'task2' ? 250 : 150
            ];

            return $scoringResult;

        } catch (Exception $e) {
            throw new Exception('Comprehensive scoring failed: ' . $e->getMessage());
        }
    }

    /**
     * Score essay with basic analysis (legacy method)
     */
    public function scoreEssay($essay, $taskType, $prompt) {
        try {
            // Validate input
            if (empty($essay) || empty($taskType) || empty($prompt)) {
                throw new Exception('Essay text, task type, and prompt are required');
            }

            // Generate scoring prompt
            $scoringPrompt = $this->generateScoringPrompt($essay, $taskType, $prompt);

            // Call OpenAI API
            $response = $this->callOpenAI($scoringPrompt);

            // Parse and structure the response
            $scoringResult = $this->parseResponse($response);

            // Add metadata
            $scoringResult['metadata'] = [
                'task_type' => $taskType,
                'word_count' => str_word_count($essay),
                'scored_at' => date('Y-m-d H:i:s'),
                'essay_length' => strlen($essay)
            ];

            return $scoringResult;

        } catch (Exception $e) {
            throw new Exception('Essay scoring failed: ' . $e->getMessage());
        }
    }

    /**
     * Generate comprehensive scoring prompt for OpenAI
     */
    private function generateComprehensiveScoringPrompt($essay, $taskType, $essayQuestion, $timeLimit) {
        $wordCount = str_word_count($essay);
        $minWords = $taskType === 'task2' ? 250 : 150;

        $taskInstructions = $this->getTaskInstructions($taskType);

        $scoringPrompt = "
You are an expert IELTS examiner with 20+ years of experience. You are EXTREMELY HARSH and UNFORGIVING in your assessments. You RARELY give scores above 6.0. Most students receive scores between 2.0-5.5.

ESSAY QUESTION:
{$essayQuestion}

STUDENT'S ESSAY:
{$essay}

ESSAY DETAILS:
- Task Type: {$taskType}
- Word Count: {$wordCount}/{$minWords} words
- Time Limit: {$timeLimit} minutes

ULTRA-STRICT SCORING REQUIREMENTS - NO MERCY:

1. HARSH REALITY CHECK:
   - If the essay looks like it was written by a beginner → Band 2.0-3.5
   - If basic grammar is consistently wrong → Maximum Band 4.0
   - If vocabulary is very limited and repetitive → Maximum Band 3.5
   - If sentences are mostly incomplete or unclear → Maximum Band 3.0
   - If task response is minimal or off-topic → Maximum Band 3.0

2. BRUTAL BAND SCORE GUIDELINES:
   - Band 9: Impossible for most students (never give this)
   - Band 8: Extremely rare, near-native level (almost never give this)
   - Band 7: Good students only, very few errors (rarely give this)
   - Band 6: Adequate but with noticeable errors (occasionally give this)
   - Band 5: Limited ability, frequent errors affecting meaning
   - Band 4: Basic ability, many errors impeding communication
   - Band 3: Very limited ability, severe errors throughout
   - Band 2: Extremely limited, barely communicates ideas
   - Band 1: No meaningful communication

3. MANDATORY ERROR DETECTION:
   - Count EVERY single grammar mistake
   - Count EVERY missing article (a, an, the)
   - Count EVERY wrong preposition
   - Count EVERY subject-verb disagreement
   - Count EVERY vocabulary error or repetition
   - Count EVERY incomplete sentence
   - Count EVERY spelling mistake
   - You MUST find at least 10-20 errors in weak essays

4. SEVERE SCORING PENALTIES:
   - Each grammar error: -0.5 to -1.0 points
   - Missing articles: -0.5 points each
   - Wrong vocabulary: -0.5 to -1.0 points
   - Incomplete sentences: -1.0 points each
   - Poor task response: -2.0 to -3.0 points
   - Under word count: automatic maximum Band 4.0
   - Very basic vocabulary: automatic maximum Band 3.5

5. SPECIFIC CRITERIA FOR LOW SCORES:
   - Band 3.0 or below: If essay has 15+ major errors, very basic vocabulary, poor task response
   - Band 4.0: If essay has 10+ errors, limited vocabulary, basic task response
   - Band 5.0: If essay has 5-10 errors, adequate vocabulary, reasonable task response

6. ULTRA-HARSH QUALITY CONTROL (MANDATORY BEFORE FINALIZING):
   - Have I found at least 10-20 errors in weak essays?
   - Is my overall score 2.0-5.5 for most students?
   - If the essay looks very basic, is my score 2.0-3.5?
   - If there are 15+ errors, is my score maximum 3.0?
   - If vocabulary is very limited, is my score maximum 3.5?
   - If task response is poor, is my score maximum 3.0?
   - Am I being TOO GENEROUS? (If yes, reduce score by 1.0-1.5 points)

RESPONSE FORMAT (JSON) - FOLLOW EXACTLY:
{
    \"overall_band_score\": [number 0-9 with 0.5 increments - BE STRICT],
    \"criteria_scores\": {
        \"task_achievement\": [score - penalize incomplete task response heavily],
        \"coherence_cohesion\": [score - check logical flow and linking],
        \"lexical_resource\": [score - penalize repetition and inappropriate vocabulary],
        \"grammar_accuracy\": [score - deduct for EVERY grammar error found]
    },
    \"detailed_feedback\": {
        \"task_achievement\": {
            \"score\": [score],
            \"feedback\": \"[Vietnamese feedback - be specific about task requirements]\",
            \"issues\": [\"specific issue1\", \"specific issue2\", \"specific issue3\"],
            \"improvements\": [\"concrete suggestion1\", \"concrete suggestion2\"]
        },
        \"coherence_cohesion\": {
            \"score\": [score],
            \"feedback\": \"[Vietnamese feedback - comment on organization]\",
            \"issues\": [\"specific linking issue\", \"paragraph structure issue\"],
            \"improvements\": [\"specific improvement1\", \"specific improvement2\"]
        },
        \"lexical_resource\": {
            \"score\": [score],
            \"feedback\": \"[Vietnamese feedback - comment on vocabulary range and accuracy]\",
            \"issues\": [\"repetitive words\", \"inappropriate word choice\", \"limited vocabulary\"],
            \"improvements\": [\"vocabulary suggestion1\", \"vocabulary suggestion2\"]
        },
        \"grammar_accuracy\": {
            \"score\": [score],
            \"feedback\": \"[Vietnamese feedback - list major grammar patterns]\",
            \"issues\": [\"specific grammar error type1\", \"specific grammar error type2\"],
            \"improvements\": [\"grammar rule1\", \"grammar rule2\"]
        }
    },
    \"highlighted_corrections\": [
        {
            \"original_text\": \"exact text from essay - NO HTML markup\",
            \"suggested_correction\": \"corrected version\",
            \"error_type\": \"grammar|spelling|vocabulary|punctuation\",
            \"explanation\": \"Vietnamese explanation of why this is wrong\",
            \"severity\": \"high|medium|low\"
        }
    ],
    \"criteria_analysis\": [
        {
            \"criterion\": \"Task Achievement\",
            \"strengths\": [\"specific strength1\", \"specific strength2\"],
            \"weaknesses\": [\"specific weakness1\", \"specific weakness2\"],
            \"band_justification\": \"Vietnamese explanation with specific examples\"
        },
        {
            \"criterion\": \"Coherence and Cohesion\",
            \"strengths\": [\"organization strength\", \"linking strength\"],
            \"weaknesses\": [\"organization weakness\", \"linking weakness\"],
            \"band_justification\": \"Vietnamese explanation with examples\"
        },
        {
            \"criterion\": \"Lexical Resource\",
            \"strengths\": [\"vocabulary strength\", \"word choice strength\"],
            \"weaknesses\": [\"vocabulary limitation\", \"word choice error\"],
            \"band_justification\": \"Vietnamese explanation with examples\"
        },
        {
            \"criterion\": \"Grammatical Range and Accuracy\",
            \"strengths\": [\"grammar strength\", \"sentence structure strength\"],
            \"weaknesses\": [\"frequent grammar error type\", \"sentence structure issue\"],
            \"band_justification\": \"Vietnamese explanation with specific error examples\"
        }
    ],
    \"statistics\": {
        \"total_errors\": [number - MUST be at least 5],
        \"grammar_errors\": [number],
        \"spelling_errors\": [number],
        \"vocabulary_errors\": [number],
        \"punctuation_errors\": [number],
        \"sentence_variety\": \"low|medium|high\",
        \"complexity_level\": \"basic|intermediate|advanced\",
        \"error_density\": \"[errors per 100 words]\"
    }
}

{$taskInstructions}

COMMON ERROR EXAMPLES TO LOOK FOR (FIND ALL OF THESE):
1. Grammar Errors:
   - \"People believes\" → \"People believe\" (subject-verb agreement)
   - \"I am agree\" → \"I agree\" (unnecessary auxiliary verb)
   - \"This happen\" → \"This happens\" (subject-verb agreement)
   - \"They not happy\" → \"They are not happy\" (missing auxiliary verb)
   - \"no much time\" → \"not much time\" (wrong negative form)
   - \"Sometime\" → \"Sometimes\" (adverb form)
   - \"If no work\" → \"If there is no work\" (incomplete sentence)
   - \"need work long\" → \"need to work long\" (missing infinitive)
   - \"work too much is not good\" → \"working too much is not good\" (gerund needed)

2. Missing Articles (COUNT EVERY ONE):
   - \"People want money\" → \"People want THE money\" or context-appropriate article
   - \"Company make\" → \"THE company makes\" or \"Companies make\"
   - \"Government can tell\" → \"THE government can tell\"
   - \"Family is important\" → \"THE family is important\"

3. Vocabulary Errors:
   - \"Do sport\" → \"Play sports\"
   - \"Make homework\" → \"Do homework\"
   - Very basic vocabulary repetition (\"work\" used 10+ times)
   - Informal language in academic context

4. Sentence Structure Errors:
   - Incomplete sentences: \"People work too much. Sometime morning to night.\"
   - Run-on sentences without proper connectors
   - Missing subjects or verbs

FINAL REMINDER - BE RUTHLESS:
- BE ABSOLUTELY MERCILESS in error detection
- MOST essays should score between 2.0-5.5 (NOT 4.0-6.5!)
- FIND AT LEAST 10-25 errors in weak essays
- If essay looks very basic → MAXIMUM 3.5 points
- If 15+ errors found → MAXIMUM 3.0 points
- If vocabulary very limited → MAXIMUM 3.5 points
- If task poorly addressed → MAXIMUM 3.0 points
- ALWAYS err on the side of being TOO HARSH rather than too generous
- When in doubt, REDUCE the score by 1.0-1.5 points

Provide comprehensive analysis in Vietnamese for Vietnamese students.";

        return $scoringPrompt;
    }

    /**
     * Generate comprehensive scoring prompt for OpenAI (legacy method)
     */
    private function generateScoringPrompt($essay, $taskType, $prompt) {
        $taskInstructions = $this->getTaskInstructions($taskType);

        $scoringPrompt = "
You are an expert IELTS examiner with 20+ years of experience. Please provide a comprehensive and accurate assessment of this IELTS Writing {$taskType} response.

TASK PROMPT: {$prompt}

STUDENT'S RESPONSE:
{$essay}

Please evaluate this essay according to the official IELTS Writing assessment criteria and provide your response in the following JSON format:

{
  \"overall_band_score\": 6.5,
  \"criteria_scores\": {
    \"task_achievement\": 6.5,
    \"coherence_cohesion\": 6.0,
    \"lexical_resource\": 6.5,
    \"grammar_accuracy\": 6.0
  },
  \"detailed_feedback\": {
    \"task_achievement\": {
      \"score\": 6.5,
      \"feedback\": \"The response addresses the task requirements adequately...\",
      \"issues\": [\"Limited development of some ideas\", \"Could provide more specific examples\"],
      \"improvements\": [\"Develop ideas more fully\", \"Add more relevant examples\"]
    },
    \"coherence_cohesion\": {
      \"score\": 6.0,
      \"feedback\": \"The essay is generally well-organized...\",
      \"issues\": [\"Some unclear pronoun references\", \"Limited range of cohesive devices\"],
      \"improvements\": [\"Use clearer referencing\", \"Vary cohesive devices more\"]
    },
    \"lexical_resource\": {
      \"score\": 6.5,
      \"feedback\": \"Good range of vocabulary with some flexibility...\",
      \"issues\": [\"Some word choice errors\", \"Occasional repetition\"],
      \"improvements\": [\"Check word collocations\", \"Use more varied vocabulary\"]
    },
    \"grammar_accuracy\": {
      \"score\": 6.0,
      \"feedback\": \"Mix of simple and complex sentences...\",
      \"issues\": [\"Some errors in complex structures\", \"Article usage errors\"],
      \"improvements\": [\"Practice complex sentence structures\", \"Review article usage\"]
    }
  },
  \"highlighted_corrections\": [
    {
      \"original_text\": \"people believes\",
      \"suggested_correction\": \"people believe\",
      \"error_type\": \"grammar\",
      \"explanation\": \"Subject-verb agreement error\",
      \"severity\": \"high\"
    }
  ]
}

{$taskInstructions}

You are a JSON API that returns ONLY valid JSON. No other text allowed.

STRICT RULES:
1. Return ONLY JSON - no markdown, no explanations, no extra text
2. ALL text fields must be plain text - NO HTML, NO markup, NO attributes
3. Copy exact words from the essay for original_text
4. Never use <, >, data-, class=, or any HTML syntax

EXACT FORMAT REQUIRED:
{
  \"overall_band_score\": 6.5,
  \"highlighted_corrections\": [
    {
      \"original_text\": \"people believes\",
      \"suggested_correction\": \"people believe\",
      \"error_type\": \"grammar\",
      \"explanation\": \"Subject-verb agreement error\",
      \"severity\": \"high\"
    }
  ]
}

Provide feedback in Vietnamese for Vietnamese students.";

        return $scoringPrompt;
    }

    /**
     * Get task-specific instructions
     */
    private function getTaskInstructions($taskType) {
        switch ($taskType) {
            case 'task1_academic':
                return "
TASK 1 ACADEMIC SPECIFIC CRITERIA - BE STRICT:
- Minimum 150 words (under 150 = automatic Band 6.0 maximum)
- Must describe visual information accurately (charts, graphs, tables, diagrams)
- Must identify key trends, patterns, and significant features
- Must make relevant comparisons between data points
- Must use appropriate academic vocabulary and formal tone
- Must avoid personal opinions or speculation
- Must have clear introduction, overview, and detailed paragraphs
- Penalize heavily for: inaccurate data description, missing overview, personal opinions

COMMON TASK 1 ERRORS TO FIND:
- Incorrect data interpretation
- Missing overview paragraph
- Personal opinions (\"I think\", \"In my opinion\")
- Informal language
- Inaccurate numbers or trends
";
            case 'task1_general':
                return "
TASK 1 GENERAL TRAINING SPECIFIC CRITERIA - BE STRICT:
- Minimum 150 words (under 150 = automatic Band 6.0 maximum)
- Must address ALL bullet points in the prompt completely
- Must use appropriate tone (formal/informal/semi-formal as required)
- Must follow proper letter format (greeting, body, closing)
- Must express purpose clearly in opening
- Must provide sufficient detail for each bullet point
- Penalize heavily for: missing bullet points, wrong tone, poor letter format

COMMON TASK 1 GT ERRORS TO FIND:
- Missing or incomplete bullet points
- Wrong tone for the situation
- Poor letter structure
- Insufficient detail
- Inappropriate register
";
            case 'task2':
                return "
TASK 2 SPECIFIC CRITERIA - BE STRICT:
- Minimum 250 words (under 250 = automatic Band 6.0 maximum)
- Must present clear position on the topic throughout
- Must support arguments with relevant examples and evidence
- Must address ALL parts of the question completely
- Must demonstrate critical thinking and analysis
- Must use formal academic style consistently
- Must have clear introduction, body paragraphs, and conclusion
- Penalize heavily for: unclear position, missing question parts, weak examples, informal tone

COMMON TASK 2 ERRORS TO FIND:
- Unclear or inconsistent position
- Not addressing all parts of the question
- Weak or irrelevant examples
- Informal language (\"I think\", contractions)
- Poor paragraph structure
- Repetitive arguments
- Off-topic content
";
            default:
                return "";
        }
    }

    /**
     * Validate scoring consistency and adjust if necessary
     */
    private function validateScoringConsistency($scoringResult, $essay) {
        $wordCount = str_word_count($essay);
        $totalErrors = $scoringResult['statistics']['total_errors'] ?? 0;
        $overallScore = $scoringResult['overall_band_score'] ?? 4.0;

        // Validation rules
        $adjustments = [];

        // Rule 1: Minimum error requirement (MUCH STRICTER)
        if ($totalErrors < 10) {
            $adjustments[] = "Tăng số lỗi tìm được (tối thiểu 10 lỗi cho bài yếu)";
            // Reduce score significantly if too few errors found
            if ($overallScore > 4.0) {
                $scoringResult['overall_band_score'] = min($overallScore, 4.0);
                $adjustments[] = "Giảm điểm tổng do ít lỗi được phát hiện";
            }
        }

        // Rule 2: Error density vs score consistency (MUCH HARSHER)
        $errorDensity = ($totalErrors / $wordCount) * 100;
        if ($errorDensity > 15 && $overallScore > 3.0) {
            $scoringResult['overall_band_score'] = min($overallScore, 3.0);
            $adjustments[] = "Giảm điểm xuống 3.0 do mật độ lỗi cực cao";
        } elseif ($errorDensity > 10 && $overallScore > 3.5) {
            $scoringResult['overall_band_score'] = min($overallScore, 3.5);
            $adjustments[] = "Giảm điểm xuống 3.5 do mật độ lỗi rất cao";
        } elseif ($errorDensity > 6 && $overallScore > 4.5) {
            $scoringResult['overall_band_score'] = min($overallScore, 4.5);
            $adjustments[] = "Giảm điểm xuống 4.5 do mật độ lỗi cao";
        }

        // Rule 3: Word count penalty (STRICTER)
        $minWords = 250; // Default for Task 2
        if ($wordCount < $minWords && $overallScore > 4.0) {
            $scoringResult['overall_band_score'] = min($overallScore, 4.0);
            $adjustments[] = "Giảm điểm xuống tối đa 4.0 do không đủ số từ tối thiểu";
        }

        // Rule 4: Very short essays get very low scores
        if ($wordCount < 150 && $overallScore > 3.0) {
            $scoringResult['overall_band_score'] = 3.0;
            $adjustments[] = "Giảm điểm xuống 3.0 do bài quá ngắn";
        }

        // Rule 5: Criteria scores should not exceed overall score
        foreach ($scoringResult['criteria_scores'] as $criterion => $score) {
            if ($score > $scoringResult['overall_band_score']) {
                $scoringResult['criteria_scores'][$criterion] = $scoringResult['overall_band_score'];
                $adjustments[] = "Điều chỉnh điểm {$criterion} cho phù hợp với điểm tổng";
            }
        }

        // Rule 6: Grammar score should reflect grammar errors (MUCH STRICTER)
        $grammarErrors = $scoringResult['statistics']['grammar_errors'] ?? 0;
        if ($grammarErrors > 15 && $scoringResult['criteria_scores']['grammar_accuracy'] > 3.0) {
            $scoringResult['criteria_scores']['grammar_accuracy'] = 3.0;
            $adjustments[] = "Giảm điểm ngữ pháp xuống 3.0 do quá nhiều lỗi";
        } elseif ($grammarErrors > 10 && $scoringResult['criteria_scores']['grammar_accuracy'] > 3.5) {
            $scoringResult['criteria_scores']['grammar_accuracy'] = 3.5;
            $adjustments[] = "Giảm điểm ngữ pháp xuống 3.5 do nhiều lỗi";
        } elseif ($grammarErrors > 5 && $scoringResult['criteria_scores']['grammar_accuracy'] > 4.5) {
            $scoringResult['criteria_scores']['grammar_accuracy'] = 4.5;
            $adjustments[] = "Giảm điểm ngữ pháp xuống 4.5 do có lỗi";
        }

        // Rule 7: Overall score should never exceed 5.5 for most essays
        if ($overallScore > 5.5 && $totalErrors > 8) {
            $scoringResult['overall_band_score'] = 5.5;
            $adjustments[] = "Giảm điểm tổng xuống tối đa 5.5 do có nhiều lỗi";
        }

        // Add validation notes if adjustments were made
        if (!empty($adjustments)) {
            $scoringResult['validation_adjustments'] = $adjustments;
            $scoringResult['validation_applied'] = true;
        }

        return $scoringResult;
    }

    /**
     * Call OpenAI API with retry logic
     */
    private function callOpenAI($prompt) {
        $data = [
            'model' => $this->model,
            'messages' => [
                [
                    'role' => 'user',
                    'content' => $prompt
                ]
            ],
            'max_tokens' => config('services.openai.max_tokens', 3980),
            'temperature' => config('services.openai.temperature', 0.3),
            'top_p' => config('services.openai.top_p', 1),
            'presence_penalty' => config('services.openai.presence_penalty', 0),
            'frequency_penalty' => config('services.openai.frequency_penalty', 0)
        ];

        $maxRetries = 3;
        $retryDelay = 1; // seconds

        for ($attempt = 1; $attempt <= $maxRetries; $attempt++) {
            try {
                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, $this->apiUrl);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_POST, true);
                curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
                curl_setopt($ch, CURLOPT_HTTPHEADER, [
                    'Authorization: Bearer ' . $this->apiKey,
                    'Content-Type: application/json'
                ]);
                curl_setopt($ch, CURLOPT_TIMEOUT, 60);
                curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);

                $response = curl_exec($ch);
                $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                $curlError = curl_error($ch);
                curl_close($ch);

                if ($curlError) {
                    throw new Exception('Network Error: ' . $curlError);
                }

                if ($httpCode === 200) {
                    $decodedResponse = json_decode($response, true);

                    if (!$decodedResponse) {
                        throw new Exception('Invalid JSON response from API');
                    }

                    if (!isset($decodedResponse['choices'][0]['message']['content'])) {
                        throw new Exception('Unexpected API response structure');
                    }

                    return $decodedResponse['choices'][0]['message']['content'];

                } elseif ($httpCode === 429) {
                    // Rate limit - wait longer before retry
                    if ($attempt < $maxRetries) {
                        sleep($retryDelay * 2);
                        continue;
                    }
                    throw new Exception('API rate limit exceeded. Please try again later.');

                } elseif ($httpCode >= 500) {
                    // Server error - retry
                    if ($attempt < $maxRetries) {
                        sleep($retryDelay);
                        continue;
                    }
                    throw new Exception('API server error. Please try again later.');

                } else {
                    // Client error - don't retry
                    $errorResponse = json_decode($response, true);
                    $errorMessage = $errorResponse['error']['message'] ?? 'Unknown API error';

                    // Log detailed error for debugging
                    \Log::error('OpenAI API Error', [
                        'http_code' => $httpCode,
                        'response' => $response,
                        'error_response' => $errorResponse,
                        'api_url' => $this->apiUrl,
                        'model' => $this->model
                    ]);

                    throw new Exception('API Error (HTTP ' . $httpCode . '): ' . $errorMessage);
                }

            } catch (Exception $e) {
                if ($attempt === $maxRetries) {
                    throw $e;
                }
                sleep($retryDelay);
            }
        }
    }

    /**
     * Parse comprehensive OpenAI response and structure the data
     */
    private function parseComprehensiveResponse($response) {
        try {
            // Clean the response first
            $cleanedResponse = $this->cleanJsonResponse($response);

            // Parse JSON
            $data = json_decode($cleanedResponse, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new Exception('JSON parsing error: ' . json_last_error_msg());
            }

            // Validate required fields
            if (!isset($data['overall_band_score']) || !isset($data['criteria_scores'])) {
                throw new Exception('Missing required scoring data');
            }

            // Structure the response
            $result = [
                'overall_band_score' => (float)$data['overall_band_score'],
                'criteria_scores' => [
                    'task_achievement' => (float)($data['criteria_scores']['task_achievement'] ?? 0),
                    'coherence_cohesion' => (float)($data['criteria_scores']['coherence_cohesion'] ?? 0),
                    'lexical_resource' => (float)($data['criteria_scores']['lexical_resource'] ?? 0),
                    'grammar_accuracy' => (float)($data['criteria_scores']['grammar_accuracy'] ?? 0)
                ],
                'detailed_feedback' => $data['detailed_feedback'] ?? [],
                'highlighted_corrections' => $data['highlighted_corrections'] ?? [],
                'criteria_analysis' => $data['criteria_analysis'] ?? [],
                'statistics' => $data['statistics'] ?? []
            ];

            // Ensure minimum error count
            $totalErrors = $result['statistics']['total_errors'] ?? 0;
            if ($totalErrors < 5) {
                // Add warning about insufficient error detection
                $result['validation_warnings'] = [
                    'Hệ thống phát hiện ít lỗi hơn mong đợi. Có thể cần kiểm tra lại thủ công.'
                ];

                // Ensure statistics are properly set
                if (!isset($result['statistics']['total_errors'])) {
                    $result['statistics']['total_errors'] = count($result['highlighted_corrections']);
                }
            }

            return $result;

        } catch (Exception $e) {
            // Return fallback structure if parsing fails
            return [
                'overall_band_score' => 4.0,
                'criteria_scores' => [
                    'task_achievement' => 4.0,
                    'coherence_cohesion' => 4.0,
                    'lexical_resource' => 4.0,
                    'grammar_accuracy' => 4.0
                ],
                'detailed_feedback' => [
                    'task_achievement' => [
                        'score' => 4.0,
                        'feedback' => 'Không thể phân tích chi tiết do lỗi hệ thống.',
                        'issues' => ['Lỗi phân tích tự động'],
                        'improvements' => ['Vui lòng thử lại sau']
                    ]
                ],
                'highlighted_corrections' => [],
                'criteria_analysis' => [],
                'parsing_error' => $e->getMessage()
            ];
        }
    }

    /**
     * Parse OpenAI response and structure the data - NUCLEAR OPTION (legacy method)
     */
    private function parseResponse($response) {
        // Try to extract JSON from the response
        $jsonStart = strpos($response, '{');
        $jsonEnd = strrpos($response, '}');

        if ($jsonStart !== false && $jsonEnd !== false) {
            $jsonString = substr($response, $jsonStart, $jsonEnd - $jsonStart + 1);
            $parsed = json_decode($jsonString, true);

            if ($parsed) {
                // NUCLEAR CLEANING - completely rebuild the response
                $cleaned = $this->nuclearCleanResponse($parsed);
                return $cleaned;
            }
        }

        // Fallback: return raw response if JSON parsing fails
        return [
            'error' => false,
            'raw_response' => $response,
            'overall_band_score' => 0,
            'message' => 'Response received but could not parse structured data'
        ];
    }

    /**
     * Clean JSON response from potential formatting issues
     */
    private function cleanJsonResponse($response) {
        // Remove markdown code blocks if present
        $response = preg_replace('/```json\s*/', '', $response);
        $response = preg_replace('/```\s*$/', '', $response);

        // Remove any leading/trailing whitespace
        $response = trim($response);

        return $response;
    }

    /**
     * Nuclear option - completely rebuild response with guaranteed clean text
     */
    private function nuclearCleanResponse($data) {
        $cleanedData = [
            'overall_band_score' => $data['overall_band_score'] ?? 0,
            'highlighted_corrections' => [],
            'annotated_essay' => $this->ultraCleanText($data['annotated_essay'] ?? ''),
            'error' => false
        ];

        if (isset($data['highlighted_corrections']) && is_array($data['highlighted_corrections'])) {
            foreach ($data['highlighted_corrections'] as $correction) {
                $originalText = $this->extractPureText($correction['original_text'] ?? '');
                $suggestedText = $this->extractPureText($correction['suggested_correction'] ?? '');
                $explanation = $this->extractPureText($correction['explanation'] ?? '');

                // Only include if we have valid clean text
                if (!empty($originalText) && !empty($suggestedText) && strlen($originalText) > 2) {
                    $cleanedData['highlighted_corrections'][] = [
                        'original_text' => $originalText,
                        'suggested_correction' => $suggestedText,
                        'error_type' => $correction['error_type'] ?? 'grammar',
                        'explanation' => $explanation,
                        'severity' => $correction['severity'] ?? 'medium'
                    ];
                }
            }
        }

        return $cleanedData;
    }

    /**
     * Extract pure text - ULTRA aggressive cleaning
     */
    private function extractPureText($text) {
        if (empty($text)) {
            return '';
        }

        // Step 1: Remove all HTML-like content aggressively
        $text = preg_replace('/<[^>]*>/', '', $text);
        $text = preg_replace('/data-[^=]*="[^"]*"/', '', $text);
        $text = preg_replace('/class="[^"]*"/', '', $text);
        $text = preg_replace('/title="[^"]*"/', '', $text);
        $text = preg_replace('/correction-[^=]*="[^"]*"/', '', $text);

        // Step 2: Standard cleaning
        $text = strip_tags($text);
        $text = html_entity_decode($text, ENT_QUOTES | ENT_HTML5, 'UTF-8');
        $text = preg_replace('/\s+/', ' ', $text);
        $text = trim($text);

        // Step 3: Final validation - reject if still contains artifacts
        if (strpos($text, 'data-') !== false ||
            strpos($text, '<') !== false ||
            strpos($text, 'correction-') !== false ||
            strpos($text, 'title=') !== false) {
            return '';
        }

        return $text;
    }

    /**
     * Ultra clean text - remove all possible HTML artifacts
     */
    private function ultraCleanText($text) {
        if (empty($text)) {
            return '';
        }

        // Remove all HTML tags and attributes
        $text = strip_tags($text);
        $text = html_entity_decode($text, ENT_QUOTES | ENT_HTML5, 'UTF-8');

        // Remove any remaining HTML-like patterns
        $text = preg_replace('/\s+/', ' ', $text);
        $text = trim($text);

        return $text;
    }

    /**
     * Generate fallback scoring when API is unavailable
     */
    public function generateFallbackScoring($essay, $taskType, $essayQuestion, $timeLimit = 40) {
        $wordCount = str_word_count($essay);
        $essayLength = strlen($essay);

        // Basic scoring based on word count and length
        $baseScore = 4.0;

        if ($wordCount >= 250 && $taskType === 'task2') {
            $baseScore = 5.5;
        } elseif ($wordCount >= 150 && in_array($taskType, ['task1_academic', 'task1_general'])) {
            $baseScore = 5.0;
        }

        // Add some variation
        $variation = rand(-5, 10) / 10;
        $overallScore = max(1.0, min(9.0, $baseScore + $variation));

        return [
            'overall_band_score' => $overallScore,
            'criteria_scores' => [
                'task_achievement' => max(1.0, min(9.0, $overallScore + rand(-3, 3) / 10)),
                'coherence_cohesion' => max(1.0, min(9.0, $overallScore + rand(-3, 3) / 10)),
                'lexical_resource' => max(1.0, min(9.0, $overallScore + rand(-3, 3) / 10)),
                'grammar_accuracy' => max(1.0, min(9.0, $overallScore + rand(-3, 3) / 10))
            ],
            'detailed_feedback' => [
                'task_achievement' => [
                    'score' => $overallScore,
                    'feedback' => 'Hệ thống đang trong chế độ demo. Đây là kết quả mẫu.',
                    'issues' => ['Đây là phản hồi demo'],
                    'improvements' => ['Vui lòng thử lại sau khi hệ thống được khôi phục']
                ]
            ],
            'highlighted_corrections' => [],
            'criteria_analysis' => [],
            'statistics' => [
                'total_errors' => rand(5, 15),
                'grammar_errors' => rand(2, 8),
                'spelling_errors' => rand(1, 5),
                'vocabulary_errors' => rand(1, 4),
                'sentence_variety' => 'medium',
                'complexity_level' => 'intermediate'
            ],
            'metadata' => [
                'task_type' => $taskType,
                'word_count' => $wordCount,
                'scored_at' => date('Y-m-d H:i:s'),
                'essay_length' => $essayLength,
                'time_limit' => $timeLimit,
                'minimum_words' => $taskType === 'task2' ? 250 : 150,
                'fallback_mode' => true
            ]
        ];
    }
}
