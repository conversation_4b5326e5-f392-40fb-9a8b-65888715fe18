<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Services\HashidService;
use App\Traits\VietnamTimezone;

class ScoringAttempt extends Model
{
    use VietnamTimezone;
    protected $fillable = [
        'user_id',
        'essay_question_id',
        'essay_content',
        'task_type',
        'time_limit',
        'overall_band_score',
        'task_achievement',
        'coherence_cohesion',
        'lexical_resource',
        'grammar_accuracy',
        'detailed_feedback',
        'highlighted_corrections',
        'criteria_analysis',
        'statistics',
        'word_count',
        'essay_length',
        'status',
        'error_message',
        'rescored_at',
    ];

    protected $casts = [
        'detailed_feedback' => 'array',
        'highlighted_corrections' => 'array',
        'criteria_analysis' => 'array',
        'statistics' => 'array',
        'overall_band_score' => 'decimal:1',
        'task_achievement' => 'decimal:1',
        'coherence_cohesion' => 'decimal:1',
        'lexical_resource' => 'decimal:1',
        'grammar_accuracy' => 'decimal:1',
        'rescored_at' => 'datetime',
    ];

    /**
     * Get the user that owns the scoring attempt
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the essay question
     */
    public function essayQuestion(): BelongsTo
    {
        return $this->belongsTo(EssayQuestion::class);
    }

    /**
     * Check if scoring is completed
     */
    public function isCompleted(): bool
    {
        return $this->status === 'completed';
    }

    /**
     * Check if scoring failed
     */
    public function isFailed(): bool
    {
        return $this->status === 'failed';
    }

    /**
     * Get band score description
     */
    public function getBandDescription(): string
    {
        if (!$this->overall_band_score) {
            return 'Not scored yet';
        }

        $score = (float) $this->overall_band_score;

        if ($score >= 8.5) {
            return 'Expert User';
        } elseif ($score >= 7.5) {
            return 'Very Good User';
        } elseif ($score >= 6.5) {
            return 'Competent User';
        } elseif ($score >= 5.5) {
            return 'Modest User';
        } elseif ($score >= 4.5) {
            return 'Limited User';
        } elseif ($score >= 3.5) {
            return 'Extremely Limited User';
        } else {
            return 'Intermittent User';
        }
    }

    /**
     * Calculate CEFR Level based on overall band score
     */
    public function getCefrLevel(): string
    {
        if (!$this->overall_band_score) {
            return 'A1';
        }

        $score = (float) $this->overall_band_score;

        // Map IELTS band scores to CEFR levels
        if ($score >= 8.5) return 'C2';
        if ($score >= 7.0) return 'C1';
        if ($score >= 5.5) return 'B2';
        if ($score >= 4.0) return 'B1';
        if ($score >= 3.0) return 'A2';

        return 'A1';
    }

    /**
     * Get hashid for this attempt
     */
    public function getHashid(): string
    {
        return HashidService::encodeAttempt($this->id);
    }

    /**
     * Generate share URL for this attempt
     */
    public function getShareUrl(): string
    {
        return route('scoring.show', ['attempt' => $this->getHashid()]);
    }

    /**
     * Get route key name for model binding
     */
    public function getRouteKeyName(): string
    {
        return 'hashid';
    }

    /**
     * Retrieve the model for a bound value
     */
    public function resolveRouteBinding($value, $field = null)
    {
        // If it's numeric, it's the old ID format
        if (is_numeric($value)) {
            return $this->where('id', $value)->first();
        }

        // If it's a hashid, decode it
        $id = HashidService::decodeAttempt($value);

        if ($id) {
            return $this->where('id', $id)->first();
        }

        return null;
    }

    /**
     * Check if this attempt can be shared publicly
     */
    public function canBeShared(): bool
    {
        return $this->isCompleted() && $this->overall_band_score !== null;
    }
}
