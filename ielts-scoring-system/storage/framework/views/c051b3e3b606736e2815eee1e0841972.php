<?php $__env->startSection('title', 'Thông tin cá nhân'); ?>

<?php $__env->startSection('content'); ?>
<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- Profile Header -->
            <div class="profile-header" data-aos="fade-up">
                <div class="profile-avatar">
                    <div class="avatar-circle">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="avatar-badge">
                        <?php if($user->role === 'admin'): ?>
                            <i class="fas fa-crown"></i>
                        <?php else: ?>
                            <i class="fas fa-check-circle"></i>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="profile-info">
                    <h1 class="profile-name"><?php echo e($user->name); ?></h1>
                    <p class="profile-email"><?php echo e($user->email); ?></p>
                    <div class="profile-meta">
                        <span class="role-badge role-<?php echo e($user->role); ?>">
                            <?php echo e(ucfirst($user->role)); ?>

                        </span>
                        <?php if($user->credit): ?>
                            <span class="credits-badge">
                                <i class="fas fa-coins"></i>
                                <?php echo e($user->getAvailableCredits()); ?> credits
                            </span>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="profile-actions">
                    <a href="<?php echo e(route('profile.edit')); ?>" class="btn btn-primary">
                        <i class="fas fa-edit"></i>
                        Chỉnh sửa
                    </a>
                    <a href="<?php echo e(route('profile.change-password')); ?>" class="btn btn-outline-primary">
                        <i class="fas fa-key"></i>
                        Đổi mật khẩu
                    </a>
                </div>
            </div>

            <!-- Profile Stats -->
            <div class="row g-4 mb-4" data-aos="fade-up" data-aos-delay="100">
                <div class="col-md-4">
                    <div class="stat-card">
                        <div class="stat-icon bg-gradient-primary">
                            <i class="fas fa-file-alt"></i>
                        </div>
                        <div class="stat-content">
                            <h3><?php echo e($user->ieltsAttempts()->count()); ?></h3>
                            <p>Bài thi đã làm</p>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="stat-card">
                        <div class="stat-icon bg-gradient-success">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-content">
                            <h3><?php echo e($user->ieltsAttempts()->whereNotNull('overall_score')->count()); ?></h3>
                            <p>Bài đã chấm</p>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="stat-card">
                        <div class="stat-icon bg-gradient-warning">
                            <i class="fas fa-star"></i>
                        </div>
                        <div class="stat-content">
                            <?php
                                $avgScore = $user->ieltsAttempts()->whereNotNull('overall_score')->avg('overall_score');
                            ?>
                            <h3><?php echo e($avgScore ? number_format($avgScore, 1) : '0.0'); ?></h3>
                            <p>Điểm trung bình</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Account Information -->
            <div class="profile-section" data-aos="fade-up" data-aos-delay="200">
                <div class="section-header">
                    <h2 class="section-title">
                        <i class="fas fa-user-circle"></i>
                        Thông tin tài khoản
                    </h2>
                </div>

                <div class="info-grid">
                    <div class="info-item">
                        <label class="info-label">Họ và tên</label>
                        <div class="info-value"><?php echo e($user->name); ?></div>
                    </div>

                    <div class="info-item">
                        <label class="info-label">Email</label>
                        <div class="info-value"><?php echo e($user->email); ?></div>
                    </div>

                    <div class="info-item">
                        <label class="info-label">Vai trò</label>
                        <div class="info-value">
                            <span class="role-badge role-<?php echo e($user->role); ?>">
                                <?php echo e($user->role === 'admin' ? 'Quản trị viên' : 'Người dùng'); ?>

                            </span>
                        </div>
                    </div>

                    <div class="info-item">
                        <label class="info-label">Ngày tham gia</label>
                        <div class="info-value"><?php echo e($user->created_at->format('d/m/Y')); ?></div>
                    </div>

                    <?php if($user->credit): ?>
                        <div class="info-item">
                            <label class="info-label">Credits khả dụng</label>
                            <div class="info-value">
                                <span class="credits-display">
                                    <i class="fas fa-coins"></i>
                                    <?php echo e($user->getAvailableCredits()); ?> credits
                                </span>
                            </div>
                        </div>

                        <div class="info-item">
                            <label class="info-label">Tổng credits đã sử dụng</label>
                            <div class="info-value">
                                <span class="credits-used">
                                    <?php echo e($user->credit->total_credits - $user->getAvailableCredits()); ?> credits
                                </span>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="profile-section" data-aos="fade-up" data-aos-delay="300">
                <div class="section-header">
                    <h2 class="section-title">
                        <i class="fas fa-history"></i>
                        Hoạt động gần đây
                    </h2>
                    <a href="<?php echo e(route('scoring.history')); ?>" class="section-link">
                        Xem tất cả
                        <i class="fas fa-arrow-right"></i>
                    </a>
                </div>

                <div class="activity-list">
                    <?php $__empty_1 = true; $__currentLoopData = $user->ieltsAttempts()->latest()->take(5)->get(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $attempt): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <div class="activity-item">
                            <div class="activity-icon">
                                <?php if($attempt->overall_score): ?>
                                    <i class="fas fa-check-circle text-success"></i>
                                <?php else: ?>
                                    <i class="fas fa-clock text-warning"></i>
                                <?php endif; ?>
                            </div>

                            <div class="activity-content">
                                <div class="activity-title">
                                    <?php echo e($attempt->task_type === 'task1' ? 'IELTS Writing Task 1' : 'IELTS Writing Task 2'); ?>

                                </div>
                                <div class="activity-meta">
                                    <?php echo e($attempt->created_at->diffForHumans()); ?>

                                    <?php if($attempt->overall_score): ?>
                                        • Band <?php echo e($attempt->overall_score); ?>

                                    <?php else: ?>
                                        • Đang xử lý
                                    <?php endif; ?>
                                </div>
                            </div>

                            <?php if($attempt->overall_score): ?>
                                <div class="activity-score">
                                    <span class="score-badge"><?php echo e($attempt->overall_score); ?></span>
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <div class="empty-state">
                            <i class="fas fa-file-alt"></i>
                            <h4>Chưa có hoạt động nào</h4>
                            <p>Bắt đầu làm bài thi IELTS Writing đầu tiên của bạn</p>
                            <a href="<?php echo e(route('scoring.index')); ?>" class="btn btn-primary">
                                <i class="fas fa-plus"></i>
                                Làm bài thi mới
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php if(session('success')): ?>
    <div class="toast-container position-fixed bottom-0 end-0 p-3">
        <div class="toast show" role="alert">
            <div class="toast-header">
                <i class="fas fa-check-circle text-success me-2"></i>
                <strong class="me-auto">Thành công</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body">
                <?php echo e(session('success')); ?>

            </div>
        </div>
    </div>
<?php endif; ?>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
/* Reset any conflicting styles */
.container {
    max-width: 1200px;
}

.row {
    margin-left: -15px;
    margin-right: -15px;
}

.col-lg-8 {
    padding-left: 15px;
    padding-right: 15px;
}
/* Profile Header */
.profile-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 20px;
    padding: 3rem 2rem;
    color: white;
    margin-bottom: 2rem;
    display: flex;
    align-items: center;
    gap: 2rem;
    position: relative;
    overflow: hidden;
}

.profile-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
    opacity: 0.3;
}

.profile-header > * {
    position: relative;
    z-index: 1;
}

.profile-avatar {
    position: relative;
    flex-shrink: 0;
}

.avatar-circle {
    width: 120px;
    height: 120px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    color: white;
    backdrop-filter: blur(20px);
    border: 3px solid rgba(255, 255, 255, 0.3);
}

.avatar-badge {
    position: absolute;
    bottom: 5px;
    right: 5px;
    width: 35px;
    height: 35px;
    background: #4ade80;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    border: 3px solid white;
    font-size: 0.9rem;
}

.profile-info {
    flex: 1;
}

.profile-name {
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: 0.5rem;
    line-height: 1.2;
}

.profile-email {
    font-size: 1.1rem;
    opacity: 0.9;
    margin-bottom: 1rem;
}

.profile-meta {
    display: flex;
    gap: 1rem;
    align-items: center;
    flex-wrap: wrap;
}

.role-badge {
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-size: 0.85rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.role-admin {
    background: rgba(239, 68, 68, 0.2);
    color: #fca5a5;
    border: 1px solid rgba(239, 68, 68, 0.3);
}

.role-user {
    background: rgba(59, 130, 246, 0.2);
    color: #93c5fd;
    border: 1px solid rgba(59, 130, 246, 0.3);
}

.credits-badge {
    background: rgba(245, 158, 11, 0.2);
    color: #fbbf24;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-size: 0.85rem;
    font-weight: 600;
    border: 1px solid rgba(245, 158, 11, 0.3);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.profile-actions {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.profile-actions .btn {
    min-width: 150px;
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
}

/* Stat Cards */
.stat-card {
    background: white;
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 10px 40px rgba(0,0,0,0.1);
    border: 1px solid rgba(0,0,0,0.05);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 60px rgba(0,0,0,0.15);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    flex-shrink: 0;
}

.stat-content h3 {
    font-size: 2rem;
    font-weight: 800;
    color: #2d3748;
    margin-bottom: 0.25rem;
    line-height: 1;
}

.stat-content p {
    color: #718096;
    font-weight: 500;
    margin: 0;
    font-size: 0.9rem;
}

/* Profile Sections */
.profile-section {
    background: white;
    border-radius: 16px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 10px 40px rgba(0,0,0,0.1);
    border: 1px solid rgba(0,0,0,0.05);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e2e8f0;
}

.section-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #2d3748;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin: 0;
}

.section-link {
    color: #667eea;
    text-decoration: none;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
}

.section-link:hover {
    color: #764ba2;
    transform: translateX(5px);
}

/* Info Grid */
.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.info-item {
    padding: 1.5rem;
    background: #f8fafc;
    border-radius: 12px;
    border: 1px solid #e2e8f0;
}

.info-label {
    display: block;
    font-size: 0.85rem;
    font-weight: 600;
    color: #718096;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 0.5rem;
}

.info-value {
    font-size: 1rem;
    font-weight: 600;
    color: #2d3748;
}

.credits-display {
    color: #f59e0b;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.credits-used {
    color: #6b7280;
}

/* Activity List */
.activity-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
    background: #f8fafc;
    border-radius: 12px;
    border: 1px solid #e2e8f0;
    transition: all 0.3s ease;
}

.activity-item:hover {
    background: #f1f5f9;
    transform: translateX(5px);
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    flex-shrink: 0;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.activity-content {
    flex: 1;
}

.activity-title {
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 0.25rem;
}

.activity-meta {
    font-size: 0.85rem;
    color: #718096;
}

.activity-score {
    flex-shrink: 0;
}

.score-badge {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 700;
    font-size: 0.9rem;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 3rem 2rem;
    color: #718096;
}

.empty-state i {
    font-size: 3rem;
    color: #cbd5e0;
    margin-bottom: 1rem;
}

.empty-state h4 {
    font-size: 1.2rem;
    font-weight: 600;
    color: #4a5568;
    margin-bottom: 0.5rem;
}

.empty-state p {
    margin-bottom: 2rem;
}

/* Responsive */
@media (max-width: 768px) {
    .profile-header {
        flex-direction: column;
        text-align: center;
        gap: 1.5rem;
        padding: 2rem 1.5rem;
    }

    .profile-name {
        font-size: 2rem;
    }

    .avatar-circle {
        width: 100px;
        height: 100px;
        font-size: 2.5rem;
    }

    .profile-actions {
        flex-direction: row;
        justify-content: center;
        flex-wrap: wrap;
    }

    .info-grid {
        grid-template-columns: 1fr;
    }

    .section-header {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }
}
</style>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\ielts\ielts-scoring-system\resources\views/profile/show.blade.php ENDPATH**/ ?>