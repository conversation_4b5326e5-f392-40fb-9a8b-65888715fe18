<?php $__env->startSection('title', 'Chỉnh sửa người dùng - Admin'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid py-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">Chỉnh sửa người dùng</h1>
                    <p class="text-muted">Cập nhật thông tin cho <?php echo e($user->name); ?></p>
                </div>
                <div>
                    <a href="<?php echo e(route('admin.users.index')); ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Quay lại
                    </a>
                </div>
            </div>

            <?php if(session('success')): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i><?php echo e(session('success')); ?>

                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <div class="card border-0 shadow-sm">
                <div class="card-body p-4">
                    <form method="POST" action="<?php echo e(route('admin.users.update', $user)); ?>">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('PUT'); ?>

                        <!-- Name -->
                        <div class="mb-3">
                            <label for="name" class="form-label fw-medium">Họ và tên <span class="text-danger">*</span></label>
                            <input type="text" class="form-control <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                   id="name" name="name" value="<?php echo e(old('name', $user->name)); ?>" required autofocus>
                            <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <!-- Email -->
                        <div class="mb-3">
                            <label for="email" class="form-label fw-medium">Email <span class="text-danger">*</span></label>
                            <input type="email" class="form-control <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                   id="email" name="email" value="<?php echo e(old('email', $user->email)); ?>" required>
                            <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <!-- Role -->
                        <div class="mb-3">
                            <label for="role" class="form-label fw-medium">Vai trò <span class="text-danger">*</span></label>
                            <select class="form-select <?php $__errorArgs = ['role'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="role" name="role" required>
                                <option value="user" <?php echo e(old('role', $user->role) === 'user' ? 'selected' : ''); ?>>User</option>
                                <option value="admin" <?php echo e(old('role', $user->role) === 'admin' ? 'selected' : ''); ?>>Admin</option>
                            </select>
                            <?php $__errorArgs = ['role'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            <?php if($user->id === auth()->id()): ?>
                                <div class="form-text text-warning">
                                    <i class="fas fa-exclamation-triangle me-1"></i>
                                    Bạn đang chỉnh sửa tài khoản của chính mình.
                                </div>
                            <?php endif; ?>
                        </div>

                        <!-- Active Status -->
                        <div class="mb-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active"
                                       <?php echo e(old('is_active', $user->is_active) ? 'checked' : ''); ?>>
                                <label class="form-check-label fw-medium" for="is_active">
                                    Tài khoản hoạt động
                                </label>
                            </div>
                            <div class="form-text">
                                <i class="fas fa-info-circle me-1"></i>
                                Bỏ tick để tạm khóa tài khoản. Người dùng sẽ không thể đăng nhập.
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="d-flex justify-content-end gap-2">
                            <a href="<?php echo e(route('admin.users.index')); ?>" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i>Hủy
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Cập nhật
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- User Info Card -->
            <div class="card border-0 shadow-sm mt-4">
                <div class="card-header bg-light border-0">
                    <h6 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>Thông tin tài khoản
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <div class="d-flex justify-content-between">
                                <span class="text-muted">ID:</span>
                                <span class="fw-medium"><?php echo e($user->id); ?></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex justify-content-between">
                                <span class="text-muted">Ngày tạo:</span>
                                <span class="fw-medium"><?php echo e($user->created_at->format('d/m/Y H:i')); ?></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex justify-content-between">
                                <span class="text-muted">Cập nhật cuối:</span>
                                <span class="fw-medium"><?php echo e($user->updated_at->format('d/m/Y H:i')); ?></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex justify-content-between">
                                <span class="text-muted">Email verified:</span>
                                <span class="fw-medium">
                                    <?php if($user->email_verified_at): ?>
                                        <span class="badge bg-success">Đã xác thực</span>
                                    <?php else: ?>
                                        <span class="badge bg-warning">Chưa xác thực</span>
                                    <?php endif; ?>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Credits Management -->
            <?php if($user->credit): ?>
            <div class="card border-0 shadow-sm mt-4">
                <div class="card-header bg-light border-0">
                    <div class="d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">
                            <i class="fas fa-coins me-2"></i>Quản lý Credits
                        </h6>
                        <button type="button" class="btn btn-sm btn-success"
                                onclick="showAddCreditsModal(<?php echo e($user->id); ?>, '<?php echo e($user->name); ?>')">
                            <i class="fas fa-plus me-1"></i>Thêm Credits
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-4">
                            <div class="text-center">
                                <div class="h4 text-primary"><?php echo e($user->credit->credits); ?></div>
                                <div class="text-muted small">Tổng credits</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center">
                                <div class="h4 text-danger"><?php echo e($user->credit->used_credits); ?></div>
                                <div class="text-muted small">Đã sử dụng</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center">
                                <div class="h4 text-success"><?php echo e($user->getAvailableCredits()); ?></div>
                                <div class="text-muted small">Còn lại</div>
                            </div>
                        </div>
                    </div>
                    <?php if($user->credit->notes): ?>
                        <div class="mt-3">
                            <small class="text-muted">
                                <strong>Ghi chú:</strong> <?php echo e($user->credit->notes); ?>

                            </small>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            <?php endif; ?>

            <!-- Password Reset -->
            <div class="card border-0 shadow-sm mt-4">
                <div class="card-header bg-light border-0">
                    <h6 class="mb-0">
                        <i class="fas fa-key me-2"></i>Đặt lại mật khẩu
                    </h6>
                </div>
                <div class="card-body">
                    <p class="text-muted mb-3">
                        Để đặt lại mật khẩu cho người dùng này, hãy sử dụng chức năng "Quên mật khẩu"
                        hoặc yêu cầu người dùng thay đổi mật khẩu sau khi đăng nhập.
                    </p>
                    <button class="btn btn-outline-warning" onclick="alert('Tính năng đang phát triển')">
                        <i class="fas fa-key me-2"></i>Gửi email đặt lại mật khẩu
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Credits Modal -->
<div class="modal fade" id="addCreditsModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Thêm Credits</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="addCreditsForm" method="POST">
                <?php echo csrf_field(); ?>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Người dùng</label>
                        <input type="text" class="form-control" id="userName" value="<?php echo e($user->name); ?>" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Số credits thêm <span class="text-danger">*</span></label>
                        <input type="number" class="form-control" name="credits" min="1" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Ghi chú</label>
                        <textarea class="form-control" name="notes" rows="3" placeholder="Lý do thêm credits..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-coins me-2"></i>Thêm Credits
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
function showAddCreditsModal(userId, userName) {
    $('#addCreditsForm').attr('action', `/admin/users/${userId}/add-credits`);
    $('#addCreditsModal').modal('show');
}
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\ielts\ielts-scoring-system\resources\views/admin/users/edit.blade.php ENDPATH**/ ?>