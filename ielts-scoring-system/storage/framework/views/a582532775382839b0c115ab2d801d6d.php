<?php $__env->startSection('title', 'Dashboard - IELTS AI Scoring System'); ?>

<?php $__env->startSection('content'); ?>
<div class="modern-dashboard">
    <!-- Hero Section -->
    <div class="hero-section">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <div class="hero-content">
                        <h1 class="hero-title">
                            <span class="greeting">Chào mừng trở lại,</span>
                            <span class="user-name"><?php echo e(auth()->user()->name); ?>!</span>
                        </h1>
                        <p class="hero-subtitle">
                            H<PERSON>y tiếp tục hành trình chinh phục IELTS Writing của bạn
                        </p>
                        <div class="hero-actions">
                            <a href="<?php echo e(route('scoring.create')); ?>" class="btn-modern btn-primary-modern">
                                <i class="fas fa-edit"></i>
                                <span>Chấm thi mới</span>
                            </a>
                            <a href="<?php echo e(route('scoring.history')); ?>" class="btn-modern btn-secondary-modern">
                                <i class="fas fa-history"></i>
                                <span>Xem lịch sử</span>
                            </a>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4">
                    <div class="hero-illustration">
                        <div class="floating-card">
                            <i class="fas fa-graduation-cap"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="container-fluid">
        <div class="stats-section">
            <div class="row g-4">
                <div class="col-xl-3 col-md-6">
                    <div class="stat-card-modern" data-aos="fade-up" data-aos-delay="100">
                        <div class="stat-icon-wrapper">
                            <div class="stat-icon bg-gradient-primary">
                                <i class="fas fa-file-alt"></i>
                            </div>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number"><?php echo e($totalAttempts); ?></div>
                            <div class="stat-label">Tổng số bài thi</div>
                            <div class="stat-trend">
                                <i class="fas fa-arrow-up"></i>
                                <span>+12% so với tháng trước</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6">
                    <div class="stat-card-modern" data-aos="fade-up" data-aos-delay="200">
                        <div class="stat-icon-wrapper">
                            <div class="stat-icon bg-gradient-success">
                                <i class="fas fa-check-circle"></i>
                            </div>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number"><?php echo e($completedAttempts); ?></div>
                            <div class="stat-label">Bài đã hoàn thành</div>
                            <div class="stat-trend">
                                <i class="fas fa-arrow-up"></i>
                                <span>+8% so với tháng trước</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6">
                    <div class="stat-card-modern" data-aos="fade-up" data-aos-delay="300">
                        <div class="stat-icon-wrapper">
                            <div class="stat-icon bg-gradient-warning">
                                <i class="fas fa-star"></i>
                            </div>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number">
                                <?php if($averageScore): ?>
                                    <?php echo e(number_format($averageScore, 1)); ?>

                                <?php else: ?>
                                    -
                                <?php endif; ?>
                            </div>
                            <div class="stat-label">Điểm trung bình</div>
                            <div class="stat-trend">
                                <i class="fas fa-arrow-up"></i>
                                <span>+0.3 so với tháng trước</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6">
                    <div class="stat-card-modern" data-aos="fade-up" data-aos-delay="400">
                        <div class="stat-icon-wrapper">
                            <div class="stat-icon bg-gradient-info">
                                <i class="fas fa-coins"></i>
                            </div>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number"><?php echo e($availableCredits); ?></div>
                            <div class="stat-label">Credits còn lại</div>
                            <div class="stat-trend">
                                <i class="fas fa-info-circle"></i>
                                <span>Đủ cho <?php echo e(floor($availableCredits)); ?> bài thi</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Attempts -->
    <div class="container-fluid">
        <div class="recent-attempts-section">
            <div class="row">
                <div class="col-12">
                    <div class="modern-card" data-aos="fade-up" data-aos-delay="500">
                        <div class="modern-card-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="section-title">
                                    <h3 class="title">Bài thi gần đây</h3>
                                    <p class="subtitle">Theo dõi tiến độ học tập của bạn</p>
                                </div>
                                <a href="<?php echo e(route('scoring.history')); ?>" class="btn-modern btn-outline-modern">
                                    <i class="fas fa-history"></i>
                                    <span>Xem tất cả</span>
                                </a>
                            </div>
                        </div>
                        <div class="modern-card-body">
                            <?php if($recentAttempts->count() > 0): ?>
                                <div class="attempts-list">
                                    <?php $__currentLoopData = $recentAttempts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $attempt): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="attempt-item" data-aos="fade-up" data-aos-delay="<?php echo e(600 + ($index * 100)); ?>">
                                        <div class="attempt-content">
                                            <div class="attempt-info">
                                                <div class="attempt-title">
                                                    <?php if($attempt->essayQuestion): ?>
                                                        <?php echo e(Str::limit($attempt->essayQuestion->title, 50)); ?>

                                                    <?php else: ?>
                                                        Câu hỏi tự do
                                                    <?php endif; ?>
                                                </div>
                                                <div class="attempt-meta">
                                                    <span class="task-type">
                                                        <i class="fas fa-tag"></i>
                                                        <?php echo e(ucfirst(str_replace('_', ' ', $attempt->task_type))); ?>

                                                    </span>
                                                    <span class="attempt-date">
                                                        <i class="fas fa-calendar"></i>
                                                        <?php echo e($attempt->created_at_vn); ?>

                                                    </span>
                                                </div>
                                            </div>

                                            <div class="attempt-score">
                                                <?php if($attempt->isCompleted()): ?>
                                                    <div class="score-display">
                                                        <div class="score-number"><?php echo e($attempt->overall_band_score); ?></div>
                                                        <div class="score-label">Band Score</div>
                                                    </div>
                                                <?php else: ?>
                                                    <div class="score-pending">
                                                        <i class="fas fa-clock"></i>
                                                        <span>Đang xử lý</span>
                                                    </div>
                                                <?php endif; ?>
                                            </div>

                                            <div class="attempt-status">
                                                <?php if($attempt->status === 'completed'): ?>
                                                    <span class="status-badge status-success">
                                                        <i class="fas fa-check"></i>
                                                        Hoàn thành
                                                    </span>
                                                <?php elseif($attempt->status === 'pending'): ?>
                                                    <span class="status-badge status-warning">
                                                        <i class="fas fa-clock"></i>
                                                        Đang xử lý
                                                    </span>
                                                <?php else: ?>
                                                    <span class="status-badge status-danger">
                                                        <i class="fas fa-times"></i>
                                                        Lỗi
                                                    </span>
                                                <?php endif; ?>
                                            </div>

                                            <div class="attempt-actions">
                                                <?php if($attempt->isCompleted()): ?>
                                                    <a href="<?php echo e(route('scoring.show', $attempt->getHashid())); ?>" class="btn-action btn-view">
                                                        <i class="fas fa-eye"></i>
                                                        <span>Xem chi tiết</span>
                                                    </a>
                                                <?php elseif($attempt->status === 'pending'): ?>
                                                    <button class="btn-action btn-disabled" disabled>
                                                        <i class="fas fa-spinner fa-spin"></i>
                                                        <span>Đang xử lý</span>
                                                    </button>
                                                <?php else: ?>
                                                    <span class="error-text">
                                                        <i class="fas fa-exclamation-triangle"></i>
                                                        Có lỗi xảy ra
                                                    </span>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                            <?php else: ?>
                                <div class="empty-state">
                                    <div class="empty-illustration">
                                        <i class="fas fa-file-alt"></i>
                                    </div>
                                    <h4 class="empty-title">Chưa có bài thi nào</h4>
                                    <p class="empty-subtitle">Bắt đầu hành trình chinh phục IELTS Writing của bạn ngay hôm nay</p>
                                    <a href="<?php echo e(route('scoring.create')); ?>" class="btn-modern btn-primary-modern">
                                        <i class="fas fa-edit"></i>
                                        <span>Chấm thi ngay</span>
                                    </a>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="container-fluid">
        <div class="quick-actions-section">
            <div class="row">
                <div class="col-12">
                    <div class="modern-card" data-aos="fade-up" data-aos-delay="700">
                        <div class="modern-card-header">
                            <div class="section-title">
                                <h3 class="title">Hành động nhanh</h3>
                                <p class="subtitle">Các tính năng thường dùng</p>
                            </div>
                        </div>
                        <div class="modern-card-body">
                            <div class="row g-4">
                                <div class="col-lg-4 col-md-6">
                                    <a href="<?php echo e(route('scoring.create')); ?>" class="action-card" data-aos="zoom-in" data-aos-delay="800">
                                        <div class="action-icon bg-gradient-primary">
                                            <i class="fas fa-edit"></i>
                                        </div>
                                        <div class="action-content">
                                            <h4 class="action-title">Chấm thi mới</h4>
                                            <p class="action-description">Bắt đầu bài thi IELTS Writing mới</p>
                                        </div>
                                        <div class="action-arrow">
                                            <i class="fas fa-arrow-right"></i>
                                        </div>
                                    </a>
                                </div>
                                <div class="col-lg-4 col-md-6">
                                    <a href="<?php echo e(route('scoring.history')); ?>" class="action-card" data-aos="zoom-in" data-aos-delay="900">
                                        <div class="action-icon bg-gradient-success">
                                            <i class="fas fa-history"></i>
                                        </div>
                                        <div class="action-content">
                                            <h4 class="action-title">Lịch sử</h4>
                                            <p class="action-description">Xem tất cả bài thi đã làm</p>
                                        </div>
                                        <div class="action-arrow">
                                            <i class="fas fa-arrow-right"></i>
                                        </div>
                                    </a>
                                </div>
                                <div class="col-lg-4 col-md-6">
                                    <div class="action-card action-disabled" data-aos="zoom-in" data-aos-delay="1000">
                                        <div class="action-icon bg-gradient-info">
                                            <i class="fas fa-chart-line"></i>
                                        </div>
                                        <div class="action-content">
                                            <h4 class="action-title">Thống kê</h4>
                                            <p class="action-description">Xem tiến độ học tập (Sắp ra mắt)</p>
                                        </div>
                                        <div class="action-arrow">
                                            <i class="fas fa-clock"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Modern Dashboard Styles */
:root {
    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --gradient-warning: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    --gradient-info: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    --shadow-soft: 0 10px 40px rgba(0,0,0,0.1);
    --shadow-medium: 0 20px 60px rgba(0,0,0,0.15);
    --border-radius: 20px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modern-dashboard {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
    padding: 0;
}

/* Hero Section */
.hero-section {
    background: var(--gradient-primary);
    padding: 80px 0;
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.hero-content {
    position: relative;
    z-index: 2;
}

.hero-title {
    color: white;
    font-size: 3.5rem;
    font-weight: 800;
    margin-bottom: 1rem;
    line-height: 1.2;
}

.greeting {
    display: block;
    font-size: 1.2rem;
    font-weight: 400;
    opacity: 0.9;
    margin-bottom: 0.5rem;
}

.user-name {
    background: linear-gradient(45deg, #fff, #f0f8ff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    color: rgba(255,255,255,0.9);
    font-size: 1.2rem;
    margin-bottom: 2rem;
    font-weight: 300;
}

.hero-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.hero-illustration {
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
}

.floating-card {
    width: 200px;
    height: 200px;
    background: rgba(255,255,255,0.1);
    backdrop-filter: blur(20px);
    border-radius: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 4rem;
    color: white;
    animation: float 6s ease-in-out infinite;
    border: 1px solid rgba(255,255,255,0.2);
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(5deg); }
}

/* Modern Buttons */
.btn-modern {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 2rem;
    border-radius: 50px;
    font-weight: 600;
    text-decoration: none;
    transition: var(--transition);
    border: none;
    cursor: pointer;
    font-size: 1rem;
}

.btn-primary-modern {
    background: rgba(255,255,255,0.2);
    color: white;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255,255,255,0.3);
}

.btn-primary-modern:hover {
    background: rgba(255,255,255,0.3);
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    color: white;
}

.btn-secondary-modern {
    background: transparent;
    color: white;
    border: 2px solid rgba(255,255,255,0.3);
}

.btn-secondary-modern:hover {
    background: rgba(255,255,255,0.1);
    border-color: rgba(255,255,255,0.5);
    transform: translateY(-2px);
    color: white;
}

.btn-outline-modern {
    background: transparent;
    color: #667eea;
    border: 2px solid #667eea;
    padding: 0.75rem 1.5rem;
}

.btn-outline-modern:hover {
    background: #667eea;
    color: white;
    transform: translateY(-2px);
}

/* Stats Section */
.stats-section {
    margin: -60px 0 60px 0;
    position: relative;
    z-index: 3;
}

.stat-card-modern {
    background: white;
    border-radius: var(--border-radius);
    padding: 2rem;
    box-shadow: var(--shadow-soft);
    transition: var(--transition);
    border: 1px solid rgba(255,255,255,0.8);
    height: 100%;
}

.stat-card-modern:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-medium);
}

.stat-icon-wrapper {
    margin-bottom: 1.5rem;
}

.stat-icon {
    width: 80px;
    height: 80px;
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
    margin-bottom: 1rem;
}

.bg-gradient-primary { background: var(--gradient-primary); }
.bg-gradient-success { background: var(--gradient-success); }
.bg-gradient-warning { background: var(--gradient-warning); }
.bg-gradient-info { background: var(--gradient-info); }

.stat-number {
    font-size: 3rem;
    font-weight: 800;
    color: #2d3748;
    margin-bottom: 0.5rem;
    line-height: 1;
}

.stat-label {
    font-size: 1rem;
    color: #718096;
    font-weight: 600;
    margin-bottom: 1rem;
}

.stat-trend {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: #48bb78;
    font-weight: 500;
}

.stat-trend i {
    font-size: 0.75rem;
}

/* Modern Cards */
.modern-card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-soft);
    border: 1px solid rgba(255,255,255,0.8);
    overflow: hidden;
    transition: var(--transition);
    margin-bottom: 2rem;
}

.modern-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
}

.modern-card-header {
    padding: 2rem 2rem 1rem 2rem;
    border-bottom: 1px solid #f7fafc;
}

.modern-card-body {
    padding: 2rem;
}

.section-title .title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 0.5rem;
}

.section-title .subtitle {
    color: #718096;
    margin: 0;
    font-size: 0.95rem;
}

/* Attempts List */
.attempts-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.attempt-item {
    background: #f8fafc;
    border-radius: 15px;
    padding: 1.5rem;
    transition: var(--transition);
    border: 1px solid #e2e8f0;
}

.attempt-item:hover {
    background: #edf2f7;
    transform: translateX(5px);
}

.attempt-content {
    display: grid;
    grid-template-columns: 2fr auto auto auto;
    gap: 1.5rem;
    align-items: center;
}

.attempt-info .attempt-title {
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
}

.attempt-meta {
    display: flex;
    gap: 1rem;
    font-size: 0.875rem;
    color: #718096;
}

.attempt-meta span {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.score-display {
    text-align: center;
}

.score-number {
    font-size: 2rem;
    font-weight: 800;
    color: #667eea;
    line-height: 1;
}

.score-label {
    font-size: 0.75rem;
    color: #718096;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.score-pending {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #ed8936;
    font-weight: 500;
}

.status-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: 50px;
    font-size: 0.875rem;
    font-weight: 600;
}

.status-success {
    background: #c6f6d5;
    color: #22543d;
}

.status-warning {
    background: #fef5e7;
    color: #c05621;
}

.status-danger {
    background: #fed7d7;
    color: #c53030;
}

.btn-action {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 600;
    transition: var(--transition);
    border: none;
    cursor: pointer;
    font-size: 0.875rem;
}

.btn-view {
    background: var(--gradient-primary);
    color: white;
}

.btn-view:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
    color: white;
}

.btn-disabled {
    background: #e2e8f0;
    color: #a0aec0;
    cursor: not-allowed;
}

.error-text {
    color: #e53e3e;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 4rem 2rem;
}

.empty-illustration {
    width: 120px;
    height: 120px;
    background: linear-gradient(135deg, #e2e8f0, #cbd5e0);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 2rem auto;
    font-size: 3rem;
    color: #a0aec0;
}

.empty-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 1rem;
}

.empty-subtitle {
    color: #718096;
    margin-bottom: 2rem;
    font-size: 1.1rem;
}

/* Action Cards */
.action-card {
    display: block;
    background: white;
    border-radius: var(--border-radius);
    padding: 2rem;
    text-decoration: none;
    transition: var(--transition);
    border: 1px solid #e2e8f0;
    height: 100%;
    position: relative;
    overflow: hidden;
}

.action-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
    text-decoration: none;
}

.action-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
    transform: scaleX(0);
    transition: var(--transition);
}

.action-card:hover::before {
    transform: scaleX(1);
}

.action-icon {
    width: 60px;
    height: 60px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    margin-bottom: 1.5rem;
}

.action-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 0.5rem;
}

.action-description {
    color: #718096;
    margin-bottom: 1.5rem;
    font-size: 0.95rem;
}

.action-arrow {
    position: absolute;
    top: 2rem;
    right: 2rem;
    color: #cbd5e0;
    transition: var(--transition);
}

.action-card:hover .action-arrow {
    color: #667eea;
    transform: translateX(5px);
}

.action-disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.action-disabled:hover {
    transform: none;
    box-shadow: var(--shadow-soft);
}

.action-disabled .action-arrow {
    color: #cbd5e0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }

    .hero-actions {
        flex-direction: column;
    }

    .btn-modern {
        justify-content: center;
    }

    .attempt-content {
        grid-template-columns: 1fr;
        gap: 1rem;
        text-align: center;
    }

    .floating-card {
        width: 150px;
        height: 150px;
        font-size: 3rem;
    }

    .stats-section {
        margin-top: -40px;
    }
}

/* AOS Animation Overrides */
[data-aos] {
    pointer-events: none;
}

[data-aos].aos-animate {
    pointer-events: auto;
}
</style>

<!-- AOS Animation Library -->
<link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
<script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    AOS.init({
        duration: 800,
        easing: 'ease-out-cubic',
        once: true,
        offset: 100
    });
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\ielts\ielts-scoring-system\resources\views/home/<USER>/ ?>