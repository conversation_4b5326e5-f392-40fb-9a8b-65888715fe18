<?php $__env->startSection('title', 'IELTS AI Scoring System'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- Hero Section -->
    <div class="hero-section bg-gradient-primary text-white py-5 mb-5">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <h1 class="display-4 fw-bold mb-4">IELTS AI Scoring System</h1>
                    <p class="lead mb-4">Hệ thống chấm thi IELTS Writing tự động với AI, giúp bạn cải thiện kỹ năng viết một cách hiệu quả và chính xác.</p>
                    <div class="d-flex gap-3">
                        <?php if(auth()->guard()->check()): ?>
                            <a href="<?php echo e(route('scoring.create')); ?>" class="btn btn-light btn-lg">
                                <i class="fas fa-edit me-2"></i>Bắt đầu chấm thi
                            </a>
                            <a href="<?php echo e(route('dashboard')); ?>" class="btn btn-outline-light btn-lg">
                                <i class="fas fa-chart-line me-2"></i>Dashboard
                            </a>
                        <?php else: ?>
                            <a href="<?php echo e(route('register')); ?>" class="btn btn-light btn-lg">
                                <i class="fas fa-user-plus me-2"></i>Đăng ký ngay
                            </a>
                            <a href="<?php echo e(route('login')); ?>" class="btn btn-outline-light btn-lg">
                                <i class="fas fa-sign-in-alt me-2"></i>Đăng nhập
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="text-center">
                        <i class="fas fa-robot fa-10x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Features Section -->
    <div class="container mb-5">
        <div class="row text-center mb-5">
            <div class="col-12">
                <h2 class="display-5 fw-bold mb-3">Tính năng nổi bật</h2>
                <p class="lead text-muted">Hệ thống chấm thi IELTS Writing với công nghệ AI tiên tiến</p>
            </div>
        </div>
        
        <div class="row g-4">
            <div class="col-md-4">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body text-center p-4">
                        <div class="feature-icon bg-primary text-white rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center" style="width: 80px; height: 80px;">
                            <i class="fas fa-brain fa-2x"></i>
                        </div>
                        <h4 class="fw-bold mb-3">AI Chấm thi thông minh</h4>
                        <p class="text-muted">Sử dụng công nghệ AI tiên tiến để chấm thi chính xác theo tiêu chuẩn IELTS quốc tế</p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body text-center p-4">
                        <div class="feature-icon bg-success text-white rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center" style="width: 80px; height: 80px;">
                            <i class="fas fa-chart-line fa-2x"></i>
                        </div>
                        <h4 class="fw-bold mb-3">Phân tích chi tiết</h4>
                        <p class="text-muted">Báo cáo chi tiết về từng tiêu chí: Task Achievement, Coherence, Lexical Resource, Grammar</p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body text-center p-4">
                        <div class="feature-icon bg-warning text-white rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center" style="width: 80px; height: 80px;">
                            <i class="fas fa-lightbulb fa-2x"></i>
                        </div>
                        <h4 class="fw-bold mb-3">Gợi ý cải thiện</h4>
                        <p class="text-muted">Nhận được những gợi ý cụ thể để cải thiện kỹ năng viết và nâng cao band score</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Sample Questions Section -->
    <?php if($questions->count() > 0): ?>
    <div class="container mb-5">
        <div class="row">
            <div class="col-12">
                <h2 class="display-6 fw-bold mb-4">Câu hỏi mẫu</h2>
                <p class="lead text-muted mb-4">Thử sức với các câu hỏi IELTS Writing thực tế</p>
            </div>
        </div>
        
        <div class="row g-4">
            <?php $__currentLoopData = $questions->take(3); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $question): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="col-lg-4">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-header bg-light border-0">
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="badge bg-primary"><?php echo e($question->getTaskTypeLabel()); ?></span>
                            <small class="text-muted"><?php echo e($question->time_limit); ?> phút</small>
                        </div>
                    </div>
                    <div class="card-body">
                        <h5 class="card-title fw-bold"><?php echo e($question->title); ?></h5>
                        <p class="card-text text-muted"><?php echo e(Str::limit($question->question, 150)); ?></p>
                        <div class="mt-auto">
                            <small class="text-muted">Tối thiểu <?php echo e($question->min_words); ?> từ</small>
                        </div>
                    </div>
                    <div class="card-footer bg-transparent border-0">
                        <?php if(auth()->guard()->check()): ?>
                            <a href="<?php echo e(route('scoring.create', ['question_id' => $question->id])); ?>" class="btn btn-primary w-100">
                                <i class="fas fa-edit me-2"></i>Làm bài
                            </a>
                        <?php else: ?>
                            <a href="<?php echo e(route('login')); ?>" class="btn btn-outline-primary w-100">
                                <i class="fas fa-sign-in-alt me-2"></i>Đăng nhập để làm bài
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </div>
    <?php endif; ?>

    <!-- Recent Attempts (for logged in users) -->
    <?php if(auth()->guard()->check()): ?>
    <?php if($recentAttempts->count() > 0): ?>
    <div class="container mb-5">
        <div class="row">
            <div class="col-12">
                <h2 class="display-6 fw-bold mb-4">Bài thi gần đây</h2>
            </div>
        </div>
        
        <div class="row">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Câu hỏi</th>
                                        <th>Loại</th>
                                        <th>Band Score</th>
                                        <th>Trạng thái</th>
                                        <th>Ngày thi</th>
                                        <th>Hành động</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $recentAttempts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $attempt): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td>
                                            <?php if($attempt->essayQuestion): ?>
                                                <?php echo e(Str::limit($attempt->essayQuestion->title, 50)); ?>

                                            <?php else: ?>
                                                <em>Câu hỏi tự do</em>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary"><?php echo e(ucfirst(str_replace('_', ' ', $attempt->task_type))); ?></span>
                                        </td>
                                        <td>
                                            <?php if($attempt->isCompleted()): ?>
                                                <span class="fw-bold text-primary"><?php echo e($attempt->overall_band_score); ?></span>
                                            <?php else: ?>
                                                <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if($attempt->status === 'completed'): ?>
                                                <span class="badge bg-success">Hoàn thành</span>
                                            <?php elseif($attempt->status === 'pending'): ?>
                                                <span class="badge bg-warning">Đang xử lý</span>
                                            <?php else: ?>
                                                <span class="badge bg-danger">Lỗi</span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo e($attempt->created_at->format('d/m/Y H:i')); ?></td>
                                        <td>
                                            <?php if($attempt->isCompleted()): ?>
                                                <a href="<?php echo e(route('scoring.show', $attempt->id)); ?>" class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-eye me-1"></i>Xem
                                                </a>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
    <?php endif; ?>

    <!-- CTA Section -->
    <div class="bg-light py-5">
        <div class="container text-center">
            <h2 class="display-6 fw-bold mb-3">Sẵn sàng cải thiện IELTS Writing?</h2>
            <p class="lead text-muted mb-4">Bắt đầu hành trình nâng cao band score của bạn ngay hôm nay</p>
            <?php if(auth()->guard()->check()): ?>
                <a href="<?php echo e(route('scoring.create')); ?>" class="btn btn-primary btn-lg">
                    <i class="fas fa-rocket me-2"></i>Bắt đầu ngay
                </a>
            <?php else: ?>
                <a href="<?php echo e(route('register')); ?>" class="btn btn-primary btn-lg me-3">
                    <i class="fas fa-user-plus me-2"></i>Đăng ký miễn phí
                </a>
                <a href="<?php echo e(route('login')); ?>" class="btn btn-outline-primary btn-lg">
                    <i class="fas fa-sign-in-alt me-2"></i>Đăng nhập
                </a>
            <?php endif; ?>
        </div>
    </div>
</div>

<style>
.hero-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.feature-icon {
    transition: transform 0.3s ease;
}

.card:hover .feature-icon {
    transform: scale(1.1);
}

.card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.1) !important;
}
</style>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\ielts\ielts-scoring-system\resources\views/home/<USER>/ ?>