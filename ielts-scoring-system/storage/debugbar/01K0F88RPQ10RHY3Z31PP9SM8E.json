{"__meta": {"id": "01K0F88RPQ10RHY3Z31PP9SM8E", "datetime": "2025-07-18 17:10:49", "utime": **********.303695, "method": "GET", "uri": "/share/ZXlKcGRpSTZJbmM0UzIxQk5rdDZkVmxKV20wclVGUjZNRkY0Wm5jOVBTSXNJblpoYkhWbElqb2lOSEI1ZWtkV1owbFFUVkpGTld4NFRGSjRVRW80Y1VWMWN6UldaMFJzUjBzMGF6TTViVmt3V2pCNWVEbDRWWGRKTTA5ck9GRnVlbFpzZVdwcU4yRmhRbWR5WmxGWmRFMTJNMDFDYUhKa2RsTnpLMVF3YkdGYVNEZzFSMUZQTDFFdmNsVkplaTk1ZDBzMFVGUlVkbTlOWmtRME9EUXJlRlJ1SzFoaVlsSTBUbVVpTENKdFlXTWlPaUpsWlRCbE5qRXdZMk00WkRKbE0yRmtNamMyTW1JeE5XSTROekJsTW1VNE16ZGpNVEUxTVRrM09UZzBaRFE1WVRkbU1tVm1ZV1EzTjJRM05URmhNV1EwSWl3aWRHRm5Jam9pSW4wPQ==", "ip": "127.0.0.1"}, "messages": {"count": 2, "messages": [{"message": "[17:10:48] LOG.info: IELTSScorer initialized {\n    \"api_url\": \"https:\\/\\/api.v3.cm\\/v1\\/chat\\/completions\",\n    \"model\": \"o3\",\n    \"api_key_length\": 51\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.9229, "xdebug_link": null, "collector": "log"}, {"message": "[17:10:48] LOG.error: htmlspecialchars(): Argument #1 ($string) must be of type string, array given (View: C:\\laragon\\www\\ielts\\ielts-scoring-system\\resources\\views\\scoring\\public.blade.php) {\n    \"userId\": 3,\n    \"exception\": {}\n}", "message_html": null, "is_string": false, "label": "error", "time": **********.972774, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.780198, "end": **********.303714, "duration": 0.5235159397125244, "duration_str": "524ms", "measures": [{"label": "Booting", "start": **********.780198, "relative_start": 0, "end": **********.902806, "relative_end": **********.902806, "duration": 0.*****************, "duration_str": "123ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.902816, "relative_start": 0.*****************, "end": **********.303716, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "401ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.918118, "relative_start": 0.*****************, "end": **********.919964, "relative_end": **********.919964, "duration": 0.0018460750579833984, "duration_str": "1.85ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.96569, "relative_start": 0.*****************, "end": **********.303723, "relative_end": 9.059906005859375e-06, "duration": 0.*****************, "duration_str": "338ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: scoring.public", "start": **********.967397, "relative_start": 0.*****************, "end": **********.967397, "relative_end": **********.967397, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::show", "start": **********.187651, "relative_start": 0.****************, "end": **********.187651, "relative_end": **********.187651, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.navigation", "start": **********.191023, "relative_start": 0.4108250141143799, "end": **********.191023, "relative_end": **********.191023, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.theme-switcher", "start": **********.192425, "relative_start": 0.41222691535949707, "end": **********.192425, "relative_end": **********.192425, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.icons.sun", "start": **********.194475, "relative_start": 0.4142768383026123, "end": **********.194475, "relative_end": **********.194475, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.icons.moon", "start": **********.195273, "relative_start": 0.41507482528686523, "end": **********.195273, "relative_end": **********.195273, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.icons.sun", "start": **********.196024, "relative_start": 0.41582584381103516, "end": **********.196024, "relative_end": **********.196024, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.icons.moon", "start": **********.196245, "relative_start": 0.4160468578338623, "end": **********.196245, "relative_end": **********.196245, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.icons.computer-desktop", "start": **********.196479, "relative_start": 0.41628098487854004, "end": **********.196479, "relative_end": **********.196479, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.header", "start": **********.198009, "relative_start": 0.*****************, "end": **********.198009, "relative_end": **********.198009, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.card", "start": **********.199734, "relative_start": 0.*****************, "end": **********.199734, "relative_end": **********.199734, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.trace-and-editor", "start": **********.200559, "relative_start": 0.***************, "end": **********.200559, "relative_end": **********.200559, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.trace", "start": **********.212148, "relative_start": 0.****************, "end": **********.212148, "relative_end": **********.212148, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.icons.chevron-down", "start": **********.215737, "relative_start": 0.43553900718688965, "end": **********.215737, "relative_end": **********.215737, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.icons.chevron-up", "start": **********.216602, "relative_start": 0.4364039897918701, "end": **********.216602, "relative_end": **********.216602, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.icons.chevron-up", "start": **********.217344, "relative_start": 0.4371459484100342, "end": **********.217344, "relative_end": **********.217344, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.icons.chevron-down", "start": **********.21752, "relative_start": 0.43732190132141113, "end": **********.21752, "relative_end": **********.21752, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.editor", "start": **********.239935, "relative_start": 0.*****************, "end": **********.239935, "relative_end": **********.239935, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.card", "start": **********.286267, "relative_start": 0.5060689449310303, "end": **********.286267, "relative_end": **********.286267, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.context", "start": **********.286657, "relative_start": 0.5064589977264404, "end": **********.286657, "relative_end": **********.286657, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.card", "start": **********.288817, "relative_start": 0.5086188316345215, "end": **********.288817, "relative_end": **********.288817, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.card", "start": **********.289206, "relative_start": 0.5090079307556152, "end": **********.289206, "relative_end": **********.289206, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.layout", "start": **********.289481, "relative_start": 0.5092828273773193, "end": **********.289481, "relative_end": **********.289481, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 28042008, "peak_usage_str": "27MB"}, "exceptions": {"count": 2, "exceptions": [{"type": "Illuminate\\View\\ViewException", "message": "htmlspecialchars(): Argument #1 ($string) must be of type string, array given (View: C:\\laragon\\www\\ielts\\ielts-scoring-system\\resources\\views\\scoring\\public.blade.php)", "code": 0, "file": "vendor/laravel/framework/src/Illuminate/Support/helpers.php", "line": 141, "stack_trace": null, "stack_trace_html": "<pre class=sf-dump id=sf-dump-2047274613 data-indent-pad=\"  \"><span class=sf-dump-note>array:63</span> [<samp data-depth=1 class=sf-dump-expanded>\n  <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>59</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"19 characters\">handleViewException</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"38 characters\">Illuminate\\View\\Engines\\CompilerEngine</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">[object TypeError]</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-num>1</span>\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"71 characters\">vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>76</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"12 characters\">evaluatePath</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\View\\Engines\\PhpEngine</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"102 characters\">C:\\laragon\\www\\ielts\\ielts-scoring-system\\storage\\framework\\views/d6ccf6006ae9cc9eb654e17fa393b764.php</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>__env</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\View\\Factory\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\View</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Factory</span></span> {<a class=sf-dump-ref href=#sf-dump-2047274613-ref21306 title=\"2 occurrences\">#1306</a> &#8230;25}\n        \"<span class=sf-dump-key>app</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-2047274613-ref24 title=\"2 occurrences\">#4</a> &#8230;44}\n        \"<span class=sf-dump-key>errors</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\ViewErrorBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ViewErrorBag</span></span> {<a class=sf-dump-ref href=#sf-dump-2047274613-ref21422 title=\"2 occurrences\">#1422</a><samp data-depth=5 id=sf-dump-2047274613-ref21422 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []\n        </samp>}\n        \"<span class=sf-dump-key>attempt</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\ScoringAttempt\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ScoringAttempt</span></span> {<a class=sf-dump-ref href=#sf-dump-2047274613-ref21405 title=\"2 occurrences\">#1405</a><samp data-depth=5 id=sf-dump-2047274613-ref21405 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"16 characters\">scoring_attempts</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n          +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n          #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n          +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n          +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n          +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:21</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>10</span>\n            \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-num>3</span>\n            \"<span class=sf-dump-key>essay_question_id</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>essay_content</span>\" => \"\"\"\n              <span class=sf-dump-str title=\"1184 characters\">International news is good for student but sometime it is not nessessary. Many people think student in secondary school need to study about international news, other think this waste their time. My essay will write about both of these thing.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n              <span class=sf-dump-str title=\"1184 characters\">Firstly, International news is good because it make student smart. When student learn international news they can know many things. Example, student can know weather in other country, famous people life, or footbal match. They will very interesting about these topic and can relax in the classroom. Learning international news is also help student speaking english well.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n              <span class=sf-dump-str title=\"1184 characters\">However, I thinking international news sometime is boring. Student have many important subject, example maths, literature, or chemistry. These subjects can help student become doctor or engineer in future. International news not really important, only waste of time.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n              <span class=sf-dump-str title=\"1184 characters\">In my opinion, secondary student should play sport and learn math better than learn international news. Teacher should teaching student good subject not international news.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n              <span class=sf-dump-str title=\"1184 characters\">I conclusion, international news have advantage but more disadvantage. Student no need study this subject, waste of their time.</span>\n              \"\"\"\n            \"<span class=sf-dump-key>task_type</span>\" => \"<span class=sf-dump-str title=\"5 characters\">task2</span>\"\n            \"<span class=sf-dump-key>time_limit</span>\" => <span class=sf-dump-num>40</span>\n            \"<span class=sf-dump-key>overall_band_score</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.0</span>\"\n            \"<span class=sf-dump-key>task_achievement</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.0</span>\"\n            \"<span class=sf-dump-key>coherence_cohesion</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.5</span>\"\n            \"<span class=sf-dump-key>lexical_resource</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.0</span>\"\n            \"<span class=sf-dump-key>grammar_accuracy</span>\" => \"<span class=sf-dump-str title=\"3 characters\">3.5</span>\"\n            \"<span class=sf-dump-key>detailed_feedback</span>\" => \"<span class=sf-dump-str title=\"2203 characters\">{&quot;grammar_accuracy&quot;: {&quot;score&quot;: 3.5, &quot;issues&quot;: [&quot;Sai ch&#7911; ng&#7919;-&#273;&#7897;ng t&#7915;: &#8216;news is&#8230; it make student&#8230;&#8217;;&quot;, &quot;Thi&#7871;u m&#7841;o t&#7915;: &#8216;in other country&#8217;; sai th&#236;: &#8216;I thinking&#8217;;&quot;, &quot;C&#226;u thi&#7871;u th&#224;nh ph&#7847;n: &#8216;International news not really important&#8217;.&quot;, &quot;Vi&#7871;t hoa v&#224; ch&#7845;m c&#226;u ch&#432;a chu&#7849;n.&quot;], &quot;feedback&quot;: &quot;L&#7895;i ng&#7919; ph&#225;p c&#417; b&#7843;n xu&#7845;t hi&#7879;n th&#432;&#7901;ng xuy&#234;n; ch&#7911;-v&#7883;, th&#236; v&#224; m&#7841;o t&#7915; ch&#432;a chu&#7849;n.&quot;, &quot;improvements&quot;: [&quot;&#212;n l&#7841;i S-V agreement, m&#7841;o t&#7915;, danh t&#7915; s&#7889; nhi&#7873;u.&quot;, &quot;D&#249;ng c&#7845;u tr&#250;c ph&#7913;c t&#7841;p h&#417;n: Although&#8230;, in spite of&#8230;, would lead to&#8230;&quot;, &quot;T&#7921; &#273;&#7885;c l&#7841;i v&#224; s&#7917;a l&#7895;i ng&#7919; ph&#225;p tr&#432;&#7899;c khi n&#7897;p b&#224;i.&quot;]}, &quot;lexical_resource&quot;: {&quot;score&quot;: 4, &quot;issues&quot;: [&quot;L&#7863;p &#273;i l&#7863;p l&#7841;i &#8216;student&#8217;, &#8216;international news&#8217;.&quot;, &quot;Sai ch&#237;nh t&#7843;: nessessary, footbal, interesting, advantage v.v.&quot;, &quot;D&#249;ng t&#7915; ch&#432;a chu&#7849;n: &#8216;smart&#8217; (n&#234;n thay b&#7857;ng &#8216;knowledgeable&#8217;), &#8216;good subject&#8217;.&quot;], &quot;feedback&quot;: &quot;T&#7915; v&#7921;ng &#7903; m&#7913;c c&#417; b&#7843;n, l&#7863;p nhi&#7873;u, c&#243; sai ch&#237;nh t&#7843; v&#224; d&#249;ng t&#7915; kh&#244;ng ch&#237;nh x&#225;c.&quot;, &quot;improvements&quot;: [&quot;H&#7885;c th&#234;m collocations: keep abreast of international affairs, broaden horizons&#8230;&quot;, &quot;Ki&#7875;m tra ch&#237;nh t&#7843; sau khi vi&#7871;t.&quot;, &quot;D&#249;ng t&#7915; &#273;a d&#7841;ng h&#417;n, tr&#225;nh l&#7863;p; s&#7917; d&#7909;ng t&#7915; &#273;&#7891;ng ngh&#297;a, paraphrase.&quot;]}, &quot;task_achievement&quot;: {&quot;score&quot;: 4, &quot;issues&quot;: [&quot;Thi&#7871;u t&#7915; (179/250 words) &#8594; b&#7883; tr&#7915; &#273;i&#7875;m.&quot;, &quot;L&#7853;p lu&#7853;n &#273;&#417;n gi&#7843;n, ch&#432;a c&#243; v&#237; d&#7909; c&#7909; th&#7875;, s&#7889; li&#7879;u ho&#7863;c ph&#226;n t&#237;ch s&#226;u.&quot;, &quot;Ch&#432;a tr&#7843; l&#7901;i &#273;&#7847;y &#273;&#7911; c&#7843; hai quan &#273;i&#7875;m m&#7897;t c&#225;ch c&#226;n b&#7857;ng.&quot;], &quot;feedback&quot;: &quot;B&#224;i vi&#7871;t ch&#432;a &#273;&#225;p &#7913;ng &#273;&#7911; y&#234;u c&#7847;u t&#7889;i thi&#7875;u 250 t&#7915;, &#253; c&#242;n s&#417; s&#224;i v&#224; minh ho&#7841; ch&#432;a thuy&#7871;t ph&#7909;c.&quot;, &quot;improvements&quot;: [&quot;Vi&#7871;t t&#7889;i thi&#7875;u 260 t&#7915; &#273;&#7875; tr&#225;nh tr&#7915; &#273;i&#7875;m.&quot;, &quot;Tri&#7875;n khai m&#7895;i l&#7853;p lu&#7853;n v&#7899;i v&#237; d&#7909; th&#7921;c t&#7871;, s&#7889; li&#7879;u, ho&#7863;c d&#7851;n ch&#7913;ng c&#225; nh&#226;n.&quot;, &quot;N&#234;u r&#245; l&#7853;p tr&#432;&#7901;ng ngay ph&#7847;n m&#7903; b&#224;i v&#224; nh&#7855;c l&#7841;i trong k&#7871;t lu&#7853;n.&quot;]}, &quot;coherence_cohesion&quot;: {&quot;score&quot;: 4.5, &quot;issues&quot;: [&quot;T&#7915; n&#7889;i ngh&#232;o n&#224;n, h&#7847;u nh&#432; ch&#7881; d&#249;ng &#8216;Firstly&#8217;, &#8216;However&#8217;.&quot;, &quot;C&#225;c &#273;o&#7841;n qu&#225; ng&#7855;n, &#253; chuy&#7875;n &#273;&#7897;t ng&#7897;t, ch&#432;a c&#243; c&#226;u ch&#7911; &#273;&#7873; r&#245; r&#224;ng.&quot;, &quot;Thi&#7871;u m&#7841;ch logic gi&#7919;a c&#225;c c&#226;u.&quot;], &quot;feedback&quot;: &quot;B&#7889; c&#7909;c c&#243; m&#7903; th&#226;n k&#7871;t nh&#432;ng li&#234;n k&#7871;t &#253; c&#242;n r&#7901;i r&#7841;c, t&#7915; n&#7889;i h&#7841;n ch&#7871; v&#224; l&#7863;p l&#7841;i.&quot;, &quot;improvements&quot;: [&quot;S&#7917; d&#7909;ng &#273;a d&#7841;ng linking words: In addition, Moreover, On the other hand, Consequently&#8230;&quot;, &quot;M&#7895;i &#273;o&#7841;n th&#226;n b&#224;i: 1 c&#226;u ch&#7911; &#273;&#7873; &#8211; 2-3 c&#226;u gi&#7843;i th&#237;ch &#8211; 1 v&#237; d&#7909; &#8211; 1 c&#226;u k&#7871;t.&quot;, &quot;Tr&#225;nh l&#7863;p t&#7915; &#8216;student&#8217;, &#8216;international news&#8217; qu&#225; nhi&#7873;u; d&#249;ng &#273;&#7841;i t&#7915; thay th&#7871;.&quot;]}}</span>\"\n            \"<span class=sf-dump-key>highlighted_corrections</span>\" => \"<span class=sf-dump-str title=\"2895 characters\">[{&quot;severity&quot;: &quot;high&quot;, &quot;error_type&quot;: &quot;spelling&quot;, &quot;explanation&quot;: &quot;Sai ch&#237;nh t&#7843; &#8216;sometime&#8217; (thi&#7871;u &#8216;s&#8217;) v&#224; &#8216;nessessary&#8217;.&quot;, &quot;original_text&quot;: &quot;sometime it is not nessessary&quot;, &quot;suggested_correction&quot;: &quot;sometimes it is not necessary&quot;}, {&quot;severity&quot;: &quot;medium&quot;, &quot;error_type&quot;: &quot;grammar&quot;, &quot;explanation&quot;: &quot;Thi&#7871;u &#8216;s&#8217; &#7903; danh t&#7915; s&#7889; nhi&#7873;u v&#224; m&#7841;o t&#7915; &#8216;schools&#8217;.&quot;, &quot;original_text&quot;: &quot;student in secondary school&quot;, &quot;suggested_correction&quot;: &quot;students in secondary schools&quot;}, {&quot;severity&quot;: &quot;medium&quot;, &quot;error_type&quot;: &quot;grammar&quot;, &quot;explanation&quot;: &quot;&#272;&#7897;ng t&#7915; ph&#7843;i chia s&#7889; &#237;t &#8216;wastes&#8217; khi ch&#7911; ng&#7919; l&#224; &#8216;this&#8217;.&quot;, &quot;original_text&quot;: &quot;this waste their time&quot;, &quot;suggested_correction&quot;: &quot;this wastes their time&quot;}, {&quot;severity&quot;: &quot;high&quot;, &quot;error_type&quot;: &quot;grammar|vocabulary&quot;, &quot;explanation&quot;: &quot;Chia &#273;&#7897;ng t&#7915; &#8216;makes&#8217;; d&#249;ng t&#7915; ph&#249; h&#7907;p &#8216;beneficial&#8217;, &#8216;knowledgeable&#8217;.&quot;, &quot;original_text&quot;: &quot;International news is good because it make student smart&quot;, &quot;suggested_correction&quot;: &quot;International news is beneficial because it makes students more knowledgeable&quot;}, {&quot;severity&quot;: &quot;medium&quot;, &quot;error_type&quot;: &quot;grammar|vocabulary&quot;, &quot;explanation&quot;: &quot;Thi&#7871;u c&#7909;m &#8216;For example&#8217;; &#8216;know weather&#8217; &#8594; &#8216;learn about the weather&#8217;; &#8216;country&#8217; &#8594; &#8216;countries&#8217;.&quot;, &quot;original_text&quot;: &quot;Example, student can know weather in other country&quot;, &quot;suggested_correction&quot;: &quot;For example, students can learn about the weather in other countries&quot;}, {&quot;severity&quot;: &quot;high&quot;, &quot;error_type&quot;: &quot;grammar|vocabulary&quot;, &quot;explanation&quot;: &quot;Sai c&#7845;u tr&#250;c &#8216;is also help&#8217;; c&#7847;n &#8216;helps&#8217;; vi&#7871;t hoa &#8216;English&#8217;.&quot;, &quot;original_text&quot;: &quot;Learning international news is also help student speaking english well.&quot;, &quot;suggested_correction&quot;: &quot;Learning international news also helps students improve their English speaking skills.&quot;}, {&quot;severity&quot;: &quot;medium&quot;, &quot;error_type&quot;: &quot;grammar&quot;, &quot;explanation&quot;: &quot;Sai th&#236; ti&#7871;p di&#7877;n, v&#7883; tr&#237; &#8216;sometimes&#8217;.&quot;, &quot;original_text&quot;: &quot;I thinking international news sometime is boring.&quot;, &quot;suggested_correction&quot;: &quot;I think international news is sometimes boring.&quot;}, {&quot;severity&quot;: &quot;medium&quot;, &quot;error_type&quot;: &quot;grammar|punctuation&quot;, &quot;explanation&quot;: &quot;Danh t&#7915; s&#7889; nhi&#7873;u; thi&#7871;u d&#7845;u ph&#7849;y; m&#7841;o t&#7915;.&quot;, &quot;original_text&quot;: &quot;Student have many important subject, example maths, literature, or chemistry.&quot;, &quot;suggested_correction&quot;: &quot;Students have many important subjects, for example, maths, literature or chemistry.&quot;}, {&quot;severity&quot;: &quot;high&quot;, &quot;error_type&quot;: &quot;grammar&quot;, &quot;explanation&quot;: &quot;Thi&#7871;u &#273;&#7897;ng t&#7915; &#8216;is&#8217;; c&#7847;n m&#7841;o t&#7915; &#8216;a&#8217;.&quot;, &quot;original_text&quot;: &quot;International news not really important, only waste of time.&quot;, &quot;suggested_correction&quot;: &quot;International news is not really important; it is only a waste of time.&quot;}, {&quot;severity&quot;: &quot;high&quot;, &quot;error_type&quot;: &quot;grammar|spelling&quot;, &quot;explanation&quot;: &quot;C&#7847;n &#8216;In conclusion&#8217;; &#8216;news&#8217; coi nh&#432; s&#7889; &#237;t; th&#234;m &#8216;some&#8217;; danh t&#7915; s&#7889; nhi&#7873;u &#8216;disadvantages&#8217;.&quot;, &quot;original_text&quot;: &quot;I conclusion, international news have advantage but more disadvantage.&quot;, &quot;suggested_correction&quot;: &quot;In conclusion, international news has some advantages but more disadvantages.&quot;}]</span>\"\n            \"<span class=sf-dump-key>criteria_analysis</span>\" => \"<span class=sf-dump-str title=\"1493 characters\">[{&quot;criterion&quot;: &quot;Task Achievement&quot;, &quot;strengths&quot;: [&quot;&#272;&#227; n&#234;u &#273;&#432;&#7907;c hai quan &#273;i&#7875;m&quot;, &quot;C&#243; l&#7853;p tr&#432;&#7901;ng c&#225; nh&#226;n trong &#273;o&#7841;n k&#7871;t&quot;], &quot;weaknesses&quot;: [&quot;D&#432;&#7899;i 250 t&#7915; n&#234;n b&#7883; tr&#7915; &#273;i&#7875;m&quot;, &quot;L&#7853;p lu&#7853;n h&#7901;i h&#7907;t, v&#237; d&#7909; chung chung&quot;, &quot;Kh&#244;ng gi&#7843;i th&#237;ch v&#236; sao tin qu&#7889;c t&#7871; gi&#250;p n&#243;i ti&#7871;ng Anh t&#7889;t h&#417;n&quot;, &quot;Kh&#244;ng ph&#7843;n bi&#7879;n l&#7841;i quan &#273;i&#7875;m &#273;&#7889;i l&#7853;p&quot;], &quot;band_justification&quot;: &quot;&#272;&#225;p &#7913;ng ph&#7847;n n&#224;o y&#234;u c&#7847;u &#273;&#7873; nh&#432;ng ph&#225;t tri&#7875;n ch&#432;a &#273;&#7911;, thi&#7871;u chi&#7873;u s&#226;u v&#224; thi&#7871;u t&#7915; &#8594; band 4.0.&quot;}, {&quot;criterion&quot;: &quot;Coherence &amp; Cohesion&quot;, &quot;strengths&quot;: [&quot;C&#243; b&#7889; c&#7909;c c&#417; b&#7843;n: m&#7903;&#8211;th&#226;n&#8211;k&#7871;t&quot;, &quot;M&#7897;t s&#7889; t&#7915; n&#7889;i &#273;&#432;&#7907;c d&#249;ng (Firstly, However)&quot;], &quot;weaknesses&quot;: [&quot;Li&#234;n k&#7871;t &#253; k&#233;m, chuy&#7875;n &#273;o&#7841;n &#273;&#7897;t ng&#7897;t&quot;, &quot;C&#226;u ch&#7911; &#273;&#7873; m&#7901; nh&#7841;t&quot;, &quot;D&#249;ng l&#7863;p t&#7915; n&#7889;i, ch&#432;a &#273;a d&#7841;ng&quot;], &quot;band_justification&quot;: &quot;Li&#234;n k&#7871;t &#7903; m&#7913;c h&#7841;n ch&#7871;, &#253; &#273;&#244;i khi r&#7901;i r&#7841;c nh&#432;ng v&#7851;n theo tr&#236;nh t&#7921; chung &#8594; 4.5.&quot;}, {&quot;criterion&quot;: &quot;Lexical Resource&quot;, &quot;strengths&quot;: [&quot;T&#7915; v&#7921;ng quen thu&#7897;c, d&#7877; hi&#7875;u&quot;, &quot;M&#7897;t v&#224;i t&#7915; h&#7885;c thu&#7853;t &#273;&#417;n gi&#7843;n (advantage, disadvantage)&quot;], &quot;weaknesses&quot;: [&quot;L&#7863;p t&#7915; th&#432;&#7901;ng xuy&#234;n&quot;, &quot;Sai ch&#237;nh t&#7843; nhi&#7873;u&quot;, &quot;D&#249;ng t&#7915; ch&#432;a ch&#237;nh x&#225;c ho&#7863;c kh&#244;ng ph&#249; h&#7907;p v&#259;n phong h&#7885;c thu&#7853;t&quot;], &quot;band_justification&quot;: &quot;V&#7889;n t&#7915; h&#7841;n ch&#7871;, l&#7895;i ch&#237;nh t&#7843; &amp; ch&#7885;n t&#7915; &#7843;nh h&#432;&#7903;ng ng&#432;&#7901;i &#273;&#7885;c &#8594; 4.0.&quot;}, {&quot;criterion&quot;: &quot;Grammatical Range &amp; Accuracy&quot;, &quot;strengths&quot;: [&quot;Bi&#7871;t k&#7871;t th&#250;c c&#226;u b&#7857;ng d&#7845;u ch&#7845;m&quot;, &quot;M&#7897;t s&#7889; m&#7879;nh &#273;&#7873; &#273;&#417;n ch&#237;nh x&#225;c&quot;], &quot;weaknesses&quot;: [&quot;L&#7895;i S-V agreement li&#234;n t&#7909;c&quot;, &quot;Thi&#7871;u m&#7841;o t&#7915;, gi&#7899;i t&#7915;&quot;, &quot;C&#7845;u tr&#250;c c&#226;u &#273;&#417;n gi&#7843;n, hi&#7871;m c&#226;u ph&#7913;c&quot;, &quot;Sai th&#236; v&#224; thi&#7871;u &#273;&#7897;ng t&#7915; to be&quot;], &quot;band_justification&quot;: &quot;L&#7895;i th&#432;&#7901;ng xuy&#234;n khi&#7871;n kh&#243; hi&#7875;u, c&#7845;u tr&#250;c &#273;&#417;n gi&#7843;n &#8594; 3.5.&quot;}]</span>\"\n            \"<span class=sf-dump-key>statistics</span>\" => \"<span class=sf-dump-str title=\"145 characters\">{&quot;total_errors&quot;: 45, &quot;grammar_errors&quot;: 28, &quot;spelling_errors&quot;: 10, &quot;complexity_level&quot;: &quot;basic&quot;, &quot;sentence_variety&quot;: &quot;low&quot;, &quot;vocabulary_errors&quot;: 7}</span>\"\n            \"<span class=sf-dump-key>word_count</span>\" => <span class=sf-dump-num>179</span>\n            \"<span class=sf-dump-key>essay_length</span>\" => <span class=sf-dump-num>1184</span>\n            \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"9 characters\">completed</span>\"\n            \"<span class=sf-dump-key>error_message</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-18 17:02:55</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-18 17:03:22</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:21</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>10</span>\n            \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-num>3</span>\n            \"<span class=sf-dump-key>essay_question_id</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>essay_content</span>\" => \"\"\"\n              <span class=sf-dump-str title=\"1184 characters\">International news is good for student but sometime it is not nessessary. Many people think student in secondary school need to study about international news, other think this waste their time. My essay will write about both of these thing.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n              <span class=sf-dump-str title=\"1184 characters\">Firstly, International news is good because it make student smart. When student learn international news they can know many things. Example, student can know weather in other country, famous people life, or footbal match. They will very interesting about these topic and can relax in the classroom. Learning international news is also help student speaking english well.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n              <span class=sf-dump-str title=\"1184 characters\">However, I thinking international news sometime is boring. Student have many important subject, example maths, literature, or chemistry. These subjects can help student become doctor or engineer in future. International news not really important, only waste of time.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n              <span class=sf-dump-str title=\"1184 characters\">In my opinion, secondary student should play sport and learn math better than learn international news. Teacher should teaching student good subject not international news.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n              <span class=sf-dump-str title=\"1184 characters\">I conclusion, international news have advantage but more disadvantage. Student no need study this subject, waste of their time.</span>\n              \"\"\"\n            \"<span class=sf-dump-key>task_type</span>\" => \"<span class=sf-dump-str title=\"5 characters\">task2</span>\"\n            \"<span class=sf-dump-key>time_limit</span>\" => <span class=sf-dump-num>40</span>\n            \"<span class=sf-dump-key>overall_band_score</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.0</span>\"\n            \"<span class=sf-dump-key>task_achievement</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.0</span>\"\n            \"<span class=sf-dump-key>coherence_cohesion</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.5</span>\"\n            \"<span class=sf-dump-key>lexical_resource</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.0</span>\"\n            \"<span class=sf-dump-key>grammar_accuracy</span>\" => \"<span class=sf-dump-str title=\"3 characters\">3.5</span>\"\n            \"<span class=sf-dump-key>detailed_feedback</span>\" => \"<span class=sf-dump-str title=\"2203 characters\">{&quot;grammar_accuracy&quot;: {&quot;score&quot;: 3.5, &quot;issues&quot;: [&quot;Sai ch&#7911; ng&#7919;-&#273;&#7897;ng t&#7915;: &#8216;news is&#8230; it make student&#8230;&#8217;;&quot;, &quot;Thi&#7871;u m&#7841;o t&#7915;: &#8216;in other country&#8217;; sai th&#236;: &#8216;I thinking&#8217;;&quot;, &quot;C&#226;u thi&#7871;u th&#224;nh ph&#7847;n: &#8216;International news not really important&#8217;.&quot;, &quot;Vi&#7871;t hoa v&#224; ch&#7845;m c&#226;u ch&#432;a chu&#7849;n.&quot;], &quot;feedback&quot;: &quot;L&#7895;i ng&#7919; ph&#225;p c&#417; b&#7843;n xu&#7845;t hi&#7879;n th&#432;&#7901;ng xuy&#234;n; ch&#7911;-v&#7883;, th&#236; v&#224; m&#7841;o t&#7915; ch&#432;a chu&#7849;n.&quot;, &quot;improvements&quot;: [&quot;&#212;n l&#7841;i S-V agreement, m&#7841;o t&#7915;, danh t&#7915; s&#7889; nhi&#7873;u.&quot;, &quot;D&#249;ng c&#7845;u tr&#250;c ph&#7913;c t&#7841;p h&#417;n: Although&#8230;, in spite of&#8230;, would lead to&#8230;&quot;, &quot;T&#7921; &#273;&#7885;c l&#7841;i v&#224; s&#7917;a l&#7895;i ng&#7919; ph&#225;p tr&#432;&#7899;c khi n&#7897;p b&#224;i.&quot;]}, &quot;lexical_resource&quot;: {&quot;score&quot;: 4, &quot;issues&quot;: [&quot;L&#7863;p &#273;i l&#7863;p l&#7841;i &#8216;student&#8217;, &#8216;international news&#8217;.&quot;, &quot;Sai ch&#237;nh t&#7843;: nessessary, footbal, interesting, advantage v.v.&quot;, &quot;D&#249;ng t&#7915; ch&#432;a chu&#7849;n: &#8216;smart&#8217; (n&#234;n thay b&#7857;ng &#8216;knowledgeable&#8217;), &#8216;good subject&#8217;.&quot;], &quot;feedback&quot;: &quot;T&#7915; v&#7921;ng &#7903; m&#7913;c c&#417; b&#7843;n, l&#7863;p nhi&#7873;u, c&#243; sai ch&#237;nh t&#7843; v&#224; d&#249;ng t&#7915; kh&#244;ng ch&#237;nh x&#225;c.&quot;, &quot;improvements&quot;: [&quot;H&#7885;c th&#234;m collocations: keep abreast of international affairs, broaden horizons&#8230;&quot;, &quot;Ki&#7875;m tra ch&#237;nh t&#7843; sau khi vi&#7871;t.&quot;, &quot;D&#249;ng t&#7915; &#273;a d&#7841;ng h&#417;n, tr&#225;nh l&#7863;p; s&#7917; d&#7909;ng t&#7915; &#273;&#7891;ng ngh&#297;a, paraphrase.&quot;]}, &quot;task_achievement&quot;: {&quot;score&quot;: 4, &quot;issues&quot;: [&quot;Thi&#7871;u t&#7915; (179/250 words) &#8594; b&#7883; tr&#7915; &#273;i&#7875;m.&quot;, &quot;L&#7853;p lu&#7853;n &#273;&#417;n gi&#7843;n, ch&#432;a c&#243; v&#237; d&#7909; c&#7909; th&#7875;, s&#7889; li&#7879;u ho&#7863;c ph&#226;n t&#237;ch s&#226;u.&quot;, &quot;Ch&#432;a tr&#7843; l&#7901;i &#273;&#7847;y &#273;&#7911; c&#7843; hai quan &#273;i&#7875;m m&#7897;t c&#225;ch c&#226;n b&#7857;ng.&quot;], &quot;feedback&quot;: &quot;B&#224;i vi&#7871;t ch&#432;a &#273;&#225;p &#7913;ng &#273;&#7911; y&#234;u c&#7847;u t&#7889;i thi&#7875;u 250 t&#7915;, &#253; c&#242;n s&#417; s&#224;i v&#224; minh ho&#7841; ch&#432;a thuy&#7871;t ph&#7909;c.&quot;, &quot;improvements&quot;: [&quot;Vi&#7871;t t&#7889;i thi&#7875;u 260 t&#7915; &#273;&#7875; tr&#225;nh tr&#7915; &#273;i&#7875;m.&quot;, &quot;Tri&#7875;n khai m&#7895;i l&#7853;p lu&#7853;n v&#7899;i v&#237; d&#7909; th&#7921;c t&#7871;, s&#7889; li&#7879;u, ho&#7863;c d&#7851;n ch&#7913;ng c&#225; nh&#226;n.&quot;, &quot;N&#234;u r&#245; l&#7853;p tr&#432;&#7901;ng ngay ph&#7847;n m&#7903; b&#224;i v&#224; nh&#7855;c l&#7841;i trong k&#7871;t lu&#7853;n.&quot;]}, &quot;coherence_cohesion&quot;: {&quot;score&quot;: 4.5, &quot;issues&quot;: [&quot;T&#7915; n&#7889;i ngh&#232;o n&#224;n, h&#7847;u nh&#432; ch&#7881; d&#249;ng &#8216;Firstly&#8217;, &#8216;However&#8217;.&quot;, &quot;C&#225;c &#273;o&#7841;n qu&#225; ng&#7855;n, &#253; chuy&#7875;n &#273;&#7897;t ng&#7897;t, ch&#432;a c&#243; c&#226;u ch&#7911; &#273;&#7873; r&#245; r&#224;ng.&quot;, &quot;Thi&#7871;u m&#7841;ch logic gi&#7919;a c&#225;c c&#226;u.&quot;], &quot;feedback&quot;: &quot;B&#7889; c&#7909;c c&#243; m&#7903; th&#226;n k&#7871;t nh&#432;ng li&#234;n k&#7871;t &#253; c&#242;n r&#7901;i r&#7841;c, t&#7915; n&#7889;i h&#7841;n ch&#7871; v&#224; l&#7863;p l&#7841;i.&quot;, &quot;improvements&quot;: [&quot;S&#7917; d&#7909;ng &#273;a d&#7841;ng linking words: In addition, Moreover, On the other hand, Consequently&#8230;&quot;, &quot;M&#7895;i &#273;o&#7841;n th&#226;n b&#224;i: 1 c&#226;u ch&#7911; &#273;&#7873; &#8211; 2-3 c&#226;u gi&#7843;i th&#237;ch &#8211; 1 v&#237; d&#7909; &#8211; 1 c&#226;u k&#7871;t.&quot;, &quot;Tr&#225;nh l&#7863;p t&#7915; &#8216;student&#8217;, &#8216;international news&#8217; qu&#225; nhi&#7873;u; d&#249;ng &#273;&#7841;i t&#7915; thay th&#7871;.&quot;]}}</span>\"\n            \"<span class=sf-dump-key>highlighted_corrections</span>\" => \"<span class=sf-dump-str title=\"2895 characters\">[{&quot;severity&quot;: &quot;high&quot;, &quot;error_type&quot;: &quot;spelling&quot;, &quot;explanation&quot;: &quot;Sai ch&#237;nh t&#7843; &#8216;sometime&#8217; (thi&#7871;u &#8216;s&#8217;) v&#224; &#8216;nessessary&#8217;.&quot;, &quot;original_text&quot;: &quot;sometime it is not nessessary&quot;, &quot;suggested_correction&quot;: &quot;sometimes it is not necessary&quot;}, {&quot;severity&quot;: &quot;medium&quot;, &quot;error_type&quot;: &quot;grammar&quot;, &quot;explanation&quot;: &quot;Thi&#7871;u &#8216;s&#8217; &#7903; danh t&#7915; s&#7889; nhi&#7873;u v&#224; m&#7841;o t&#7915; &#8216;schools&#8217;.&quot;, &quot;original_text&quot;: &quot;student in secondary school&quot;, &quot;suggested_correction&quot;: &quot;students in secondary schools&quot;}, {&quot;severity&quot;: &quot;medium&quot;, &quot;error_type&quot;: &quot;grammar&quot;, &quot;explanation&quot;: &quot;&#272;&#7897;ng t&#7915; ph&#7843;i chia s&#7889; &#237;t &#8216;wastes&#8217; khi ch&#7911; ng&#7919; l&#224; &#8216;this&#8217;.&quot;, &quot;original_text&quot;: &quot;this waste their time&quot;, &quot;suggested_correction&quot;: &quot;this wastes their time&quot;}, {&quot;severity&quot;: &quot;high&quot;, &quot;error_type&quot;: &quot;grammar|vocabulary&quot;, &quot;explanation&quot;: &quot;Chia &#273;&#7897;ng t&#7915; &#8216;makes&#8217;; d&#249;ng t&#7915; ph&#249; h&#7907;p &#8216;beneficial&#8217;, &#8216;knowledgeable&#8217;.&quot;, &quot;original_text&quot;: &quot;International news is good because it make student smart&quot;, &quot;suggested_correction&quot;: &quot;International news is beneficial because it makes students more knowledgeable&quot;}, {&quot;severity&quot;: &quot;medium&quot;, &quot;error_type&quot;: &quot;grammar|vocabulary&quot;, &quot;explanation&quot;: &quot;Thi&#7871;u c&#7909;m &#8216;For example&#8217;; &#8216;know weather&#8217; &#8594; &#8216;learn about the weather&#8217;; &#8216;country&#8217; &#8594; &#8216;countries&#8217;.&quot;, &quot;original_text&quot;: &quot;Example, student can know weather in other country&quot;, &quot;suggested_correction&quot;: &quot;For example, students can learn about the weather in other countries&quot;}, {&quot;severity&quot;: &quot;high&quot;, &quot;error_type&quot;: &quot;grammar|vocabulary&quot;, &quot;explanation&quot;: &quot;Sai c&#7845;u tr&#250;c &#8216;is also help&#8217;; c&#7847;n &#8216;helps&#8217;; vi&#7871;t hoa &#8216;English&#8217;.&quot;, &quot;original_text&quot;: &quot;Learning international news is also help student speaking english well.&quot;, &quot;suggested_correction&quot;: &quot;Learning international news also helps students improve their English speaking skills.&quot;}, {&quot;severity&quot;: &quot;medium&quot;, &quot;error_type&quot;: &quot;grammar&quot;, &quot;explanation&quot;: &quot;Sai th&#236; ti&#7871;p di&#7877;n, v&#7883; tr&#237; &#8216;sometimes&#8217;.&quot;, &quot;original_text&quot;: &quot;I thinking international news sometime is boring.&quot;, &quot;suggested_correction&quot;: &quot;I think international news is sometimes boring.&quot;}, {&quot;severity&quot;: &quot;medium&quot;, &quot;error_type&quot;: &quot;grammar|punctuation&quot;, &quot;explanation&quot;: &quot;Danh t&#7915; s&#7889; nhi&#7873;u; thi&#7871;u d&#7845;u ph&#7849;y; m&#7841;o t&#7915;.&quot;, &quot;original_text&quot;: &quot;Student have many important subject, example maths, literature, or chemistry.&quot;, &quot;suggested_correction&quot;: &quot;Students have many important subjects, for example, maths, literature or chemistry.&quot;}, {&quot;severity&quot;: &quot;high&quot;, &quot;error_type&quot;: &quot;grammar&quot;, &quot;explanation&quot;: &quot;Thi&#7871;u &#273;&#7897;ng t&#7915; &#8216;is&#8217;; c&#7847;n m&#7841;o t&#7915; &#8216;a&#8217;.&quot;, &quot;original_text&quot;: &quot;International news not really important, only waste of time.&quot;, &quot;suggested_correction&quot;: &quot;International news is not really important; it is only a waste of time.&quot;}, {&quot;severity&quot;: &quot;high&quot;, &quot;error_type&quot;: &quot;grammar|spelling&quot;, &quot;explanation&quot;: &quot;C&#7847;n &#8216;In conclusion&#8217;; &#8216;news&#8217; coi nh&#432; s&#7889; &#237;t; th&#234;m &#8216;some&#8217;; danh t&#7915; s&#7889; nhi&#7873;u &#8216;disadvantages&#8217;.&quot;, &quot;original_text&quot;: &quot;I conclusion, international news have advantage but more disadvantage.&quot;, &quot;suggested_correction&quot;: &quot;In conclusion, international news has some advantages but more disadvantages.&quot;}]</span>\"\n            \"<span class=sf-dump-key>criteria_analysis</span>\" => \"<span class=sf-dump-str title=\"1493 characters\">[{&quot;criterion&quot;: &quot;Task Achievement&quot;, &quot;strengths&quot;: [&quot;&#272;&#227; n&#234;u &#273;&#432;&#7907;c hai quan &#273;i&#7875;m&quot;, &quot;C&#243; l&#7853;p tr&#432;&#7901;ng c&#225; nh&#226;n trong &#273;o&#7841;n k&#7871;t&quot;], &quot;weaknesses&quot;: [&quot;D&#432;&#7899;i 250 t&#7915; n&#234;n b&#7883; tr&#7915; &#273;i&#7875;m&quot;, &quot;L&#7853;p lu&#7853;n h&#7901;i h&#7907;t, v&#237; d&#7909; chung chung&quot;, &quot;Kh&#244;ng gi&#7843;i th&#237;ch v&#236; sao tin qu&#7889;c t&#7871; gi&#250;p n&#243;i ti&#7871;ng Anh t&#7889;t h&#417;n&quot;, &quot;Kh&#244;ng ph&#7843;n bi&#7879;n l&#7841;i quan &#273;i&#7875;m &#273;&#7889;i l&#7853;p&quot;], &quot;band_justification&quot;: &quot;&#272;&#225;p &#7913;ng ph&#7847;n n&#224;o y&#234;u c&#7847;u &#273;&#7873; nh&#432;ng ph&#225;t tri&#7875;n ch&#432;a &#273;&#7911;, thi&#7871;u chi&#7873;u s&#226;u v&#224; thi&#7871;u t&#7915; &#8594; band 4.0.&quot;}, {&quot;criterion&quot;: &quot;Coherence &amp; Cohesion&quot;, &quot;strengths&quot;: [&quot;C&#243; b&#7889; c&#7909;c c&#417; b&#7843;n: m&#7903;&#8211;th&#226;n&#8211;k&#7871;t&quot;, &quot;M&#7897;t s&#7889; t&#7915; n&#7889;i &#273;&#432;&#7907;c d&#249;ng (Firstly, However)&quot;], &quot;weaknesses&quot;: [&quot;Li&#234;n k&#7871;t &#253; k&#233;m, chuy&#7875;n &#273;o&#7841;n &#273;&#7897;t ng&#7897;t&quot;, &quot;C&#226;u ch&#7911; &#273;&#7873; m&#7901; nh&#7841;t&quot;, &quot;D&#249;ng l&#7863;p t&#7915; n&#7889;i, ch&#432;a &#273;a d&#7841;ng&quot;], &quot;band_justification&quot;: &quot;Li&#234;n k&#7871;t &#7903; m&#7913;c h&#7841;n ch&#7871;, &#253; &#273;&#244;i khi r&#7901;i r&#7841;c nh&#432;ng v&#7851;n theo tr&#236;nh t&#7921; chung &#8594; 4.5.&quot;}, {&quot;criterion&quot;: &quot;Lexical Resource&quot;, &quot;strengths&quot;: [&quot;T&#7915; v&#7921;ng quen thu&#7897;c, d&#7877; hi&#7875;u&quot;, &quot;M&#7897;t v&#224;i t&#7915; h&#7885;c thu&#7853;t &#273;&#417;n gi&#7843;n (advantage, disadvantage)&quot;], &quot;weaknesses&quot;: [&quot;L&#7863;p t&#7915; th&#432;&#7901;ng xuy&#234;n&quot;, &quot;Sai ch&#237;nh t&#7843; nhi&#7873;u&quot;, &quot;D&#249;ng t&#7915; ch&#432;a ch&#237;nh x&#225;c ho&#7863;c kh&#244;ng ph&#249; h&#7907;p v&#259;n phong h&#7885;c thu&#7853;t&quot;], &quot;band_justification&quot;: &quot;V&#7889;n t&#7915; h&#7841;n ch&#7871;, l&#7895;i ch&#237;nh t&#7843; &amp; ch&#7885;n t&#7915; &#7843;nh h&#432;&#7903;ng ng&#432;&#7901;i &#273;&#7885;c &#8594; 4.0.&quot;}, {&quot;criterion&quot;: &quot;Grammatical Range &amp; Accuracy&quot;, &quot;strengths&quot;: [&quot;Bi&#7871;t k&#7871;t th&#250;c c&#226;u b&#7857;ng d&#7845;u ch&#7845;m&quot;, &quot;M&#7897;t s&#7889; m&#7879;nh &#273;&#7873; &#273;&#417;n ch&#237;nh x&#225;c&quot;], &quot;weaknesses&quot;: [&quot;L&#7895;i S-V agreement li&#234;n t&#7909;c&quot;, &quot;Thi&#7871;u m&#7841;o t&#7915;, gi&#7899;i t&#7915;&quot;, &quot;C&#7845;u tr&#250;c c&#226;u &#273;&#417;n gi&#7843;n, hi&#7871;m c&#226;u ph&#7913;c&quot;, &quot;Sai th&#236; v&#224; thi&#7871;u &#273;&#7897;ng t&#7915; to be&quot;], &quot;band_justification&quot;: &quot;L&#7895;i th&#432;&#7901;ng xuy&#234;n khi&#7871;n kh&#243; hi&#7875;u, c&#7845;u tr&#250;c &#273;&#417;n gi&#7843;n &#8594; 3.5.&quot;}]</span>\"\n            \"<span class=sf-dump-key>statistics</span>\" => \"<span class=sf-dump-str title=\"145 characters\">{&quot;total_errors&quot;: 45, &quot;grammar_errors&quot;: 28, &quot;spelling_errors&quot;: 10, &quot;complexity_level&quot;: &quot;basic&quot;, &quot;sentence_variety&quot;: &quot;low&quot;, &quot;vocabulary_errors&quot;: 7}</span>\"\n            \"<span class=sf-dump-key>word_count</span>\" => <span class=sf-dump-num>179</span>\n            \"<span class=sf-dump-key>essay_length</span>\" => <span class=sf-dump-num>1184</span>\n            \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"9 characters\">completed</span>\"\n            \"<span class=sf-dump-key>error_message</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-18 17:02:55</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-18 17:03:22</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">previous</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:9</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>detailed_feedback</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n            \"<span class=sf-dump-key>highlighted_corrections</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n            \"<span class=sf-dump-key>criteria_analysis</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n            \"<span class=sf-dump-key>statistics</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n            \"<span class=sf-dump-key>overall_band_score</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:1</span>\"\n            \"<span class=sf-dump-key>task_achievement</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:1</span>\"\n            \"<span class=sf-dump-key>coherence_cohesion</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:1</span>\"\n            \"<span class=sf-dump-key>lexical_resource</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:1</span>\"\n            \"<span class=sf-dump-key>grammar_accuracy</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:1</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>user</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\User\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">User</span></span> {<a class=sf-dump-ref>#1470</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"5 characters\">users</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n              +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n              +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n              +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>3</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">anh vo</span>\"\n                \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"\n                \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"10 characters\">0327239691</span>\"\n                \"<span class=sf-dump-key>email_verified_at</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$kHdkDSFSoZnvKLzjz2BNNO.eTtDMX8mMlt3RY4/iSp3q.LGENyf.i</span>\"\n                \"<span class=sf-dump-key>role</span>\" => \"<span class=sf-dump-str title=\"4 characters\">user</span>\"\n                \"<span class=sf-dump-key>is_active</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>remember_token</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-18 16:16:51</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-18 16:16:51</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>3</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">anh vo</span>\"\n                \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"\n                \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"10 characters\">0327239691</span>\"\n                \"<span class=sf-dump-key>email_verified_at</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$kHdkDSFSoZnvKLzjz2BNNO.eTtDMX8mMlt3RY4/iSp3q.LGENyf.i</span>\"\n                \"<span class=sf-dump-key>role</span>\" => \"<span class=sf-dump-str title=\"4 characters\">user</span>\"\n                \"<span class=sf-dump-key>is_active</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>remember_token</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-18 16:16:51</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-18 16:16:51</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">previous</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>email_verified_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n                \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"6 characters\">hashed</span>\"\n                \"<span class=sf-dump-key>is_active</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadCallback</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadContext</span>: <span class=sf-dump-const>null</span>\n              +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">password</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"14 characters\">remember_token</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:6</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"5 characters\">email</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"5 characters\">phone</span>\"\n                <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"8 characters\">password</span>\"\n                <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"4 characters\">role</span>\"\n                <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"9 characters\">is_active</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">authPasswordName</span>: \"<span class=sf-dump-str title=\"8 characters\">password</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">rememberTokenName</span>: \"<span class=sf-dump-str title=\"14 characters\">remember_token</span>\"\n            </samp>}\n            \"<span class=sf-dump-key>essayQuestion</span>\" => <span class=sf-dump-const>null</span>\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadCallback</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadContext</span>: <span class=sf-dump-const>null</span>\n          +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n          +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:18</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">user_id</span>\"\n            <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"17 characters\">essay_question_id</span>\"\n            <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"13 characters\">essay_content</span>\"\n            <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"9 characters\">task_type</span>\"\n            <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"10 characters\">time_limit</span>\"\n            <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"18 characters\">overall_band_score</span>\"\n            <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"16 characters\">task_achievement</span>\"\n            <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"18 characters\">coherence_cohesion</span>\"\n            <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"16 characters\">lexical_resource</span>\"\n            <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"16 characters\">grammar_accuracy</span>\"\n            <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"17 characters\">detailed_feedback</span>\"\n            <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"23 characters\">highlighted_corrections</span>\"\n            <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"17 characters\">criteria_analysis</span>\"\n            <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"10 characters\">statistics</span>\"\n            <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"10 characters\">word_count</span>\"\n            <span class=sf-dump-index>15</span> => \"<span class=sf-dump-str title=\"12 characters\">essay_length</span>\"\n            <span class=sf-dump-index>16</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n            <span class=sf-dump-index>17</span> => \"<span class=sf-dump-str title=\"13 characters\">error_message</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n          </samp>]\n        </samp>}\n        \"<span class=sf-dump-key>token</span>\" => \"<span class=sf-dump-str title=\"456 characters\">ZXlKcGRpSTZJbmM0UzIxQk5rdDZkVmxKV20wclVGUjZNRkY0Wm5jOVBTSXNJblpoYkhWbElqb2lOSEI1ZWtkV1owbFFUVkpGTld4NFRGSjRVRW80Y1VWMWN6UldaMFJzUjBzMGF6TTViVmt3V2pCNWVEbDRWWGRKTTA5ck9GRnVlbFpzZVdwcU4yRmhRbWR5WmxGWmRFMTJNMDFDYUhKa2RsTnpLMVF3YkdGYVNEZzFSMUZQTDFFdmNsVkplaTk1ZDBzMFVGUlVkbTlOWmtRME9EUXJlRlJ1SzFoaVlsSTBUbVVpTENKdFlXTWlPaUpsWlRCbE5qRXdZMk00WkRKbE0yRmtNamMyTW1JeE5XSTROekJsTW1VNE16ZGpNVEUxTVRrM09UZzBaRFE1WVRkbU1tVm1ZV1EzTjJRM05URmhNV1EwSWl3aWRHRm5Jam9pSW4wPQ==</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"53 characters\">vendor/laravel/framework/src/Illuminate/View/View.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"3 characters\">get</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"38 characters\">Illuminate\\View\\Engines\\CompilerEngine</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"82 characters\">C:\\laragon\\www\\ielts\\ielts-scoring-system\\resources\\views/scoring/public.blade.php</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>__env</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\View\\Factory\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\View</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Factory</span></span> {<a class=sf-dump-ref href=#sf-dump-2047274613-ref21306 title=\"2 occurrences\">#1306</a> &#8230;25}\n        \"<span class=sf-dump-key>app</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-2047274613-ref24 title=\"2 occurrences\">#4</a> &#8230;44}\n        \"<span class=sf-dump-key>errors</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\ViewErrorBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ViewErrorBag</span></span> {<a class=sf-dump-ref href=#sf-dump-2047274613-ref21422 title=\"2 occurrences\">#1422</a>}\n        \"<span class=sf-dump-key>attempt</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\ScoringAttempt\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ScoringAttempt</span></span> {<a class=sf-dump-ref href=#sf-dump-2047274613-ref21405 title=\"2 occurrences\">#1405</a>}\n        \"<span class=sf-dump-key>token</span>\" => \"<span class=sf-dump-str title=\"456 characters\">ZXlKcGRpSTZJbmM0UzIxQk5rdDZkVmxKV20wclVGUjZNRkY0Wm5jOVBTSXNJblpoYkhWbElqb2lOSEI1ZWtkV1owbFFUVkpGTld4NFRGSjRVRW80Y1VWMWN6UldaMFJzUjBzMGF6TTViVmt3V2pCNWVEbDRWWGRKTTA5ck9GRnVlbFpzZVdwcU4yRmhRbWR5WmxGWmRFMTJNMDFDYUhKa2RsTnpLMVF3YkdGYVNEZzFSMUZQTDFFdmNsVkplaTk1ZDBzMFVGUlVkbTlOWmtRME9EUXJlRlJ1SzFoaVlsSTBUbVVpTENKdFlXTWlPaUpsWlRCbE5qRXdZMk00WkRKbE0yRmtNamMyTW1JeE5XSTROekJsTW1VNE16ZGpNVEUxTVRrM09UZzBaRFE1WVRkbU1tVm1ZV1EzTjJRM05URmhNV1EwSWl3aWRHRm5Jam9pSW4wPQ==</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"53 characters\">vendor/laravel/framework/src/Illuminate/View/View.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>191</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"11 characters\">getContents</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Illuminate\\View\\View</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"53 characters\">vendor/laravel/framework/src/Illuminate/View/View.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>160</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"14 characters\">renderContents</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Illuminate\\View\\View</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"57 characters\">vendor/laravel/framework/src/Illuminate/Http/Response.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>78</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">render</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Illuminate\\View\\View</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"57 characters\">vendor/laravel/framework/src/Illuminate/Http/Response.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>34</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"10 characters\">setContent</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Illuminate\\Http\\Response</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">[object Illuminate\\View\\View]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>924</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"11 characters\">__construct</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Illuminate\\Http\\Response</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">[object Illuminate\\View\\View]</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-num>200</span>\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>Content-Type</span>\" => \"<span class=sf-dump-str title=\"9 characters\">text/html</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>891</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"10 characters\">toResponse</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">::</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"29 characters\">[object Illuminate\\View\\View]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>807</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"15 characters\">prepareResponse</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"29 characters\">[object Illuminate\\View\\View]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>169</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Routing\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>11</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>66</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>12</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Barryvdh\\Debugbar\\Middleware\\InjectDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>13</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"43 characters\">app/Http/Middleware/EnsureUserHasCredit.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>32</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>14</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">App\\Http\\Middleware\\EnsureUserHasCredit</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>15</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"81 characters\">vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>50</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>16</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"48 characters\">Illuminate\\Routing\\Middleware\\SubstituteBindings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>17</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>63</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>18</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Auth\\Middleware\\Authenticate</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>19</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"86 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>87</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>20</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"53 characters\">Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>21</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>48</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>22</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\View\\Middleware\\ShareErrorsFromSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>23</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"75 characters\">vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>120</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>24</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"75 characters\">vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>63</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"21 characters\">handleStatefulRequest</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Illuminate\\Session\\Middleware\\StartSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Session\\Store]</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>25</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Illuminate\\Session\\Middleware\\StartSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>26</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>36</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>27</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>28</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"76 characters\">vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>74</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>29</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Illuminate\\Cookie\\Middleware\\EncryptCookies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>30</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>126</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>31</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>807</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>32</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>786</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"19 characters\">runRouteWithinStack</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>33</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>750</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">runRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>34</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>739</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"15 characters\">dispatchToRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>35</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>200</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">dispatch</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>36</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>169</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Illuminate\\Foundation\\Http\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>37</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>66</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>38</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Barryvdh\\Debugbar\\Middleware\\InjectDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>39</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>40</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"96 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>31</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>41</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"63 characters\">Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>42</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>43</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>51</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>44</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\Foundation\\Http\\Middleware\\TrimStrings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>45</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"76 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>27</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>46</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Illuminate\\Http\\Middleware\\ValidatePostSize</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>47</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"103 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>109</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>48</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"70 characters\">Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>49</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"70 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>48</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>50</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"37 characters\">Illuminate\\Http\\Middleware\\HandleCors</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>51</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>58</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>52</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Http\\Middleware\\TrustProxies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>53</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"94 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>22</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>54</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"61 characters\">Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>55</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"80 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePathEncoding.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>26</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>56</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"47 characters\">Illuminate\\Http\\Middleware\\ValidatePathEncoding</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>57</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>126</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>58</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>175</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>59</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>144</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"24 characters\">sendRequestThroughRouter</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>60</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Application.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>1219</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>61</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"16 characters\">public/index.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>20</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"13 characters\">handleRequest</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Application</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>62</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"71 characters\">vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>23</span>\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"58 characters\">C:\\laragon\\www\\ielts\\ielts-scoring-system\\public\\index.php</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"12 characters\">require_once</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2047274613\", {\"maxDepth\":0})</script>\n", "surrounding_lines": ["            $value = $value->value;\n", "        }\n", "\n", "        return htmlspecialchars($value ?? '', ENT_QUOTES | ENT_SUBSTITUTE, 'UTF-8', $doubleEncode);\n", "    }\n", "}\n", "\n"], "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSupport%2Fhelpers.php&line=141", "ajax": false, "filename": "helpers.php", "line": "141"}}, {"type": "TypeError", "message": "htmlspecialchars(): Argument #1 ($string) must be of type string, array given", "code": 0, "file": "vendor/laravel/framework/src/Illuminate/Support/helpers.php", "line": 141, "stack_trace": null, "stack_trace_html": "<pre class=sf-dump id=sf-dump-548656238 data-indent-pad=\"  \"><span class=sf-dump-note>array:67</span> [<samp data-depth=1 class=sf-dump-expanded>\n  <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"59 characters\">vendor/laravel/framework/src/Illuminate/Support/helpers.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>141</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"16 characters\">htmlspecialchars</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>score</span>\" => <span class=sf-dump-num>3.5</span>\n        \"<span class=sf-dump-key>issues</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"49 characters\">Sai ch&#7911; ng&#7919;-&#273;&#7897;ng t&#7915;: &#8216;news is&#8230; it make student&#8230;&#8217;;</span>\"\n          <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"56 characters\">Thi&#7871;u m&#7841;o t&#7915;: &#8216;in other country&#8217;; sai th&#236;: &#8216;I thinking&#8217;;</span>\"\n          <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"64 characters\">C&#226;u thi&#7871;u th&#224;nh ph&#7847;n: &#8216;International news not really important&#8217;.</span>\"\n          <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"32 characters\">Vi&#7871;t hoa v&#224; ch&#7845;m c&#226;u ch&#432;a chu&#7849;n.</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>feedback</span>\" => \"<span class=sf-dump-str title=\"77 characters\">L&#7895;i ng&#7919; ph&#225;p c&#417; b&#7843;n xu&#7845;t hi&#7879;n th&#432;&#7901;ng xuy&#234;n; ch&#7911;-v&#7883;, th&#236; v&#224; m&#7841;o t&#7915; ch&#432;a chu&#7849;n.</span>\"\n        \"<span class=sf-dump-key>improvements</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">&#212;n l&#7841;i S-V agreement, m&#7841;o t&#7915;, danh t&#7915; s&#7889; nhi&#7873;u.</span>\"\n          <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"67 characters\">D&#249;ng c&#7845;u tr&#250;c ph&#7913;c t&#7841;p h&#417;n: Although&#8230;, in spite of&#8230;, would lead to&#8230;</span>\"\n          <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"49 characters\">T&#7921; &#273;&#7885;c l&#7841;i v&#224; s&#7917;a l&#7895;i ng&#7919; ph&#225;p tr&#432;&#7899;c khi n&#7897;p b&#224;i.</span>\"\n        </samp>]\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-num>11</span>\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"5 characters\">UTF-8</span>\"\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-const>true</span>\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"60 characters\">storage/framework/views/d6ccf6006ae9cc9eb654e17fa393b764.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>361</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str>e</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>score</span>\" => <span class=sf-dump-num>3.5</span>\n        \"<span class=sf-dump-key>issues</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"49 characters\">Sai ch&#7911; ng&#7919;-&#273;&#7897;ng t&#7915;: &#8216;news is&#8230; it make student&#8230;&#8217;;</span>\"\n          <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"56 characters\">Thi&#7871;u m&#7841;o t&#7915;: &#8216;in other country&#8217;; sai th&#236;: &#8216;I thinking&#8217;;</span>\"\n          <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"64 characters\">C&#226;u thi&#7871;u th&#224;nh ph&#7847;n: &#8216;International news not really important&#8217;.</span>\"\n          <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"32 characters\">Vi&#7871;t hoa v&#224; ch&#7845;m c&#226;u ch&#432;a chu&#7849;n.</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>feedback</span>\" => \"<span class=sf-dump-str title=\"77 characters\">L&#7895;i ng&#7919; ph&#225;p c&#417; b&#7843;n xu&#7845;t hi&#7879;n th&#432;&#7901;ng xuy&#234;n; ch&#7911;-v&#7883;, th&#236; v&#224; m&#7841;o t&#7915; ch&#432;a chu&#7849;n.</span>\"\n        \"<span class=sf-dump-key>improvements</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">&#212;n l&#7841;i S-V agreement, m&#7841;o t&#7915;, danh t&#7915; s&#7889; nhi&#7873;u.</span>\"\n          <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"67 characters\">D&#249;ng c&#7845;u tr&#250;c ph&#7913;c t&#7841;p h&#417;n: Although&#8230;, in spite of&#8230;, would lead to&#8230;</span>\"\n          <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"49 characters\">T&#7921; &#273;&#7885;c l&#7841;i v&#224; s&#7917;a l&#7895;i ng&#7919; ph&#225;p tr&#432;&#7899;c khi n&#7897;p b&#224;i.</span>\"\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"65 characters\">vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>123</span>\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"102 characters\">C:\\laragon\\www\\ielts\\ielts-scoring-system\\storage\\framework\\views\\d6ccf6006ae9cc9eb654e17fa393b764.php</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"7 characters\">require</span>\"\n  </samp>]\n  <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"65 characters\">vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>124</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"31 characters\">Illuminate\\Filesystem\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Illuminate\\Filesystem\\Filesystem</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">::</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>57</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"10 characters\">getRequire</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Illuminate\\Filesystem\\Filesystem</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"102 characters\">C:\\laragon\\www\\ielts\\ielts-scoring-system\\storage\\framework\\views/d6ccf6006ae9cc9eb654e17fa393b764.php</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>__env</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\View\\Factory\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\View</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Factory</span></span> {<a class=sf-dump-ref href=#sf-dump-548656238-ref21306 title=\"3 occurrences\">#1306</a> &#8230;25}\n        \"<span class=sf-dump-key>app</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-548656238-ref24 title=\"3 occurrences\">#4</a> &#8230;44}\n        \"<span class=sf-dump-key>errors</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\ViewErrorBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ViewErrorBag</span></span> {<a class=sf-dump-ref href=#sf-dump-548656238-ref21422 title=\"3 occurrences\">#1422</a><samp data-depth=5 id=sf-dump-548656238-ref21422 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []\n        </samp>}\n        \"<span class=sf-dump-key>attempt</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\ScoringAttempt\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ScoringAttempt</span></span> {<a class=sf-dump-ref href=#sf-dump-548656238-ref21405 title=\"3 occurrences\">#1405</a><samp data-depth=5 id=sf-dump-548656238-ref21405 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"16 characters\">scoring_attempts</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n          +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n          #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n          +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n          +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n          +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:21</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>10</span>\n            \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-num>3</span>\n            \"<span class=sf-dump-key>essay_question_id</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>essay_content</span>\" => \"\"\"\n              <span class=sf-dump-str title=\"1184 characters\">International news is good for student but sometime it is not nessessary. Many people think student in secondary school need to study about international news, other think this waste their time. My essay will write about both of these thing.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n              <span class=sf-dump-str title=\"1184 characters\">Firstly, International news is good because it make student smart. When student learn international news they can know many things. Example, student can know weather in other country, famous people life, or footbal match. They will very interesting about these topic and can relax in the classroom. Learning international news is also help student speaking english well.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n              <span class=sf-dump-str title=\"1184 characters\">However, I thinking international news sometime is boring. Student have many important subject, example maths, literature, or chemistry. These subjects can help student become doctor or engineer in future. International news not really important, only waste of time.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n              <span class=sf-dump-str title=\"1184 characters\">In my opinion, secondary student should play sport and learn math better than learn international news. Teacher should teaching student good subject not international news.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n              <span class=sf-dump-str title=\"1184 characters\">I conclusion, international news have advantage but more disadvantage. Student no need study this subject, waste of their time.</span>\n              \"\"\"\n            \"<span class=sf-dump-key>task_type</span>\" => \"<span class=sf-dump-str title=\"5 characters\">task2</span>\"\n            \"<span class=sf-dump-key>time_limit</span>\" => <span class=sf-dump-num>40</span>\n            \"<span class=sf-dump-key>overall_band_score</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.0</span>\"\n            \"<span class=sf-dump-key>task_achievement</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.0</span>\"\n            \"<span class=sf-dump-key>coherence_cohesion</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.5</span>\"\n            \"<span class=sf-dump-key>lexical_resource</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.0</span>\"\n            \"<span class=sf-dump-key>grammar_accuracy</span>\" => \"<span class=sf-dump-str title=\"3 characters\">3.5</span>\"\n            \"<span class=sf-dump-key>detailed_feedback</span>\" => \"<span class=sf-dump-str title=\"2203 characters\">{&quot;grammar_accuracy&quot;: {&quot;score&quot;: 3.5, &quot;issues&quot;: [&quot;Sai ch&#7911; ng&#7919;-&#273;&#7897;ng t&#7915;: &#8216;news is&#8230; it make student&#8230;&#8217;;&quot;, &quot;Thi&#7871;u m&#7841;o t&#7915;: &#8216;in other country&#8217;; sai th&#236;: &#8216;I thinking&#8217;;&quot;, &quot;C&#226;u thi&#7871;u th&#224;nh ph&#7847;n: &#8216;International news not really important&#8217;.&quot;, &quot;Vi&#7871;t hoa v&#224; ch&#7845;m c&#226;u ch&#432;a chu&#7849;n.&quot;], &quot;feedback&quot;: &quot;L&#7895;i ng&#7919; ph&#225;p c&#417; b&#7843;n xu&#7845;t hi&#7879;n th&#432;&#7901;ng xuy&#234;n; ch&#7911;-v&#7883;, th&#236; v&#224; m&#7841;o t&#7915; ch&#432;a chu&#7849;n.&quot;, &quot;improvements&quot;: [&quot;&#212;n l&#7841;i S-V agreement, m&#7841;o t&#7915;, danh t&#7915; s&#7889; nhi&#7873;u.&quot;, &quot;D&#249;ng c&#7845;u tr&#250;c ph&#7913;c t&#7841;p h&#417;n: Although&#8230;, in spite of&#8230;, would lead to&#8230;&quot;, &quot;T&#7921; &#273;&#7885;c l&#7841;i v&#224; s&#7917;a l&#7895;i ng&#7919; ph&#225;p tr&#432;&#7899;c khi n&#7897;p b&#224;i.&quot;]}, &quot;lexical_resource&quot;: {&quot;score&quot;: 4, &quot;issues&quot;: [&quot;L&#7863;p &#273;i l&#7863;p l&#7841;i &#8216;student&#8217;, &#8216;international news&#8217;.&quot;, &quot;Sai ch&#237;nh t&#7843;: nessessary, footbal, interesting, advantage v.v.&quot;, &quot;D&#249;ng t&#7915; ch&#432;a chu&#7849;n: &#8216;smart&#8217; (n&#234;n thay b&#7857;ng &#8216;knowledgeable&#8217;), &#8216;good subject&#8217;.&quot;], &quot;feedback&quot;: &quot;T&#7915; v&#7921;ng &#7903; m&#7913;c c&#417; b&#7843;n, l&#7863;p nhi&#7873;u, c&#243; sai ch&#237;nh t&#7843; v&#224; d&#249;ng t&#7915; kh&#244;ng ch&#237;nh x&#225;c.&quot;, &quot;improvements&quot;: [&quot;H&#7885;c th&#234;m collocations: keep abreast of international affairs, broaden horizons&#8230;&quot;, &quot;Ki&#7875;m tra ch&#237;nh t&#7843; sau khi vi&#7871;t.&quot;, &quot;D&#249;ng t&#7915; &#273;a d&#7841;ng h&#417;n, tr&#225;nh l&#7863;p; s&#7917; d&#7909;ng t&#7915; &#273;&#7891;ng ngh&#297;a, paraphrase.&quot;]}, &quot;task_achievement&quot;: {&quot;score&quot;: 4, &quot;issues&quot;: [&quot;Thi&#7871;u t&#7915; (179/250 words) &#8594; b&#7883; tr&#7915; &#273;i&#7875;m.&quot;, &quot;L&#7853;p lu&#7853;n &#273;&#417;n gi&#7843;n, ch&#432;a c&#243; v&#237; d&#7909; c&#7909; th&#7875;, s&#7889; li&#7879;u ho&#7863;c ph&#226;n t&#237;ch s&#226;u.&quot;, &quot;Ch&#432;a tr&#7843; l&#7901;i &#273;&#7847;y &#273;&#7911; c&#7843; hai quan &#273;i&#7875;m m&#7897;t c&#225;ch c&#226;n b&#7857;ng.&quot;], &quot;feedback&quot;: &quot;B&#224;i vi&#7871;t ch&#432;a &#273;&#225;p &#7913;ng &#273;&#7911; y&#234;u c&#7847;u t&#7889;i thi&#7875;u 250 t&#7915;, &#253; c&#242;n s&#417; s&#224;i v&#224; minh ho&#7841; ch&#432;a thuy&#7871;t ph&#7909;c.&quot;, &quot;improvements&quot;: [&quot;Vi&#7871;t t&#7889;i thi&#7875;u 260 t&#7915; &#273;&#7875; tr&#225;nh tr&#7915; &#273;i&#7875;m.&quot;, &quot;Tri&#7875;n khai m&#7895;i l&#7853;p lu&#7853;n v&#7899;i v&#237; d&#7909; th&#7921;c t&#7871;, s&#7889; li&#7879;u, ho&#7863;c d&#7851;n ch&#7913;ng c&#225; nh&#226;n.&quot;, &quot;N&#234;u r&#245; l&#7853;p tr&#432;&#7901;ng ngay ph&#7847;n m&#7903; b&#224;i v&#224; nh&#7855;c l&#7841;i trong k&#7871;t lu&#7853;n.&quot;]}, &quot;coherence_cohesion&quot;: {&quot;score&quot;: 4.5, &quot;issues&quot;: [&quot;T&#7915; n&#7889;i ngh&#232;o n&#224;n, h&#7847;u nh&#432; ch&#7881; d&#249;ng &#8216;Firstly&#8217;, &#8216;However&#8217;.&quot;, &quot;C&#225;c &#273;o&#7841;n qu&#225; ng&#7855;n, &#253; chuy&#7875;n &#273;&#7897;t ng&#7897;t, ch&#432;a c&#243; c&#226;u ch&#7911; &#273;&#7873; r&#245; r&#224;ng.&quot;, &quot;Thi&#7871;u m&#7841;ch logic gi&#7919;a c&#225;c c&#226;u.&quot;], &quot;feedback&quot;: &quot;B&#7889; c&#7909;c c&#243; m&#7903; th&#226;n k&#7871;t nh&#432;ng li&#234;n k&#7871;t &#253; c&#242;n r&#7901;i r&#7841;c, t&#7915; n&#7889;i h&#7841;n ch&#7871; v&#224; l&#7863;p l&#7841;i.&quot;, &quot;improvements&quot;: [&quot;S&#7917; d&#7909;ng &#273;a d&#7841;ng linking words: In addition, Moreover, On the other hand, Consequently&#8230;&quot;, &quot;M&#7895;i &#273;o&#7841;n th&#226;n b&#224;i: 1 c&#226;u ch&#7911; &#273;&#7873; &#8211; 2-3 c&#226;u gi&#7843;i th&#237;ch &#8211; 1 v&#237; d&#7909; &#8211; 1 c&#226;u k&#7871;t.&quot;, &quot;Tr&#225;nh l&#7863;p t&#7915; &#8216;student&#8217;, &#8216;international news&#8217; qu&#225; nhi&#7873;u; d&#249;ng &#273;&#7841;i t&#7915; thay th&#7871;.&quot;]}}</span>\"\n            \"<span class=sf-dump-key>highlighted_corrections</span>\" => \"<span class=sf-dump-str title=\"2895 characters\">[{&quot;severity&quot;: &quot;high&quot;, &quot;error_type&quot;: &quot;spelling&quot;, &quot;explanation&quot;: &quot;Sai ch&#237;nh t&#7843; &#8216;sometime&#8217; (thi&#7871;u &#8216;s&#8217;) v&#224; &#8216;nessessary&#8217;.&quot;, &quot;original_text&quot;: &quot;sometime it is not nessessary&quot;, &quot;suggested_correction&quot;: &quot;sometimes it is not necessary&quot;}, {&quot;severity&quot;: &quot;medium&quot;, &quot;error_type&quot;: &quot;grammar&quot;, &quot;explanation&quot;: &quot;Thi&#7871;u &#8216;s&#8217; &#7903; danh t&#7915; s&#7889; nhi&#7873;u v&#224; m&#7841;o t&#7915; &#8216;schools&#8217;.&quot;, &quot;original_text&quot;: &quot;student in secondary school&quot;, &quot;suggested_correction&quot;: &quot;students in secondary schools&quot;}, {&quot;severity&quot;: &quot;medium&quot;, &quot;error_type&quot;: &quot;grammar&quot;, &quot;explanation&quot;: &quot;&#272;&#7897;ng t&#7915; ph&#7843;i chia s&#7889; &#237;t &#8216;wastes&#8217; khi ch&#7911; ng&#7919; l&#224; &#8216;this&#8217;.&quot;, &quot;original_text&quot;: &quot;this waste their time&quot;, &quot;suggested_correction&quot;: &quot;this wastes their time&quot;}, {&quot;severity&quot;: &quot;high&quot;, &quot;error_type&quot;: &quot;grammar|vocabulary&quot;, &quot;explanation&quot;: &quot;Chia &#273;&#7897;ng t&#7915; &#8216;makes&#8217;; d&#249;ng t&#7915; ph&#249; h&#7907;p &#8216;beneficial&#8217;, &#8216;knowledgeable&#8217;.&quot;, &quot;original_text&quot;: &quot;International news is good because it make student smart&quot;, &quot;suggested_correction&quot;: &quot;International news is beneficial because it makes students more knowledgeable&quot;}, {&quot;severity&quot;: &quot;medium&quot;, &quot;error_type&quot;: &quot;grammar|vocabulary&quot;, &quot;explanation&quot;: &quot;Thi&#7871;u c&#7909;m &#8216;For example&#8217;; &#8216;know weather&#8217; &#8594; &#8216;learn about the weather&#8217;; &#8216;country&#8217; &#8594; &#8216;countries&#8217;.&quot;, &quot;original_text&quot;: &quot;Example, student can know weather in other country&quot;, &quot;suggested_correction&quot;: &quot;For example, students can learn about the weather in other countries&quot;}, {&quot;severity&quot;: &quot;high&quot;, &quot;error_type&quot;: &quot;grammar|vocabulary&quot;, &quot;explanation&quot;: &quot;Sai c&#7845;u tr&#250;c &#8216;is also help&#8217;; c&#7847;n &#8216;helps&#8217;; vi&#7871;t hoa &#8216;English&#8217;.&quot;, &quot;original_text&quot;: &quot;Learning international news is also help student speaking english well.&quot;, &quot;suggested_correction&quot;: &quot;Learning international news also helps students improve their English speaking skills.&quot;}, {&quot;severity&quot;: &quot;medium&quot;, &quot;error_type&quot;: &quot;grammar&quot;, &quot;explanation&quot;: &quot;Sai th&#236; ti&#7871;p di&#7877;n, v&#7883; tr&#237; &#8216;sometimes&#8217;.&quot;, &quot;original_text&quot;: &quot;I thinking international news sometime is boring.&quot;, &quot;suggested_correction&quot;: &quot;I think international news is sometimes boring.&quot;}, {&quot;severity&quot;: &quot;medium&quot;, &quot;error_type&quot;: &quot;grammar|punctuation&quot;, &quot;explanation&quot;: &quot;Danh t&#7915; s&#7889; nhi&#7873;u; thi&#7871;u d&#7845;u ph&#7849;y; m&#7841;o t&#7915;.&quot;, &quot;original_text&quot;: &quot;Student have many important subject, example maths, literature, or chemistry.&quot;, &quot;suggested_correction&quot;: &quot;Students have many important subjects, for example, maths, literature or chemistry.&quot;}, {&quot;severity&quot;: &quot;high&quot;, &quot;error_type&quot;: &quot;grammar&quot;, &quot;explanation&quot;: &quot;Thi&#7871;u &#273;&#7897;ng t&#7915; &#8216;is&#8217;; c&#7847;n m&#7841;o t&#7915; &#8216;a&#8217;.&quot;, &quot;original_text&quot;: &quot;International news not really important, only waste of time.&quot;, &quot;suggested_correction&quot;: &quot;International news is not really important; it is only a waste of time.&quot;}, {&quot;severity&quot;: &quot;high&quot;, &quot;error_type&quot;: &quot;grammar|spelling&quot;, &quot;explanation&quot;: &quot;C&#7847;n &#8216;In conclusion&#8217;; &#8216;news&#8217; coi nh&#432; s&#7889; &#237;t; th&#234;m &#8216;some&#8217;; danh t&#7915; s&#7889; nhi&#7873;u &#8216;disadvantages&#8217;.&quot;, &quot;original_text&quot;: &quot;I conclusion, international news have advantage but more disadvantage.&quot;, &quot;suggested_correction&quot;: &quot;In conclusion, international news has some advantages but more disadvantages.&quot;}]</span>\"\n            \"<span class=sf-dump-key>criteria_analysis</span>\" => \"<span class=sf-dump-str title=\"1493 characters\">[{&quot;criterion&quot;: &quot;Task Achievement&quot;, &quot;strengths&quot;: [&quot;&#272;&#227; n&#234;u &#273;&#432;&#7907;c hai quan &#273;i&#7875;m&quot;, &quot;C&#243; l&#7853;p tr&#432;&#7901;ng c&#225; nh&#226;n trong &#273;o&#7841;n k&#7871;t&quot;], &quot;weaknesses&quot;: [&quot;D&#432;&#7899;i 250 t&#7915; n&#234;n b&#7883; tr&#7915; &#273;i&#7875;m&quot;, &quot;L&#7853;p lu&#7853;n h&#7901;i h&#7907;t, v&#237; d&#7909; chung chung&quot;, &quot;Kh&#244;ng gi&#7843;i th&#237;ch v&#236; sao tin qu&#7889;c t&#7871; gi&#250;p n&#243;i ti&#7871;ng Anh t&#7889;t h&#417;n&quot;, &quot;Kh&#244;ng ph&#7843;n bi&#7879;n l&#7841;i quan &#273;i&#7875;m &#273;&#7889;i l&#7853;p&quot;], &quot;band_justification&quot;: &quot;&#272;&#225;p &#7913;ng ph&#7847;n n&#224;o y&#234;u c&#7847;u &#273;&#7873; nh&#432;ng ph&#225;t tri&#7875;n ch&#432;a &#273;&#7911;, thi&#7871;u chi&#7873;u s&#226;u v&#224; thi&#7871;u t&#7915; &#8594; band 4.0.&quot;}, {&quot;criterion&quot;: &quot;Coherence &amp; Cohesion&quot;, &quot;strengths&quot;: [&quot;C&#243; b&#7889; c&#7909;c c&#417; b&#7843;n: m&#7903;&#8211;th&#226;n&#8211;k&#7871;t&quot;, &quot;M&#7897;t s&#7889; t&#7915; n&#7889;i &#273;&#432;&#7907;c d&#249;ng (Firstly, However)&quot;], &quot;weaknesses&quot;: [&quot;Li&#234;n k&#7871;t &#253; k&#233;m, chuy&#7875;n &#273;o&#7841;n &#273;&#7897;t ng&#7897;t&quot;, &quot;C&#226;u ch&#7911; &#273;&#7873; m&#7901; nh&#7841;t&quot;, &quot;D&#249;ng l&#7863;p t&#7915; n&#7889;i, ch&#432;a &#273;a d&#7841;ng&quot;], &quot;band_justification&quot;: &quot;Li&#234;n k&#7871;t &#7903; m&#7913;c h&#7841;n ch&#7871;, &#253; &#273;&#244;i khi r&#7901;i r&#7841;c nh&#432;ng v&#7851;n theo tr&#236;nh t&#7921; chung &#8594; 4.5.&quot;}, {&quot;criterion&quot;: &quot;Lexical Resource&quot;, &quot;strengths&quot;: [&quot;T&#7915; v&#7921;ng quen thu&#7897;c, d&#7877; hi&#7875;u&quot;, &quot;M&#7897;t v&#224;i t&#7915; h&#7885;c thu&#7853;t &#273;&#417;n gi&#7843;n (advantage, disadvantage)&quot;], &quot;weaknesses&quot;: [&quot;L&#7863;p t&#7915; th&#432;&#7901;ng xuy&#234;n&quot;, &quot;Sai ch&#237;nh t&#7843; nhi&#7873;u&quot;, &quot;D&#249;ng t&#7915; ch&#432;a ch&#237;nh x&#225;c ho&#7863;c kh&#244;ng ph&#249; h&#7907;p v&#259;n phong h&#7885;c thu&#7853;t&quot;], &quot;band_justification&quot;: &quot;V&#7889;n t&#7915; h&#7841;n ch&#7871;, l&#7895;i ch&#237;nh t&#7843; &amp; ch&#7885;n t&#7915; &#7843;nh h&#432;&#7903;ng ng&#432;&#7901;i &#273;&#7885;c &#8594; 4.0.&quot;}, {&quot;criterion&quot;: &quot;Grammatical Range &amp; Accuracy&quot;, &quot;strengths&quot;: [&quot;Bi&#7871;t k&#7871;t th&#250;c c&#226;u b&#7857;ng d&#7845;u ch&#7845;m&quot;, &quot;M&#7897;t s&#7889; m&#7879;nh &#273;&#7873; &#273;&#417;n ch&#237;nh x&#225;c&quot;], &quot;weaknesses&quot;: [&quot;L&#7895;i S-V agreement li&#234;n t&#7909;c&quot;, &quot;Thi&#7871;u m&#7841;o t&#7915;, gi&#7899;i t&#7915;&quot;, &quot;C&#7845;u tr&#250;c c&#226;u &#273;&#417;n gi&#7843;n, hi&#7871;m c&#226;u ph&#7913;c&quot;, &quot;Sai th&#236; v&#224; thi&#7871;u &#273;&#7897;ng t&#7915; to be&quot;], &quot;band_justification&quot;: &quot;L&#7895;i th&#432;&#7901;ng xuy&#234;n khi&#7871;n kh&#243; hi&#7875;u, c&#7845;u tr&#250;c &#273;&#417;n gi&#7843;n &#8594; 3.5.&quot;}]</span>\"\n            \"<span class=sf-dump-key>statistics</span>\" => \"<span class=sf-dump-str title=\"145 characters\">{&quot;total_errors&quot;: 45, &quot;grammar_errors&quot;: 28, &quot;spelling_errors&quot;: 10, &quot;complexity_level&quot;: &quot;basic&quot;, &quot;sentence_variety&quot;: &quot;low&quot;, &quot;vocabulary_errors&quot;: 7}</span>\"\n            \"<span class=sf-dump-key>word_count</span>\" => <span class=sf-dump-num>179</span>\n            \"<span class=sf-dump-key>essay_length</span>\" => <span class=sf-dump-num>1184</span>\n            \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"9 characters\">completed</span>\"\n            \"<span class=sf-dump-key>error_message</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-18 17:02:55</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-18 17:03:22</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:21</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>10</span>\n            \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-num>3</span>\n            \"<span class=sf-dump-key>essay_question_id</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>essay_content</span>\" => \"\"\"\n              <span class=sf-dump-str title=\"1184 characters\">International news is good for student but sometime it is not nessessary. Many people think student in secondary school need to study about international news, other think this waste their time. My essay will write about both of these thing.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n              <span class=sf-dump-str title=\"1184 characters\">Firstly, International news is good because it make student smart. When student learn international news they can know many things. Example, student can know weather in other country, famous people life, or footbal match. They will very interesting about these topic and can relax in the classroom. Learning international news is also help student speaking english well.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n              <span class=sf-dump-str title=\"1184 characters\">However, I thinking international news sometime is boring. Student have many important subject, example maths, literature, or chemistry. These subjects can help student become doctor or engineer in future. International news not really important, only waste of time.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n              <span class=sf-dump-str title=\"1184 characters\">In my opinion, secondary student should play sport and learn math better than learn international news. Teacher should teaching student good subject not international news.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n              <span class=sf-dump-str title=\"1184 characters\">I conclusion, international news have advantage but more disadvantage. Student no need study this subject, waste of their time.</span>\n              \"\"\"\n            \"<span class=sf-dump-key>task_type</span>\" => \"<span class=sf-dump-str title=\"5 characters\">task2</span>\"\n            \"<span class=sf-dump-key>time_limit</span>\" => <span class=sf-dump-num>40</span>\n            \"<span class=sf-dump-key>overall_band_score</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.0</span>\"\n            \"<span class=sf-dump-key>task_achievement</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.0</span>\"\n            \"<span class=sf-dump-key>coherence_cohesion</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.5</span>\"\n            \"<span class=sf-dump-key>lexical_resource</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.0</span>\"\n            \"<span class=sf-dump-key>grammar_accuracy</span>\" => \"<span class=sf-dump-str title=\"3 characters\">3.5</span>\"\n            \"<span class=sf-dump-key>detailed_feedback</span>\" => \"<span class=sf-dump-str title=\"2203 characters\">{&quot;grammar_accuracy&quot;: {&quot;score&quot;: 3.5, &quot;issues&quot;: [&quot;Sai ch&#7911; ng&#7919;-&#273;&#7897;ng t&#7915;: &#8216;news is&#8230; it make student&#8230;&#8217;;&quot;, &quot;Thi&#7871;u m&#7841;o t&#7915;: &#8216;in other country&#8217;; sai th&#236;: &#8216;I thinking&#8217;;&quot;, &quot;C&#226;u thi&#7871;u th&#224;nh ph&#7847;n: &#8216;International news not really important&#8217;.&quot;, &quot;Vi&#7871;t hoa v&#224; ch&#7845;m c&#226;u ch&#432;a chu&#7849;n.&quot;], &quot;feedback&quot;: &quot;L&#7895;i ng&#7919; ph&#225;p c&#417; b&#7843;n xu&#7845;t hi&#7879;n th&#432;&#7901;ng xuy&#234;n; ch&#7911;-v&#7883;, th&#236; v&#224; m&#7841;o t&#7915; ch&#432;a chu&#7849;n.&quot;, &quot;improvements&quot;: [&quot;&#212;n l&#7841;i S-V agreement, m&#7841;o t&#7915;, danh t&#7915; s&#7889; nhi&#7873;u.&quot;, &quot;D&#249;ng c&#7845;u tr&#250;c ph&#7913;c t&#7841;p h&#417;n: Although&#8230;, in spite of&#8230;, would lead to&#8230;&quot;, &quot;T&#7921; &#273;&#7885;c l&#7841;i v&#224; s&#7917;a l&#7895;i ng&#7919; ph&#225;p tr&#432;&#7899;c khi n&#7897;p b&#224;i.&quot;]}, &quot;lexical_resource&quot;: {&quot;score&quot;: 4, &quot;issues&quot;: [&quot;L&#7863;p &#273;i l&#7863;p l&#7841;i &#8216;student&#8217;, &#8216;international news&#8217;.&quot;, &quot;Sai ch&#237;nh t&#7843;: nessessary, footbal, interesting, advantage v.v.&quot;, &quot;D&#249;ng t&#7915; ch&#432;a chu&#7849;n: &#8216;smart&#8217; (n&#234;n thay b&#7857;ng &#8216;knowledgeable&#8217;), &#8216;good subject&#8217;.&quot;], &quot;feedback&quot;: &quot;T&#7915; v&#7921;ng &#7903; m&#7913;c c&#417; b&#7843;n, l&#7863;p nhi&#7873;u, c&#243; sai ch&#237;nh t&#7843; v&#224; d&#249;ng t&#7915; kh&#244;ng ch&#237;nh x&#225;c.&quot;, &quot;improvements&quot;: [&quot;H&#7885;c th&#234;m collocations: keep abreast of international affairs, broaden horizons&#8230;&quot;, &quot;Ki&#7875;m tra ch&#237;nh t&#7843; sau khi vi&#7871;t.&quot;, &quot;D&#249;ng t&#7915; &#273;a d&#7841;ng h&#417;n, tr&#225;nh l&#7863;p; s&#7917; d&#7909;ng t&#7915; &#273;&#7891;ng ngh&#297;a, paraphrase.&quot;]}, &quot;task_achievement&quot;: {&quot;score&quot;: 4, &quot;issues&quot;: [&quot;Thi&#7871;u t&#7915; (179/250 words) &#8594; b&#7883; tr&#7915; &#273;i&#7875;m.&quot;, &quot;L&#7853;p lu&#7853;n &#273;&#417;n gi&#7843;n, ch&#432;a c&#243; v&#237; d&#7909; c&#7909; th&#7875;, s&#7889; li&#7879;u ho&#7863;c ph&#226;n t&#237;ch s&#226;u.&quot;, &quot;Ch&#432;a tr&#7843; l&#7901;i &#273;&#7847;y &#273;&#7911; c&#7843; hai quan &#273;i&#7875;m m&#7897;t c&#225;ch c&#226;n b&#7857;ng.&quot;], &quot;feedback&quot;: &quot;B&#224;i vi&#7871;t ch&#432;a &#273;&#225;p &#7913;ng &#273;&#7911; y&#234;u c&#7847;u t&#7889;i thi&#7875;u 250 t&#7915;, &#253; c&#242;n s&#417; s&#224;i v&#224; minh ho&#7841; ch&#432;a thuy&#7871;t ph&#7909;c.&quot;, &quot;improvements&quot;: [&quot;Vi&#7871;t t&#7889;i thi&#7875;u 260 t&#7915; &#273;&#7875; tr&#225;nh tr&#7915; &#273;i&#7875;m.&quot;, &quot;Tri&#7875;n khai m&#7895;i l&#7853;p lu&#7853;n v&#7899;i v&#237; d&#7909; th&#7921;c t&#7871;, s&#7889; li&#7879;u, ho&#7863;c d&#7851;n ch&#7913;ng c&#225; nh&#226;n.&quot;, &quot;N&#234;u r&#245; l&#7853;p tr&#432;&#7901;ng ngay ph&#7847;n m&#7903; b&#224;i v&#224; nh&#7855;c l&#7841;i trong k&#7871;t lu&#7853;n.&quot;]}, &quot;coherence_cohesion&quot;: {&quot;score&quot;: 4.5, &quot;issues&quot;: [&quot;T&#7915; n&#7889;i ngh&#232;o n&#224;n, h&#7847;u nh&#432; ch&#7881; d&#249;ng &#8216;Firstly&#8217;, &#8216;However&#8217;.&quot;, &quot;C&#225;c &#273;o&#7841;n qu&#225; ng&#7855;n, &#253; chuy&#7875;n &#273;&#7897;t ng&#7897;t, ch&#432;a c&#243; c&#226;u ch&#7911; &#273;&#7873; r&#245; r&#224;ng.&quot;, &quot;Thi&#7871;u m&#7841;ch logic gi&#7919;a c&#225;c c&#226;u.&quot;], &quot;feedback&quot;: &quot;B&#7889; c&#7909;c c&#243; m&#7903; th&#226;n k&#7871;t nh&#432;ng li&#234;n k&#7871;t &#253; c&#242;n r&#7901;i r&#7841;c, t&#7915; n&#7889;i h&#7841;n ch&#7871; v&#224; l&#7863;p l&#7841;i.&quot;, &quot;improvements&quot;: [&quot;S&#7917; d&#7909;ng &#273;a d&#7841;ng linking words: In addition, Moreover, On the other hand, Consequently&#8230;&quot;, &quot;M&#7895;i &#273;o&#7841;n th&#226;n b&#224;i: 1 c&#226;u ch&#7911; &#273;&#7873; &#8211; 2-3 c&#226;u gi&#7843;i th&#237;ch &#8211; 1 v&#237; d&#7909; &#8211; 1 c&#226;u k&#7871;t.&quot;, &quot;Tr&#225;nh l&#7863;p t&#7915; &#8216;student&#8217;, &#8216;international news&#8217; qu&#225; nhi&#7873;u; d&#249;ng &#273;&#7841;i t&#7915; thay th&#7871;.&quot;]}}</span>\"\n            \"<span class=sf-dump-key>highlighted_corrections</span>\" => \"<span class=sf-dump-str title=\"2895 characters\">[{&quot;severity&quot;: &quot;high&quot;, &quot;error_type&quot;: &quot;spelling&quot;, &quot;explanation&quot;: &quot;Sai ch&#237;nh t&#7843; &#8216;sometime&#8217; (thi&#7871;u &#8216;s&#8217;) v&#224; &#8216;nessessary&#8217;.&quot;, &quot;original_text&quot;: &quot;sometime it is not nessessary&quot;, &quot;suggested_correction&quot;: &quot;sometimes it is not necessary&quot;}, {&quot;severity&quot;: &quot;medium&quot;, &quot;error_type&quot;: &quot;grammar&quot;, &quot;explanation&quot;: &quot;Thi&#7871;u &#8216;s&#8217; &#7903; danh t&#7915; s&#7889; nhi&#7873;u v&#224; m&#7841;o t&#7915; &#8216;schools&#8217;.&quot;, &quot;original_text&quot;: &quot;student in secondary school&quot;, &quot;suggested_correction&quot;: &quot;students in secondary schools&quot;}, {&quot;severity&quot;: &quot;medium&quot;, &quot;error_type&quot;: &quot;grammar&quot;, &quot;explanation&quot;: &quot;&#272;&#7897;ng t&#7915; ph&#7843;i chia s&#7889; &#237;t &#8216;wastes&#8217; khi ch&#7911; ng&#7919; l&#224; &#8216;this&#8217;.&quot;, &quot;original_text&quot;: &quot;this waste their time&quot;, &quot;suggested_correction&quot;: &quot;this wastes their time&quot;}, {&quot;severity&quot;: &quot;high&quot;, &quot;error_type&quot;: &quot;grammar|vocabulary&quot;, &quot;explanation&quot;: &quot;Chia &#273;&#7897;ng t&#7915; &#8216;makes&#8217;; d&#249;ng t&#7915; ph&#249; h&#7907;p &#8216;beneficial&#8217;, &#8216;knowledgeable&#8217;.&quot;, &quot;original_text&quot;: &quot;International news is good because it make student smart&quot;, &quot;suggested_correction&quot;: &quot;International news is beneficial because it makes students more knowledgeable&quot;}, {&quot;severity&quot;: &quot;medium&quot;, &quot;error_type&quot;: &quot;grammar|vocabulary&quot;, &quot;explanation&quot;: &quot;Thi&#7871;u c&#7909;m &#8216;For example&#8217;; &#8216;know weather&#8217; &#8594; &#8216;learn about the weather&#8217;; &#8216;country&#8217; &#8594; &#8216;countries&#8217;.&quot;, &quot;original_text&quot;: &quot;Example, student can know weather in other country&quot;, &quot;suggested_correction&quot;: &quot;For example, students can learn about the weather in other countries&quot;}, {&quot;severity&quot;: &quot;high&quot;, &quot;error_type&quot;: &quot;grammar|vocabulary&quot;, &quot;explanation&quot;: &quot;Sai c&#7845;u tr&#250;c &#8216;is also help&#8217;; c&#7847;n &#8216;helps&#8217;; vi&#7871;t hoa &#8216;English&#8217;.&quot;, &quot;original_text&quot;: &quot;Learning international news is also help student speaking english well.&quot;, &quot;suggested_correction&quot;: &quot;Learning international news also helps students improve their English speaking skills.&quot;}, {&quot;severity&quot;: &quot;medium&quot;, &quot;error_type&quot;: &quot;grammar&quot;, &quot;explanation&quot;: &quot;Sai th&#236; ti&#7871;p di&#7877;n, v&#7883; tr&#237; &#8216;sometimes&#8217;.&quot;, &quot;original_text&quot;: &quot;I thinking international news sometime is boring.&quot;, &quot;suggested_correction&quot;: &quot;I think international news is sometimes boring.&quot;}, {&quot;severity&quot;: &quot;medium&quot;, &quot;error_type&quot;: &quot;grammar|punctuation&quot;, &quot;explanation&quot;: &quot;Danh t&#7915; s&#7889; nhi&#7873;u; thi&#7871;u d&#7845;u ph&#7849;y; m&#7841;o t&#7915;.&quot;, &quot;original_text&quot;: &quot;Student have many important subject, example maths, literature, or chemistry.&quot;, &quot;suggested_correction&quot;: &quot;Students have many important subjects, for example, maths, literature or chemistry.&quot;}, {&quot;severity&quot;: &quot;high&quot;, &quot;error_type&quot;: &quot;grammar&quot;, &quot;explanation&quot;: &quot;Thi&#7871;u &#273;&#7897;ng t&#7915; &#8216;is&#8217;; c&#7847;n m&#7841;o t&#7915; &#8216;a&#8217;.&quot;, &quot;original_text&quot;: &quot;International news not really important, only waste of time.&quot;, &quot;suggested_correction&quot;: &quot;International news is not really important; it is only a waste of time.&quot;}, {&quot;severity&quot;: &quot;high&quot;, &quot;error_type&quot;: &quot;grammar|spelling&quot;, &quot;explanation&quot;: &quot;C&#7847;n &#8216;In conclusion&#8217;; &#8216;news&#8217; coi nh&#432; s&#7889; &#237;t; th&#234;m &#8216;some&#8217;; danh t&#7915; s&#7889; nhi&#7873;u &#8216;disadvantages&#8217;.&quot;, &quot;original_text&quot;: &quot;I conclusion, international news have advantage but more disadvantage.&quot;, &quot;suggested_correction&quot;: &quot;In conclusion, international news has some advantages but more disadvantages.&quot;}]</span>\"\n            \"<span class=sf-dump-key>criteria_analysis</span>\" => \"<span class=sf-dump-str title=\"1493 characters\">[{&quot;criterion&quot;: &quot;Task Achievement&quot;, &quot;strengths&quot;: [&quot;&#272;&#227; n&#234;u &#273;&#432;&#7907;c hai quan &#273;i&#7875;m&quot;, &quot;C&#243; l&#7853;p tr&#432;&#7901;ng c&#225; nh&#226;n trong &#273;o&#7841;n k&#7871;t&quot;], &quot;weaknesses&quot;: [&quot;D&#432;&#7899;i 250 t&#7915; n&#234;n b&#7883; tr&#7915; &#273;i&#7875;m&quot;, &quot;L&#7853;p lu&#7853;n h&#7901;i h&#7907;t, v&#237; d&#7909; chung chung&quot;, &quot;Kh&#244;ng gi&#7843;i th&#237;ch v&#236; sao tin qu&#7889;c t&#7871; gi&#250;p n&#243;i ti&#7871;ng Anh t&#7889;t h&#417;n&quot;, &quot;Kh&#244;ng ph&#7843;n bi&#7879;n l&#7841;i quan &#273;i&#7875;m &#273;&#7889;i l&#7853;p&quot;], &quot;band_justification&quot;: &quot;&#272;&#225;p &#7913;ng ph&#7847;n n&#224;o y&#234;u c&#7847;u &#273;&#7873; nh&#432;ng ph&#225;t tri&#7875;n ch&#432;a &#273;&#7911;, thi&#7871;u chi&#7873;u s&#226;u v&#224; thi&#7871;u t&#7915; &#8594; band 4.0.&quot;}, {&quot;criterion&quot;: &quot;Coherence &amp; Cohesion&quot;, &quot;strengths&quot;: [&quot;C&#243; b&#7889; c&#7909;c c&#417; b&#7843;n: m&#7903;&#8211;th&#226;n&#8211;k&#7871;t&quot;, &quot;M&#7897;t s&#7889; t&#7915; n&#7889;i &#273;&#432;&#7907;c d&#249;ng (Firstly, However)&quot;], &quot;weaknesses&quot;: [&quot;Li&#234;n k&#7871;t &#253; k&#233;m, chuy&#7875;n &#273;o&#7841;n &#273;&#7897;t ng&#7897;t&quot;, &quot;C&#226;u ch&#7911; &#273;&#7873; m&#7901; nh&#7841;t&quot;, &quot;D&#249;ng l&#7863;p t&#7915; n&#7889;i, ch&#432;a &#273;a d&#7841;ng&quot;], &quot;band_justification&quot;: &quot;Li&#234;n k&#7871;t &#7903; m&#7913;c h&#7841;n ch&#7871;, &#253; &#273;&#244;i khi r&#7901;i r&#7841;c nh&#432;ng v&#7851;n theo tr&#236;nh t&#7921; chung &#8594; 4.5.&quot;}, {&quot;criterion&quot;: &quot;Lexical Resource&quot;, &quot;strengths&quot;: [&quot;T&#7915; v&#7921;ng quen thu&#7897;c, d&#7877; hi&#7875;u&quot;, &quot;M&#7897;t v&#224;i t&#7915; h&#7885;c thu&#7853;t &#273;&#417;n gi&#7843;n (advantage, disadvantage)&quot;], &quot;weaknesses&quot;: [&quot;L&#7863;p t&#7915; th&#432;&#7901;ng xuy&#234;n&quot;, &quot;Sai ch&#237;nh t&#7843; nhi&#7873;u&quot;, &quot;D&#249;ng t&#7915; ch&#432;a ch&#237;nh x&#225;c ho&#7863;c kh&#244;ng ph&#249; h&#7907;p v&#259;n phong h&#7885;c thu&#7853;t&quot;], &quot;band_justification&quot;: &quot;V&#7889;n t&#7915; h&#7841;n ch&#7871;, l&#7895;i ch&#237;nh t&#7843; &amp; ch&#7885;n t&#7915; &#7843;nh h&#432;&#7903;ng ng&#432;&#7901;i &#273;&#7885;c &#8594; 4.0.&quot;}, {&quot;criterion&quot;: &quot;Grammatical Range &amp; Accuracy&quot;, &quot;strengths&quot;: [&quot;Bi&#7871;t k&#7871;t th&#250;c c&#226;u b&#7857;ng d&#7845;u ch&#7845;m&quot;, &quot;M&#7897;t s&#7889; m&#7879;nh &#273;&#7873; &#273;&#417;n ch&#237;nh x&#225;c&quot;], &quot;weaknesses&quot;: [&quot;L&#7895;i S-V agreement li&#234;n t&#7909;c&quot;, &quot;Thi&#7871;u m&#7841;o t&#7915;, gi&#7899;i t&#7915;&quot;, &quot;C&#7845;u tr&#250;c c&#226;u &#273;&#417;n gi&#7843;n, hi&#7871;m c&#226;u ph&#7913;c&quot;, &quot;Sai th&#236; v&#224; thi&#7871;u &#273;&#7897;ng t&#7915; to be&quot;], &quot;band_justification&quot;: &quot;L&#7895;i th&#432;&#7901;ng xuy&#234;n khi&#7871;n kh&#243; hi&#7875;u, c&#7845;u tr&#250;c &#273;&#417;n gi&#7843;n &#8594; 3.5.&quot;}]</span>\"\n            \"<span class=sf-dump-key>statistics</span>\" => \"<span class=sf-dump-str title=\"145 characters\">{&quot;total_errors&quot;: 45, &quot;grammar_errors&quot;: 28, &quot;spelling_errors&quot;: 10, &quot;complexity_level&quot;: &quot;basic&quot;, &quot;sentence_variety&quot;: &quot;low&quot;, &quot;vocabulary_errors&quot;: 7}</span>\"\n            \"<span class=sf-dump-key>word_count</span>\" => <span class=sf-dump-num>179</span>\n            \"<span class=sf-dump-key>essay_length</span>\" => <span class=sf-dump-num>1184</span>\n            \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"9 characters\">completed</span>\"\n            \"<span class=sf-dump-key>error_message</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-18 17:02:55</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-18 17:03:22</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">previous</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:9</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>detailed_feedback</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n            \"<span class=sf-dump-key>highlighted_corrections</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n            \"<span class=sf-dump-key>criteria_analysis</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n            \"<span class=sf-dump-key>statistics</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n            \"<span class=sf-dump-key>overall_band_score</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:1</span>\"\n            \"<span class=sf-dump-key>task_achievement</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:1</span>\"\n            \"<span class=sf-dump-key>coherence_cohesion</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:1</span>\"\n            \"<span class=sf-dump-key>lexical_resource</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:1</span>\"\n            \"<span class=sf-dump-key>grammar_accuracy</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:1</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>user</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\User\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">User</span></span> {<a class=sf-dump-ref>#1470</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"5 characters\">users</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n              +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n              +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n              +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>3</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">anh vo</span>\"\n                \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"\n                \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"10 characters\">0327239691</span>\"\n                \"<span class=sf-dump-key>email_verified_at</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$kHdkDSFSoZnvKLzjz2BNNO.eTtDMX8mMlt3RY4/iSp3q.LGENyf.i</span>\"\n                \"<span class=sf-dump-key>role</span>\" => \"<span class=sf-dump-str title=\"4 characters\">user</span>\"\n                \"<span class=sf-dump-key>is_active</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>remember_token</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-18 16:16:51</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-18 16:16:51</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>3</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">anh vo</span>\"\n                \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"\n                \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"10 characters\">0327239691</span>\"\n                \"<span class=sf-dump-key>email_verified_at</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$kHdkDSFSoZnvKLzjz2BNNO.eTtDMX8mMlt3RY4/iSp3q.LGENyf.i</span>\"\n                \"<span class=sf-dump-key>role</span>\" => \"<span class=sf-dump-str title=\"4 characters\">user</span>\"\n                \"<span class=sf-dump-key>is_active</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>remember_token</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-18 16:16:51</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-18 16:16:51</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">previous</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>email_verified_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n                \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"6 characters\">hashed</span>\"\n                \"<span class=sf-dump-key>is_active</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadCallback</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadContext</span>: <span class=sf-dump-const>null</span>\n              +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">password</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"14 characters\">remember_token</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:6</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"5 characters\">email</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"5 characters\">phone</span>\"\n                <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"8 characters\">password</span>\"\n                <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"4 characters\">role</span>\"\n                <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"9 characters\">is_active</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">authPasswordName</span>: \"<span class=sf-dump-str title=\"8 characters\">password</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">rememberTokenName</span>: \"<span class=sf-dump-str title=\"14 characters\">remember_token</span>\"\n            </samp>}\n            \"<span class=sf-dump-key>essayQuestion</span>\" => <span class=sf-dump-const>null</span>\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadCallback</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadContext</span>: <span class=sf-dump-const>null</span>\n          +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n          +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:18</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">user_id</span>\"\n            <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"17 characters\">essay_question_id</span>\"\n            <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"13 characters\">essay_content</span>\"\n            <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"9 characters\">task_type</span>\"\n            <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"10 characters\">time_limit</span>\"\n            <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"18 characters\">overall_band_score</span>\"\n            <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"16 characters\">task_achievement</span>\"\n            <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"18 characters\">coherence_cohesion</span>\"\n            <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"16 characters\">lexical_resource</span>\"\n            <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"16 characters\">grammar_accuracy</span>\"\n            <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"17 characters\">detailed_feedback</span>\"\n            <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"23 characters\">highlighted_corrections</span>\"\n            <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"17 characters\">criteria_analysis</span>\"\n            <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"10 characters\">statistics</span>\"\n            <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"10 characters\">word_count</span>\"\n            <span class=sf-dump-index>15</span> => \"<span class=sf-dump-str title=\"12 characters\">essay_length</span>\"\n            <span class=sf-dump-index>16</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n            <span class=sf-dump-index>17</span> => \"<span class=sf-dump-str title=\"13 characters\">error_message</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n          </samp>]\n        </samp>}\n        \"<span class=sf-dump-key>token</span>\" => \"<span class=sf-dump-str title=\"456 characters\">ZXlKcGRpSTZJbmM0UzIxQk5rdDZkVmxKV20wclVGUjZNRkY0Wm5jOVBTSXNJblpoYkhWbElqb2lOSEI1ZWtkV1owbFFUVkpGTld4NFRGSjRVRW80Y1VWMWN6UldaMFJzUjBzMGF6TTViVmt3V2pCNWVEbDRWWGRKTTA5ck9GRnVlbFpzZVdwcU4yRmhRbWR5WmxGWmRFMTJNMDFDYUhKa2RsTnpLMVF3YkdGYVNEZzFSMUZQTDFFdmNsVkplaTk1ZDBzMFVGUlVkbTlOWmtRME9EUXJlRlJ1SzFoaVlsSTBUbVVpTENKdFlXTWlPaUpsWlRCbE5qRXdZMk00WkRKbE0yRmtNamMyTW1JeE5XSTROekJsTW1VNE16ZGpNVEUxTVRrM09UZzBaRFE1WVRkbU1tVm1ZV1EzTjJRM05URmhNV1EwSWl3aWRHRm5Jam9pSW4wPQ==</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"71 characters\">vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>76</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"12 characters\">evaluatePath</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\View\\Engines\\PhpEngine</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"102 characters\">C:\\laragon\\www\\ielts\\ielts-scoring-system\\storage\\framework\\views/d6ccf6006ae9cc9eb654e17fa393b764.php</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>__env</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\View\\Factory\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\View</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Factory</span></span> {<a class=sf-dump-ref href=#sf-dump-548656238-ref21306 title=\"3 occurrences\">#1306</a> &#8230;25}\n        \"<span class=sf-dump-key>app</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-548656238-ref24 title=\"3 occurrences\">#4</a> &#8230;44}\n        \"<span class=sf-dump-key>errors</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\ViewErrorBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ViewErrorBag</span></span> {<a class=sf-dump-ref href=#sf-dump-548656238-ref21422 title=\"3 occurrences\">#1422</a>}\n        \"<span class=sf-dump-key>attempt</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\ScoringAttempt\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ScoringAttempt</span></span> {<a class=sf-dump-ref href=#sf-dump-548656238-ref21405 title=\"3 occurrences\">#1405</a>}\n        \"<span class=sf-dump-key>token</span>\" => \"<span class=sf-dump-str title=\"456 characters\">ZXlKcGRpSTZJbmM0UzIxQk5rdDZkVmxKV20wclVGUjZNRkY0Wm5jOVBTSXNJblpoYkhWbElqb2lOSEI1ZWtkV1owbFFUVkpGTld4NFRGSjRVRW80Y1VWMWN6UldaMFJzUjBzMGF6TTViVmt3V2pCNWVEbDRWWGRKTTA5ck9GRnVlbFpzZVdwcU4yRmhRbWR5WmxGWmRFMTJNMDFDYUhKa2RsTnpLMVF3YkdGYVNEZzFSMUZQTDFFdmNsVkplaTk1ZDBzMFVGUlVkbTlOWmtRME9EUXJlRlJ1SzFoaVlsSTBUbVVpTENKdFlXTWlPaUpsWlRCbE5qRXdZMk00WkRKbE0yRmtNamMyTW1JeE5XSTROekJsTW1VNE16ZGpNVEUxTVRrM09UZzBaRFE1WVRkbU1tVm1ZV1EzTjJRM05URmhNV1EwSWl3aWRHRm5Jam9pSW4wPQ==</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"53 characters\">vendor/laravel/framework/src/Illuminate/View/View.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"3 characters\">get</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"38 characters\">Illuminate\\View\\Engines\\CompilerEngine</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"82 characters\">C:\\laragon\\www\\ielts\\ielts-scoring-system\\resources\\views/scoring/public.blade.php</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>__env</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\View\\Factory\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\View</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Factory</span></span> {<a class=sf-dump-ref href=#sf-dump-548656238-ref21306 title=\"3 occurrences\">#1306</a> &#8230;25}\n        \"<span class=sf-dump-key>app</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-548656238-ref24 title=\"3 occurrences\">#4</a> &#8230;44}\n        \"<span class=sf-dump-key>errors</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\ViewErrorBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ViewErrorBag</span></span> {<a class=sf-dump-ref href=#sf-dump-548656238-ref21422 title=\"3 occurrences\">#1422</a>}\n        \"<span class=sf-dump-key>attempt</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\ScoringAttempt\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ScoringAttempt</span></span> {<a class=sf-dump-ref href=#sf-dump-548656238-ref21405 title=\"3 occurrences\">#1405</a>}\n        \"<span class=sf-dump-key>token</span>\" => \"<span class=sf-dump-str title=\"456 characters\">ZXlKcGRpSTZJbmM0UzIxQk5rdDZkVmxKV20wclVGUjZNRkY0Wm5jOVBTSXNJblpoYkhWbElqb2lOSEI1ZWtkV1owbFFUVkpGTld4NFRGSjRVRW80Y1VWMWN6UldaMFJzUjBzMGF6TTViVmt3V2pCNWVEbDRWWGRKTTA5ck9GRnVlbFpzZVdwcU4yRmhRbWR5WmxGWmRFMTJNMDFDYUhKa2RsTnpLMVF3YkdGYVNEZzFSMUZQTDFFdmNsVkplaTk1ZDBzMFVGUlVkbTlOWmtRME9EUXJlRlJ1SzFoaVlsSTBUbVVpTENKdFlXTWlPaUpsWlRCbE5qRXdZMk00WkRKbE0yRmtNamMyTW1JeE5XSTROekJsTW1VNE16ZGpNVEUxTVRrM09UZzBaRFE1WVRkbU1tVm1ZV1EzTjJRM05URmhNV1EwSWl3aWRHRm5Jam9pSW4wPQ==</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"53 characters\">vendor/laravel/framework/src/Illuminate/View/View.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>191</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"11 characters\">getContents</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Illuminate\\View\\View</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"53 characters\">vendor/laravel/framework/src/Illuminate/View/View.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>160</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"14 characters\">renderContents</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Illuminate\\View\\View</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"57 characters\">vendor/laravel/framework/src/Illuminate/Http/Response.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>78</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">render</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Illuminate\\View\\View</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"57 characters\">vendor/laravel/framework/src/Illuminate/Http/Response.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>34</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"10 characters\">setContent</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Illuminate\\Http\\Response</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">[object Illuminate\\View\\View]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>11</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>924</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"11 characters\">__construct</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Illuminate\\Http\\Response</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">[object Illuminate\\View\\View]</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-num>200</span>\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>Content-Type</span>\" => \"<span class=sf-dump-str title=\"9 characters\">text/html</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>12</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>891</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"10 characters\">toResponse</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">::</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"29 characters\">[object Illuminate\\View\\View]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>13</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>807</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"15 characters\">prepareResponse</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"29 characters\">[object Illuminate\\View\\View]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>14</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>169</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Routing\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>15</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>66</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>16</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Barryvdh\\Debugbar\\Middleware\\InjectDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>17</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"43 characters\">app/Http/Middleware/EnsureUserHasCredit.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>32</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>18</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">App\\Http\\Middleware\\EnsureUserHasCredit</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>19</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"81 characters\">vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>50</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>20</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"48 characters\">Illuminate\\Routing\\Middleware\\SubstituteBindings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>21</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>63</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>22</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Auth\\Middleware\\Authenticate</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>23</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"86 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>87</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>24</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"53 characters\">Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>25</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>48</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>26</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\View\\Middleware\\ShareErrorsFromSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>27</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"75 characters\">vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>120</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>28</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"75 characters\">vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>63</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"21 characters\">handleStatefulRequest</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Illuminate\\Session\\Middleware\\StartSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Session\\Store]</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>29</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Illuminate\\Session\\Middleware\\StartSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>30</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>36</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>31</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>32</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"76 characters\">vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>74</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>33</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Illuminate\\Cookie\\Middleware\\EncryptCookies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>34</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>126</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>35</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>807</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>36</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>786</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"19 characters\">runRouteWithinStack</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>37</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>750</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">runRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>38</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>739</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"15 characters\">dispatchToRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>39</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>200</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">dispatch</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>40</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>169</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Illuminate\\Foundation\\Http\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>41</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>66</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>42</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Barryvdh\\Debugbar\\Middleware\\InjectDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>43</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>44</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"96 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>31</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>45</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"63 characters\">Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>46</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>47</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>51</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>48</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\Foundation\\Http\\Middleware\\TrimStrings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>49</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"76 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>27</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>50</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Illuminate\\Http\\Middleware\\ValidatePostSize</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>51</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"103 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>109</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>52</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"70 characters\">Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>53</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"70 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>48</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>54</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"37 characters\">Illuminate\\Http\\Middleware\\HandleCors</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>55</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>58</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>56</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Http\\Middleware\\TrustProxies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>57</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"94 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>22</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>58</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"61 characters\">Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>59</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"80 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePathEncoding.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>26</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>60</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"47 characters\">Illuminate\\Http\\Middleware\\ValidatePathEncoding</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>61</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>126</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>62</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>175</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>63</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>144</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"24 characters\">sendRequestThroughRouter</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>64</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Application.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>1219</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>65</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"16 characters\">public/index.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>20</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"13 characters\">handleRequest</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Application</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>66</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"71 characters\">vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>23</span>\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"58 characters\">C:\\laragon\\www\\ielts\\ielts-scoring-system\\public\\index.php</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"12 characters\">require_once</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-548656238\", {\"maxDepth\":0})</script>\n", "surrounding_lines": ["            $value = $value->value;\n", "        }\n", "\n", "        return htmlspecialchars($value ?? '', ENT_QUOTES | ENT_SUBSTITUTE, 'UTF-8', $doubleEncode);\n", "    }\n", "}\n", "\n"], "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSupport%2Fhelpers.php&line=141", "ajax": false, "filename": "helpers.php", "line": "141"}}]}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.20.0", "PHP Version": "8.2.27", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 23, "nb_templates": 23, "templates": [{"name": "scoring.public", "param_count": null, "params": [], "start": **********.967362, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\resources\\views/scoring/public.blade.phpscoring.public", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fresources%2Fviews%2Fscoring%2Fpublic.blade.php&line=1", "ajax": false, "filename": "public.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::show", "param_count": null, "params": [], "start": **********.187619, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/show.blade.phplaravel-exceptions-renderer::show", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fshow.blade.php&line=1", "ajax": false, "filename": "show.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.navigation", "param_count": null, "params": [], "start": **********.190988, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/navigation.blade.phplaravel-exceptions-renderer::components.navigation", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Fnavigation.blade.php&line=1", "ajax": false, "filename": "navigation.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.theme-switcher", "param_count": null, "params": [], "start": **********.192391, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/theme-switcher.blade.phplaravel-exceptions-renderer::components.theme-switcher", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ftheme-switcher.blade.php&line=1", "ajax": false, "filename": "theme-switcher.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.icons.sun", "param_count": null, "params": [], "start": **********.194441, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/icons/sun.blade.phplaravel-exceptions-renderer::components.icons.sun", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ficons%2Fsun.blade.php&line=1", "ajax": false, "filename": "sun.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.icons.moon", "param_count": null, "params": [], "start": **********.195243, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/icons/moon.blade.phplaravel-exceptions-renderer::components.icons.moon", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ficons%2Fmoon.blade.php&line=1", "ajax": false, "filename": "moon.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.icons.sun", "param_count": null, "params": [], "start": **********.195993, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/icons/sun.blade.phplaravel-exceptions-renderer::components.icons.sun", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ficons%2Fsun.blade.php&line=1", "ajax": false, "filename": "sun.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.icons.moon", "param_count": null, "params": [], "start": **********.196216, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/icons/moon.blade.phplaravel-exceptions-renderer::components.icons.moon", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ficons%2Fmoon.blade.php&line=1", "ajax": false, "filename": "moon.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.icons.computer-desktop", "param_count": null, "params": [], "start": **********.196448, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/icons/computer-desktop.blade.phplaravel-exceptions-renderer::components.icons.computer-desktop", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ficons%2Fcomputer-desktop.blade.php&line=1", "ajax": false, "filename": "computer-desktop.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.header", "param_count": null, "params": [], "start": **********.197971, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/header.blade.phplaravel-exceptions-renderer::components.header", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.card", "param_count": null, "params": [], "start": **********.1997, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/card.blade.phplaravel-exceptions-renderer::components.card", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Fcard.blade.php&line=1", "ajax": false, "filename": "card.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.trace-and-editor", "param_count": null, "params": [], "start": **********.200528, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/trace-and-editor.blade.phplaravel-exceptions-renderer::components.trace-and-editor", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ftrace-and-editor.blade.php&line=1", "ajax": false, "filename": "trace-and-editor.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.trace", "param_count": null, "params": [], "start": **********.212116, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/trace.blade.phplaravel-exceptions-renderer::components.trace", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ftrace.blade.php&line=1", "ajax": false, "filename": "trace.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.icons.chevron-down", "param_count": null, "params": [], "start": **********.2157, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/icons/chevron-down.blade.phplaravel-exceptions-renderer::components.icons.chevron-down", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ficons%2Fchevron-down.blade.php&line=1", "ajax": false, "filename": "chevron-down.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.icons.chevron-up", "param_count": null, "params": [], "start": **********.21657, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/icons/chevron-up.blade.phplaravel-exceptions-renderer::components.icons.chevron-up", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ficons%2Fchevron-up.blade.php&line=1", "ajax": false, "filename": "chevron-up.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.icons.chevron-up", "param_count": null, "params": [], "start": **********.217313, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/icons/chevron-up.blade.phplaravel-exceptions-renderer::components.icons.chevron-up", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ficons%2Fchevron-up.blade.php&line=1", "ajax": false, "filename": "chevron-up.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.icons.chevron-down", "param_count": null, "params": [], "start": **********.217492, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/icons/chevron-down.blade.phplaravel-exceptions-renderer::components.icons.chevron-down", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ficons%2Fchevron-down.blade.php&line=1", "ajax": false, "filename": "chevron-down.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.editor", "param_count": null, "params": [], "start": **********.239901, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/editor.blade.phplaravel-exceptions-renderer::components.editor", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Feditor.blade.php&line=1", "ajax": false, "filename": "editor.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.card", "param_count": null, "params": [], "start": **********.286232, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/card.blade.phplaravel-exceptions-renderer::components.card", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Fcard.blade.php&line=1", "ajax": false, "filename": "card.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.context", "param_count": null, "params": [], "start": **********.286629, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/context.blade.phplaravel-exceptions-renderer::components.context", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Fcontext.blade.php&line=1", "ajax": false, "filename": "context.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.card", "param_count": null, "params": [], "start": **********.288784, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/card.blade.phplaravel-exceptions-renderer::components.card", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Fcard.blade.php&line=1", "ajax": false, "filename": "card.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.card", "param_count": null, "params": [], "start": **********.289176, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/card.blade.phplaravel-exceptions-renderer::components.card", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Fcard.blade.php&line=1", "ajax": false, "filename": "card.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.layout", "param_count": null, "params": [], "start": **********.289453, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/layout.blade.phplaravel-exceptions-renderer::components.layout", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Flayout.blade.php&line=1", "ajax": false, "filename": "layout.blade.php", "line": "?"}}]}, "queries": {"count": 6, "nb_statements": 5, "nb_visible_statements": 6, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00469, "accumulated_duration_str": "4.69ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 158}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 56}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}], "start": **********.932403, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "ietls", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `sessions` where `id` = 'pCASZAoZFeOJeGpNZ7yRLWlVdF889fM8IsLGw0i1' limit 1", "type": "query", "params": [], "bindings": ["pCASZAoZFeOJeGpNZ7yRLWlVdF889fM8IsLGw0i1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.936913, "duration": 0.0020499999999999997, "duration_str": "2.05ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "ietls", "explain": null, "start_percent": 0, "width_percent": 43.71}, {"sql": "select * from `users` where `id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 82}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.94704, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "ietls", "explain": null, "start_percent": 43.71, "width_percent": 21.962}, {"sql": "select * from `user_credits` where `user_credits`.`user_id` = 3 and `user_credits`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "middleware", "name": "ensure.credit", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\app\\Http\\Middleware\\EnsureUserHasCredit.php", "line": 22}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}], "start": **********.953248, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "ensure.credit:22", "source": {"index": 21, "namespace": "middleware", "name": "ensure.credit", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\app\\Http\\Middleware\\EnsureUserHasCredit.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fapp%2FHttp%2FMiddleware%2FEnsureUserHasCredit.php&line=22", "ajax": false, "filename": "EnsureUserHasCredit.php", "line": "22"}, "connection": "ietls", "explain": null, "start_percent": 65.672, "width_percent": 10.874}, {"sql": "select * from `scoring_attempts` where `scoring_attempts`.`id` = 10 limit 1", "type": "query", "params": [], "bindings": [10], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Http/Controllers/ScoringController.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\app\\Http\\Controllers\\ScoringController.php", "line": 210}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.955633, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "ScoringController.php:210", "source": {"index": 17, "namespace": null, "name": "app/Http/Controllers/ScoringController.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\app\\Http\\Controllers\\ScoringController.php", "line": 210}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fapp%2FHttp%2FControllers%2FScoringController.php&line=210", "ajax": false, "filename": "ScoringController.php", "line": "210"}, "connection": "ietls", "explain": null, "start_percent": 76.546, "width_percent": 13.006}, {"sql": "select * from `users` where `users`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/ScoringController.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\app\\Http\\Controllers\\ScoringController.php", "line": 210}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.9580112, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "ScoringController.php:210", "source": {"index": 22, "namespace": null, "name": "app/Http/Controllers/ScoringController.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\app\\Http\\Controllers\\ScoringController.php", "line": 210}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fapp%2FHttp%2FControllers%2FScoringController.php&line=210", "ajax": false, "filename": "ScoringController.php", "line": "210"}, "connection": "ietls", "explain": null, "start_percent": 89.552, "width_percent": 10.448}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\UserCredit": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fapp%2FModels%2FUserCredit.php&line=1", "ajax": false, "filename": "UserCredit.php", "line": "?"}}, "App\\Models\\ScoringAttempt": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fapp%2FModels%2FScoringAttempt.php&line=1", "ajax": false, "filename": "ScoringAttempt.php", "line": "?"}}}, "count": 4, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "500 Internal Server Error", "full_url": "http://127.0.0.1:8000/share/ZXlKcGRpSTZJbmM0UzIxQk5rdDZkVmxKV20wclVGUjZNRkY0Wm5jOVBTSXNJblpoYkhWbElq...", "action_name": "scoring.public", "controller_action": "App\\Http\\Controllers\\ScoringController@publicShow", "uri": "GET share/{token}", "controller": "App\\Http\\Controllers\\ScoringController@publicShow<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fapp%2FHttp%2FControllers%2FScoringController.php&line=200\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fapp%2FHttp%2FControllers%2FScoringController.php&line=200\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/ScoringController.php:200-223</a>", "middleware": "web", "duration": "544ms", "peak_memory": "30MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-143244075 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-143244075\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1408662108 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1408662108\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-518774167 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"484 characters\">http://127.0.0.1:8000/share/ZXlKcGRpSTZJbmM0UzIxQk5rdDZkVmxKV20wclVGUjZNRkY0Wm5jOVBTSXNJblpoYkhWbElqb2lOSEI1ZWtkV1owbFFUVkpGTld4NFRGSjRVRW80Y1VWMWN6UldaMFJzUjBzMGF6TTViVmt3V2pCNWVEbDRWWGRKTTA5ck9GRnVlbFpzZVdwcU4yRmhRbWR5WmxGWmRFMTJNMDFDYUhKa2RsTnpLMVF3YkdGYVNEZzFSMUZQTDFFdmNsVkplaTk1ZDBzMFVGUlVkbTlOWmtRME9EUXJlRlJ1SzFoaVlsSTBUbVVpTENKdFlXTWlPaUpsWlRCbE5qRXdZMk00WkRKbE0yRmtNamMyTW1JeE5XSTROekJsTW1VNE16ZGpNVEUxTVRrM09UZzBaRFE1WVRkbU1tVm1ZV1EzTjJRM05URmhNV1EwSWl3aWRHRm5Jam9pSW4wPQ==</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"734 characters\">XSRF-TOKEN=eyJpdiI6IlIvSm9NQmI2bkJiZW9QVHNHTjh4ZWc9PSIsInZhbHVlIjoiNXlmdVhOdmZxZzBLMVAzcWEvSjJMOEhJUUdxZ25iMGllaGZFY2xkbXpoRTB2Sm9qY0RzV0NFc2EvVzMxb0dNcWh3N2pxYTRKZzNCdjRrR3plZGlQK1Y0QXVUZzdmNFdJUmZoR2l5QmY4dWJRa3pEcExUTkk5S2dpcEdaSEExWEUiLCJtYWMiOiJlYTM5ZTE1ZGI2NjQxNGJlMTY3YjQyZDYxYzM3MzVmNzI5NmJlNDE3MWZiNDIzMTQwNTAwYjk0ZWFmOWU1ZjgxIiwidGFnIjoiIn0%3D; i_e_l_t_s_a_i_scoring_system_session=eyJpdiI6InVZcTcyenVBR1YyMENKaXB5cUhvaVE9PSIsInZhbHVlIjoiWTdXeDFPTjdpSm4vc1U4cHY5NS9EcXdBdTMzdzg0UnNEakNRbnJ3cGRsYll5MVFSaS9rQkdLVU1tWjUxWE5sSzlkTkl4RXU4V2FySTIrclNDZmhqOUdxZnRKVzNoRDZ5RE9tSzdDaUpHRTNxRWtnRHdDb2krcTRxZnR4MzI4VXkiLCJtYWMiOiIzYjVhNTJkMGRhOWU2ZGU1NmFjNjJjNzY2MzIwYjEwMzk1OGU4OWRiMjMwMWI3MjM0NWExNzcyOTY0MjYzOWI0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-518774167\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1300333177 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cUmWl6GniRBNGmoQJUVf8GM28NkKFkyfd5CEwpDM</span>\"\n  \"<span class=sf-dump-key>i_e_l_t_s_a_i_scoring_system_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pCASZAoZFeOJeGpNZ7yRLWlVdF889fM8IsLGw0i1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1300333177\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-205348940 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 18 Jul 2025 17:10:49 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-205348940\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1341878140 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cUmWl6GniRBNGmoQJUVf8GM28NkKFkyfd5CEwpDM</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/scoring/show/9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"484 characters\">http://127.0.0.1:8000/share/ZXlKcGRpSTZJbmM0UzIxQk5rdDZkVmxKV20wclVGUjZNRkY0Wm5jOVBTSXNJblpoYkhWbElqb2lOSEI1ZWtkV1owbFFUVkpGTld4NFRGSjRVRW80Y1VWMWN6UldaMFJzUjBzMGF6TTViVmt3V2pCNWVEbDRWWGRKTTA5ck9GRnVlbFpzZVdwcU4yRmhRbWR5WmxGWmRFMTJNMDFDYUhKa2RsTnpLMVF3YkdGYVNEZzFSMUZQTDFFdmNsVkplaTk1ZDBzMFVGUlVkbTlOWmtRME9EUXJlRlJ1SzFoaVlsSTBUbVVpTENKdFlXTWlPaUpsWlRCbE5qRXdZMk00WkRKbE0yRmtNamMyTW1JeE5XSTROekJsTW1VNE16ZGpNVEUxTVRrM09UZzBaRFE1WVRkbU1tVm1ZV1EzTjJRM05URmhNV1EwSWl3aWRHRm5Jam9pSW4wPQ==</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>3</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1341878140\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "500 Internal Server Error", "full_url": "http://127.0.0.1:8000/share/ZXlKcGRpSTZJbmM0UzIxQk5rdDZkVmxKV20wclVGUjZNRkY0Wm5jOVBTSXNJblpoYkhWbElq...", "action_name": "scoring.public", "controller_action": "App\\Http\\Controllers\\ScoringController@publicShow"}, "badge": "500 Internal Server Error"}}