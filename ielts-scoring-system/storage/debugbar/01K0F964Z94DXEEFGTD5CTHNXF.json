{"__meta": {"id": "01K0F964Z94DXEEFGTD5CTHNXF", "datetime": "2025-07-19 00:26:52", "utime": **********.138103, "method": "GET", "uri": "/scoring/history", "ip": "127.0.0.1"}, "messages": {"count": 2, "messages": [{"message": "[00:26:51] LOG.info: IELTSScorer initialized {\n    \"api_url\": \"https:\\/\\/api.v3.cm\\/v1\\/chat\\/completions\",\n    \"model\": \"o3\",\n    \"api_key_length\": 51\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.881419, "xdebug_link": null, "collector": "log"}, {"message": "[00:26:51] LOG.error: View [scoring.history] not found. {\n    \"userId\": 1,\n    \"exception\": {}\n}", "message_html": null, "is_string": false, "label": "error", "time": **********.939942, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.742737, "end": **********.138118, "duration": 0.39538097381591797, "duration_str": "395ms", "measures": [{"label": "Booting", "start": **********.742737, "relative_start": 0, "end": **********.861881, "relative_end": **********.861881, "duration": 0.*****************, "duration_str": "119ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.861895, "relative_start": 0.*****************, "end": **********.13812, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "276ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.875877, "relative_start": 0.*****************, "end": **********.878044, "relative_end": **********.878044, "duration": 0.0021669864654541016, "duration_str": "2.17ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: laravel-exceptions-renderer::show", "start": **********.052578, "relative_start": 0.****************, "end": **********.052578, "relative_end": **********.052578, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.navigation", "start": **********.05412, "relative_start": 0.****************, "end": **********.05412, "relative_end": **********.05412, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.theme-switcher", "start": **********.05448, "relative_start": 0.31174302101135254, "end": **********.05448, "relative_end": **********.05448, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.icons.sun", "start": **********.054902, "relative_start": 0.3121650218963623, "end": **********.054902, "relative_end": **********.054902, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.icons.moon", "start": **********.055199, "relative_start": 0.31246185302734375, "end": **********.055199, "relative_end": **********.055199, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.icons.sun", "start": **********.055451, "relative_start": 0.3127138614654541, "end": **********.055451, "relative_end": **********.055451, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.icons.moon", "start": **********.055649, "relative_start": 0.3129119873046875, "end": **********.055649, "relative_end": **********.055649, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.icons.computer-desktop", "start": **********.055883, "relative_start": 0.31314587593078613, "end": **********.055883, "relative_end": **********.055883, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.header", "start": **********.056175, "relative_start": 0.31343793869018555, "end": **********.056175, "relative_end": **********.056175, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.card", "start": **********.056601, "relative_start": 0.31386399269104004, "end": **********.056601, "relative_end": **********.056601, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.trace-and-editor", "start": **********.056967, "relative_start": 0.31422996520996094, "end": **********.056967, "relative_end": **********.056967, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.trace", "start": **********.066931, "relative_start": 0.32419395446777344, "end": **********.066931, "relative_end": **********.066931, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.icons.chevron-down", "start": **********.067451, "relative_start": 0.32471394538879395, "end": **********.067451, "relative_end": **********.067451, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.icons.chevron-up", "start": **********.067752, "relative_start": 0.3250148296356201, "end": **********.067752, "relative_end": **********.067752, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.icons.chevron-up", "start": **********.067994, "relative_start": 0.3252570629119873, "end": **********.067994, "relative_end": **********.067994, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.icons.chevron-down", "start": **********.068147, "relative_start": 0.3254098892211914, "end": **********.068147, "relative_end": **********.068147, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.editor", "start": **********.086059, "relative_start": 0.3433220386505127, "end": **********.086059, "relative_end": **********.086059, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.card", "start": **********.124144, "relative_start": 0.3814070224761963, "end": **********.124144, "relative_end": **********.124144, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.context", "start": **********.124522, "relative_start": 0.38178491592407227, "end": **********.124522, "relative_end": **********.124522, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.card", "start": **********.125064, "relative_start": 0.3823268413543701, "end": **********.125064, "relative_end": **********.125064, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.card", "start": **********.125363, "relative_start": 0.3826260566711426, "end": **********.125363, "relative_end": **********.125363, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.layout", "start": **********.125588, "relative_start": 0.38285088539123535, "end": **********.125588, "relative_end": **********.125588, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 27941640, "peak_usage_str": "27MB"}, "exceptions": {"count": 1, "exceptions": [{"type": "InvalidArgumentException", "message": "View [scoring.history] not found.", "code": 0, "file": "vendor/laravel/framework/src/Illuminate/View/FileViewFinder.php", "line": 138, "stack_trace": null, "stack_trace_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:62</span> [<samp data-depth=1 class=sf-dump-expanded>\n  <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"63 characters\">vendor/laravel/framework/src/Illuminate/View/FileViewFinder.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>78</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"11 characters\">findInPaths</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Illuminate\\View\\FileViewFinder</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">scoring.history</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"57 characters\">C:\\laragon\\www\\ielts\\ielts-scoring-system\\resources\\views</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"56 characters\">vendor/laravel/framework/src/Illuminate/View/Factory.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>150</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">find</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Illuminate\\View\\FileViewFinder</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">scoring.history</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"62 characters\">vendor/laravel/framework/src/Illuminate/Foundation/helpers.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>1101</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">make</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"23 characters\">Illuminate\\View\\Factory</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">scoring.history</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>attempts</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Pagination\\LengthAwarePaginator\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Pagination</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">LengthAwarePaginator</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref21484 title=\"2 occurrences\">#1484</a><samp data-depth=5 id=sf-dump-*********-ref21484 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#1456</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\ScoringAttempt\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ScoringAttempt</span></span> {<a class=sf-dump-ref>#1478</a><samp data-depth=8 class=sf-dump-compact>\n                #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"16 characters\">scoring_attempts</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:21</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>9</span>\n                  \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-num>1</span>\n                  \"<span class=sf-dump-key>essay_question_id</span>\" => <span class=sf-dump-const>null</span>\n                  \"<span class=sf-dump-key>essay_content</span>\" => \"\"\"\n                    <span class=sf-dump-str title=\"1184 characters\">International news is good for student but sometime it is not nessessary. Many people think student in secondary school need to study about international news, other think this waste their time. My essay will write about both of these thing.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">Firstly, International news is good because it make student smart. When student learn international news they can know many things. Example, student can know weather in other country, famous people life, or footbal match. They will very interesting about these topic and can relax in the classroom. Learning international news is also help student speaking english well.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">However, I thinking international news sometime is boring. Student have many important subject, example maths, literature, or chemistry. These subjects can help student become doctor or engineer in future. International news not really important, only waste of time.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">In my opinion, secondary student should play sport and learn math better than learn international news. Teacher should teaching student good subject not international news.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">I conclusion, international news have advantage but more disadvantage. Student no need study this subject, waste of their time.</span>\n                    \"\"\"\n                  \"<span class=sf-dump-key>task_type</span>\" => \"<span class=sf-dump-str title=\"5 characters\">task2</span>\"\n                  \"<span class=sf-dump-key>time_limit</span>\" => <span class=sf-dump-num>40</span>\n                  \"<span class=sf-dump-key>overall_band_score</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.0</span>\"\n                  \"<span class=sf-dump-key>task_achievement</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.0</span>\"\n                  \"<span class=sf-dump-key>coherence_cohesion</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.0</span>\"\n                  \"<span class=sf-dump-key>lexical_resource</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.0</span>\"\n                  \"<span class=sf-dump-key>grammar_accuracy</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.0</span>\"\n                  \"<span class=sf-dump-key>detailed_feedback</span>\" => \"<span class=sf-dump-str title=\"2086 characters\">{&quot;grammar_accuracy&quot;: {&quot;score&quot;: 4, &quot;issues&quot;: [&quot;S-V: \\&quot;news is &#8230; it make student\\&quot;, \\&quot;Learning international news is also help&#8230;\\&quot;&quot;, &quot;Thi&#7871;u m&#7841;o t&#7915;: \\&quot;in other country\\&quot;, \\&quot;famous people life\\&quot;&quot;], &quot;feedback&quot;: &quot;L&#7895;i S-V agreement, thi&#7871;u m&#7841;o t&#7915;, gi&#7899;i t&#7915;, th&#236;, c&#7845;u tr&#250;c c&#226;u h&#7847;u h&#7871;t &#7903; m&#7913;c &#273;&#417;n gi&#7843;n.&quot;, &quot;improvements&quot;: [&quot;&#212;n quy t&#7855;c S-V agreement, &#273;&#7863;c bi&#7879;t v&#7899;i danh t&#7915; s&#7889; nhi&#7873;u/kh&#244;ng &#273;&#7871;m &#273;&#432;&#7907;c.&quot;, &quot;K&#7871;t h&#7907;p c&#226;u ph&#7913;c (because, although, which), v&#224; d&#249;ng &#273;&#250;ng th&#236; hi&#7879;n t&#7841;i &#273;&#417;n/hi&#7879;n t&#7841;i ti&#7871;p di&#7877;n.&quot;]}, &quot;lexical_resource&quot;: {&quot;score&quot;: 4, &quot;issues&quot;: [&quot;Ch&#237;nh t&#7843;: nessessary, footbal, interesting (d&#249;ng sai d&#7841;ng), English kh&#244;ng vi&#7871;t hoa&#8230;&quot;, &quot;Collocation sai: \\&quot;very interesting about\\&quot;, \\&quot;good subject\\&quot;, \\&quot;help student speaking\\&quot;&#8230;&quot;], &quot;feedback&quot;: &quot;T&#7915; v&#7921;ng ngh&#232;o, l&#7863;p t&#7915; &#8220;student&#8221;, &#8220;international news&#8221;, sai ch&#237;nh t&#7843; nhi&#7873;u, collocation ch&#432;a ch&#237;nh x&#225;c.&quot;, &quot;improvements&quot;: [&quot;Ghi nh&#7899; ch&#237;nh t&#7843; chu&#7849;n, tra t&#7915; &#273;i&#7875;n tr&#432;&#7899;c khi vi&#7871;t.&quot;, &quot;H&#7885;c collocation: be interested in, improve speaking skills, beneficial subject&#8230; v&#224; thay th&#7871; t&#7915; l&#7863;p b&#7857;ng synonyms.&quot;]}, &quot;task_achievement&quot;: {&quot;score&quot;: 4, &quot;issues&quot;: [&quot;Thi&#7871;u t&#7889;i thi&#7875;u 250 t&#7915;.&quot;, &quot;L&#7853;p lu&#7853;n n&#244;ng, v&#237; d&#7909; chung chung, ch&#432;a ch&#7913;ng minh quan &#273;i&#7875;m.&quot;], &quot;feedback&quot;: &quot;B&#224;i vi&#7871;t ch&#432;a &#273;&#225;p &#7913;ng &#273;&#7847;y &#273;&#7911; y&#234;u c&#7847;u Task 2. B&#224;i ch&#7881; c&#243; 179 t&#7915; (&lt; 250 t&#7915; b&#7855;t bu&#7897;c), quan &#273;i&#7875;m c&#242;n m&#417; h&#7891; v&#224; ch&#432;a &#273;&#432;&#7907;c ph&#225;t tri&#7875;n v&#7899;i d&#7851;n ch&#7913;ng thuy&#7871;t ph&#7909;c.&quot;, &quot;improvements&quot;: [&quot;M&#7903; r&#7897;ng b&#224;i l&#234;n &#237;t nh&#7845;t 260-280 t&#7915;, ph&#225;t tri&#7875;n th&#234;m &#253; &#7903; m&#7895;i &#273;o&#7841;n v&#7899;i v&#237; d&#7909; c&#7909; th&#7875; (s&#7889; li&#7879;u, t&#236;nh hu&#7889;ng th&#7921;c t&#7871;).&quot;, &quot;N&#234;u r&#245; l&#7853;p tr&#432;&#7901;ng ngay t&#7915; m&#7903; b&#224;i v&#224; nh&#7855;c l&#7841;i nh&#7845;t qu&#225;n, s&#7917; d&#7909;ng l&#7853;p lu&#7853;n so s&#225;nh &#8211; ph&#226;n t&#237;ch l&#7907;i h&#7841;i thay v&#236; li&#7879;t k&#234;.&quot;]}, &quot;coherence_cohesion&quot;: {&quot;score&quot;: 4, &quot;issues&quot;: [&quot;D&#7845;u hi&#7879;u li&#234;n k&#7871;t h&#7841;n ch&#7871; (First, However&#8230;), thi&#7871;u therefore, moreover&#8230;&quot;, &quot;Thi&#7871;u ch&#7911; &#273;&#7873; c&#226;u (topic sentence) r&#245; r&#224;ng cho t&#7915;ng &#273;o&#7841;n th&#226;n.&quot;], &quot;feedback&quot;: &quot;B&#7889; c&#7909;c c&#243; m&#7903;-th&#226;n-k&#7871;t nh&#432;ng li&#234;n k&#7871;t l&#7887;ng l&#7867;o, t&#7915; n&#7889;i c&#242;n sai ho&#7863;c l&#7863;p. C&#226;u chuy&#7875;n &#273;o&#7841;n &#273;&#417;n gi&#7843;n n&#234;n &#253; t&#432;&#7903;ng r&#7901;i r&#7841;c.&quot;, &quot;improvements&quot;: [&quot;M&#7895;i &#273;o&#7841;n n&#234;n b&#7855;t &#273;&#7847;u b&#7857;ng c&#226;u ch&#7911; &#273;&#7873;, sau &#273;&#243; gi&#7843;i th&#237;ch v&#224; &#273;&#432;a v&#237; d&#7909;.&quot;, &quot;S&#7917; d&#7909;ng &#273;a d&#7841;ng cohesive devices: besides, as a result, in contrast&#8230; v&#224; d&#249;ng &#273;&#250;ng d&#7845;u ph&#7849;y.&quot;]}}</span>\"\n                  \"<span class=sf-dump-key>highlighted_corrections</span>\" => \"<span class=sf-dump-str title=\"3255 characters\">[{&quot;severity&quot;: &quot;high&quot;, &quot;error_type&quot;: &quot;grammar|spelling&quot;, &quot;explanation&quot;: &quot;thi&#7871;u &#039;s&#039; sau student, thi&#7871;u &#039;s&#039; sau sometimes, sai ch&#237;nh t&#7843; &#039;necessary&#039;&quot;, &quot;original_text&quot;: &quot;International news is good for student but sometime it is not nessessary.&quot;, &quot;suggested_correction&quot;: &quot;International news can be beneficial for students, but sometimes it is not necessary.&quot;}, {&quot;severity&quot;: &quot;high&quot;, &quot;error_type&quot;: &quot;grammar&quot;, &quot;explanation&quot;: &quot;thi&#7871;u s&#7889; nhi&#7873;u &#039;students&#039;, thi&#7871;u li&#234;n t&#7915; &#039;while&#039;, sai chia &#273;&#7897;ng t&#7915; &#039;think&#039;, thi&#7871;u &#273;&#7841;i t&#7915; &#039;it is&#039;.&quot;, &quot;original_text&quot;: &quot;Many people think student in secondary school need to study about international news, other think this waste their time.&quot;, &quot;suggested_correction&quot;: &quot;Many people think (that) secondary school students need to study international news, while others think it is a waste of time.&quot;}, {&quot;severity&quot;: &quot;medium&quot;, &quot;error_type&quot;: &quot;grammar&quot;, &quot;explanation&quot;: &quot;S-V agreement v&#224; t&#237;nh t&#7915; so s&#225;nh h&#417;n.&quot;, &quot;original_text&quot;: &quot;it make student smart.&quot;, &quot;suggested_correction&quot;: &quot;it makes students smarter.&quot;}, {&quot;severity&quot;: &quot;high&quot;, &quot;error_type&quot;: &quot;grammar|spelling|vocabulary&quot;, &quot;explanation&quot;: &quot;thi&#7871;u &#039;For&#039;, plural forms, s&#7903; h&#7919;u c&#225;ch, ch&#237;nh t&#7843; &#039;football&#039;.&quot;, &quot;original_text&quot;: &quot;Example, student can know weather in other country, famous people life, or footbal match.&quot;, &quot;suggested_correction&quot;: &quot;For example, students can learn about the weather in other countries, famous people&#039;s lives, or football matches.&quot;}, {&quot;severity&quot;: &quot;high&quot;, &quot;error_type&quot;: &quot;grammar|vocabulary&quot;, &quot;explanation&quot;: &quot;be interested in, plural &#039;topics&#039;.&quot;, &quot;original_text&quot;: &quot;They will very interesting about these topic&quot;, &quot;suggested_correction&quot;: &quot;They will be very interested in these topics&quot;}, {&quot;severity&quot;: &quot;high&quot;, &quot;error_type&quot;: &quot;grammar|spelling&quot;, &quot;explanation&quot;: &quot;thi&#7871;u &#039;s&#039; sau helps, c&#7847;n &#273;&#7897;ng t&#7915; &#039;improve&#039;, English vi&#7871;t hoa.&quot;, &quot;original_text&quot;: &quot;Learning international news is also help student speaking english well.&quot;, &quot;suggested_correction&quot;: &quot;Learning international news also helps students improve their English speaking skills.&quot;}, {&quot;severity&quot;: &quot;medium&quot;, &quot;error_type&quot;: &quot;grammar&quot;, &quot;explanation&quot;: &quot;sai th&#236; &#273;&#7897;ng t&#7915;, &#273;&#7843;o v&#7883; tr&#237; tr&#7841;ng t&#7915;.&quot;, &quot;original_text&quot;: &quot;However, I thinking international news sometime is boring.&quot;, &quot;suggested_correction&quot;: &quot;However, I think international news is sometimes boring.&quot;}, {&quot;severity&quot;: &quot;high&quot;, &quot;error_type&quot;: &quot;grammar&quot;, &quot;explanation&quot;: &quot;thi&#7871;u &#273;&#7897;ng t&#7915; &#039;is&#039;, thi&#7871;u m&#7841;o t&#7915; &#039;a&#039;.&quot;, &quot;original_text&quot;: &quot;International news not really important, only waste of time.&quot;, &quot;suggested_correction&quot;: &quot;International news is not really important; it is only a waste of time.&quot;}, {&quot;severity&quot;: &quot;medium&quot;, &quot;error_type&quot;: &quot;grammar|vocabulary&quot;, &quot;explanation&quot;: &quot;d&#249;ng &#273;&#7897;ng t&#7915; nguy&#234;n th&#7875; &#039;teach&#039;, plural teachers/students, d&#249;ng &#039;useful&#039;.&quot;, &quot;original_text&quot;: &quot;Teacher should teaching student good subject not international news.&quot;, &quot;suggested_correction&quot;: &quot;Teachers should teach students useful subjects rather than international news.&quot;}, {&quot;severity&quot;: &quot;high&quot;, &quot;error_type&quot;: &quot;grammar|vocabulary|spelling&quot;, &quot;explanation&quot;: &quot;In conclusion, S-V agreement &#039;news has&#039;, danh t&#7915; s&#7889; nhi&#7873;u &#039;disadvantages&#039;.&quot;, &quot;original_text&quot;: &quot;I conclusion, international news have advantage but more disadvantage.&quot;, &quot;suggested_correction&quot;: &quot;In conclusion, international news has some advantages but more disadvantages.&quot;}]</span>\"\n                  \"<span class=sf-dump-key>criteria_analysis</span>\" => \"<span class=sf-dump-str title=\"1665 characters\">[{&quot;criterion&quot;: &quot;Task Achievement&quot;, &quot;strengths&quot;: [&quot;C&#243; &#273;&#7873; c&#7853;p hai quan &#273;i&#7875;m nh&#432; y&#234;u c&#7847;u &#273;&#7873;.&quot;, &quot;N&#234;u &#273;&#432;&#7907;c quan &#273;i&#7875;m c&#225; nh&#226;n &#7903; &#273;o&#7841;n th&#226;n 2 v&#224; 3.&quot;], &quot;weaknesses&quot;: [&quot;B&#224;i d&#432;&#7899;i 250 t&#7915; n&#234;n b&#7883; tr&#7915; &#273;i&#7875;m n&#7863;ng.&quot;, &quot;L&#7853;p lu&#7853;n s&#417; s&#224;i, v&#237; d&#7909; ch&#432;a c&#7909; th&#7875;.&quot;, &quot;Thi&#7871;u ph&#7847;n gi&#7843;i th&#237;ch s&#226;u, kh&#244;ng c&#243; d&#7851;n ch&#7913;ng th&#7889;ng k&#234;.&quot;, &quot;Kh&#244;ng tr&#236;nh b&#224;y r&#245; r&#224;ng l&#253; do &#7911;ng h&#7897; ho&#7863;c ph&#7843;n &#273;&#7889;i.&quot;], &quot;band_justification&quot;: &quot;Theo m&#244; t&#7843; Band 4: tr&#7843; l&#7901;i m&#7897;t ph&#7847;n c&#226;u h&#7887;i, &#253; ch&#237;nh c&#242;n h&#7841;n ch&#7871; v&#224; thi&#7871;u h&#7895; tr&#7907;. B&#224;i n&#224;y ph&#249; h&#7907;p band 4.&quot;}, {&quot;criterion&quot;: &quot;Coherence &amp; Cohesion&quot;, &quot;strengths&quot;: [&quot;C&#243; m&#7903; b&#224;i, th&#226;n b&#224;i, k&#7871;t lu&#7853;n.&quot;, &quot;D&#249;ng &#273;&#432;&#7907;c &#039;Firstly&#039;, &#039;However&#039; &#273;&#7875; chuy&#7875;n &#253;.&quot;], &quot;weaknesses&quot;: [&quot;Li&#234;n k&#7871;t &#253; l&#7887;ng l&#7867;o, t&#7915; n&#7889;i l&#7863;p v&#224; thi&#7871;u.&quot;, &quot;C&#226;u ch&#7911; &#273;&#7873; ch&#432;a r&#245; r&#224;ng, s&#7855;p x&#7871;p &#253; thi&#7871;u logic.&quot;, &quot;Thi&#7871;u tham chi&#7871;u (this/that) v&#224; quan h&#7879; t&#7915;.&quot;, &quot;&#272;o&#7841;n 2 v&#224; 3 g&#7847;n nh&#432; tr&#249;ng n&#7897;i dung.&quot;], &quot;band_justification&quot;: &quot;Theo Band 4: t&#7893; ch&#7913;c th&#244;ng tin l&#7887;ng l&#7867;o, thi&#7871;t b&#7883; li&#234;n k&#7871;t c&#417; b&#7843;n v&#224; th&#432;&#7901;ng l&#7863;p; v&#236; v&#7853;y band 4.&quot;}, {&quot;criterion&quot;: &quot;Lexical Resource&quot;, &quot;strengths&quot;: [&quot;S&#7917; d&#7909;ng m&#7897;t s&#7889; t&#7915; v&#7921;ng li&#234;n quan ch&#7911; &#273;&#7873; nh&#432; &#039;weather&#039;, &#039;famous people&#039;.&quot;, &quot;C&#7889; g&#7855;ng d&#249;ng t&#237;nh t&#7915; (interesting, important).&quot;], &quot;weaknesses&quot;: [&quot;T&#7915; v&#7921;ng r&#7845;t h&#7841;n ch&#7871;, l&#7863;p nhi&#7873;u.&quot;, &quot;Nhi&#7873;u l&#7895;i ch&#237;nh t&#7843;, collocation sai.&quot;, &quot;&#205;t t&#7915; mang t&#237;nh h&#7885;c thu&#7853;t.&quot;], &quot;band_justification&quot;: &quot;Band 4: v&#7889;n t&#7915; h&#7841;n ch&#7871;, l&#7895;i th&#432;&#7901;ng xuy&#234;n g&#226;y tr&#7903; ng&#7841;i giao ti&#7871;p nh&#432;ng &#253; ch&#237;nh v&#7851;n hi&#7875;u.&quot;}, {&quot;criterion&quot;: &quot;Grammar &amp; Accuracy&quot;, &quot;strengths&quot;: [&quot;C&#243; d&#249;ng m&#7897;t s&#7889; th&#236; hi&#7879;n t&#7841;i &#273;&#417;n, c&#7845;u tr&#250;c &#273;&#417;n.&quot;], &quot;weaknesses&quot;: [&quot;L&#7895;i S-V agreement, m&#7841;o t&#7915;, gi&#7899;i t&#7915; ph&#7893; bi&#7871;n.&quot;, &quot;&#205;t c&#226;u ph&#7913;c; c&#7845;u tr&#250;c l&#7863;p.&quot;, &quot;Nhi&#7873;u l&#7895;i &#7843;nh h&#432;&#7903;ng s&#7921; hi&#7875;u.&quot;], &quot;band_justification&quot;: &quot;Ph&#249; h&#7907;p Band 4: L&#7895;i ng&#7919; ph&#225;p th&#432;&#7901;ng xuy&#234;n, &#237;t c&#7845;u tr&#250;c ph&#7913;c t&#7841;p.&quot;}]</span>\"\n                  \"<span class=sf-dump-key>statistics</span>\" => \"<span class=sf-dump-str title=\"144 characters\">{&quot;total_errors&quot;: 38, &quot;grammar_errors&quot;: 24, &quot;spelling_errors&quot;: 8, &quot;complexity_level&quot;: &quot;basic&quot;, &quot;sentence_variety&quot;: &quot;low&quot;, &quot;vocabulary_errors&quot;: 6}</span>\"\n                  \"<span class=sf-dump-key>word_count</span>\" => <span class=sf-dump-num>179</span>\n                  \"<span class=sf-dump-key>essay_length</span>\" => <span class=sf-dump-num>1184</span>\n                  \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"9 characters\">completed</span>\"\n                  \"<span class=sf-dump-key>error_message</span>\" => <span class=sf-dump-const>null</span>\n                  \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-18 15:06:07</span>\"\n                  \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-18 15:06:38</span>\"\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:21</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>9</span>\n                  \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-num>1</span>\n                  \"<span class=sf-dump-key>essay_question_id</span>\" => <span class=sf-dump-const>null</span>\n                  \"<span class=sf-dump-key>essay_content</span>\" => \"\"\"\n                    <span class=sf-dump-str title=\"1184 characters\">International news is good for student but sometime it is not nessessary. Many people think student in secondary school need to study about international news, other think this waste their time. My essay will write about both of these thing.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">Firstly, International news is good because it make student smart. When student learn international news they can know many things. Example, student can know weather in other country, famous people life, or footbal match. They will very interesting about these topic and can relax in the classroom. Learning international news is also help student speaking english well.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">However, I thinking international news sometime is boring. Student have many important subject, example maths, literature, or chemistry. These subjects can help student become doctor or engineer in future. International news not really important, only waste of time.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">In my opinion, secondary student should play sport and learn math better than learn international news. Teacher should teaching student good subject not international news.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">I conclusion, international news have advantage but more disadvantage. Student no need study this subject, waste of their time.</span>\n                    \"\"\"\n                  \"<span class=sf-dump-key>task_type</span>\" => \"<span class=sf-dump-str title=\"5 characters\">task2</span>\"\n                  \"<span class=sf-dump-key>time_limit</span>\" => <span class=sf-dump-num>40</span>\n                  \"<span class=sf-dump-key>overall_band_score</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.0</span>\"\n                  \"<span class=sf-dump-key>task_achievement</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.0</span>\"\n                  \"<span class=sf-dump-key>coherence_cohesion</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.0</span>\"\n                  \"<span class=sf-dump-key>lexical_resource</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.0</span>\"\n                  \"<span class=sf-dump-key>grammar_accuracy</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.0</span>\"\n                  \"<span class=sf-dump-key>detailed_feedback</span>\" => \"<span class=sf-dump-str title=\"2086 characters\">{&quot;grammar_accuracy&quot;: {&quot;score&quot;: 4, &quot;issues&quot;: [&quot;S-V: \\&quot;news is &#8230; it make student\\&quot;, \\&quot;Learning international news is also help&#8230;\\&quot;&quot;, &quot;Thi&#7871;u m&#7841;o t&#7915;: \\&quot;in other country\\&quot;, \\&quot;famous people life\\&quot;&quot;], &quot;feedback&quot;: &quot;L&#7895;i S-V agreement, thi&#7871;u m&#7841;o t&#7915;, gi&#7899;i t&#7915;, th&#236;, c&#7845;u tr&#250;c c&#226;u h&#7847;u h&#7871;t &#7903; m&#7913;c &#273;&#417;n gi&#7843;n.&quot;, &quot;improvements&quot;: [&quot;&#212;n quy t&#7855;c S-V agreement, &#273;&#7863;c bi&#7879;t v&#7899;i danh t&#7915; s&#7889; nhi&#7873;u/kh&#244;ng &#273;&#7871;m &#273;&#432;&#7907;c.&quot;, &quot;K&#7871;t h&#7907;p c&#226;u ph&#7913;c (because, although, which), v&#224; d&#249;ng &#273;&#250;ng th&#236; hi&#7879;n t&#7841;i &#273;&#417;n/hi&#7879;n t&#7841;i ti&#7871;p di&#7877;n.&quot;]}, &quot;lexical_resource&quot;: {&quot;score&quot;: 4, &quot;issues&quot;: [&quot;Ch&#237;nh t&#7843;: nessessary, footbal, interesting (d&#249;ng sai d&#7841;ng), English kh&#244;ng vi&#7871;t hoa&#8230;&quot;, &quot;Collocation sai: \\&quot;very interesting about\\&quot;, \\&quot;good subject\\&quot;, \\&quot;help student speaking\\&quot;&#8230;&quot;], &quot;feedback&quot;: &quot;T&#7915; v&#7921;ng ngh&#232;o, l&#7863;p t&#7915; &#8220;student&#8221;, &#8220;international news&#8221;, sai ch&#237;nh t&#7843; nhi&#7873;u, collocation ch&#432;a ch&#237;nh x&#225;c.&quot;, &quot;improvements&quot;: [&quot;Ghi nh&#7899; ch&#237;nh t&#7843; chu&#7849;n, tra t&#7915; &#273;i&#7875;n tr&#432;&#7899;c khi vi&#7871;t.&quot;, &quot;H&#7885;c collocation: be interested in, improve speaking skills, beneficial subject&#8230; v&#224; thay th&#7871; t&#7915; l&#7863;p b&#7857;ng synonyms.&quot;]}, &quot;task_achievement&quot;: {&quot;score&quot;: 4, &quot;issues&quot;: [&quot;Thi&#7871;u t&#7889;i thi&#7875;u 250 t&#7915;.&quot;, &quot;L&#7853;p lu&#7853;n n&#244;ng, v&#237; d&#7909; chung chung, ch&#432;a ch&#7913;ng minh quan &#273;i&#7875;m.&quot;], &quot;feedback&quot;: &quot;B&#224;i vi&#7871;t ch&#432;a &#273;&#225;p &#7913;ng &#273;&#7847;y &#273;&#7911; y&#234;u c&#7847;u Task 2. B&#224;i ch&#7881; c&#243; 179 t&#7915; (&lt; 250 t&#7915; b&#7855;t bu&#7897;c), quan &#273;i&#7875;m c&#242;n m&#417; h&#7891; v&#224; ch&#432;a &#273;&#432;&#7907;c ph&#225;t tri&#7875;n v&#7899;i d&#7851;n ch&#7913;ng thuy&#7871;t ph&#7909;c.&quot;, &quot;improvements&quot;: [&quot;M&#7903; r&#7897;ng b&#224;i l&#234;n &#237;t nh&#7845;t 260-280 t&#7915;, ph&#225;t tri&#7875;n th&#234;m &#253; &#7903; m&#7895;i &#273;o&#7841;n v&#7899;i v&#237; d&#7909; c&#7909; th&#7875; (s&#7889; li&#7879;u, t&#236;nh hu&#7889;ng th&#7921;c t&#7871;).&quot;, &quot;N&#234;u r&#245; l&#7853;p tr&#432;&#7901;ng ngay t&#7915; m&#7903; b&#224;i v&#224; nh&#7855;c l&#7841;i nh&#7845;t qu&#225;n, s&#7917; d&#7909;ng l&#7853;p lu&#7853;n so s&#225;nh &#8211; ph&#226;n t&#237;ch l&#7907;i h&#7841;i thay v&#236; li&#7879;t k&#234;.&quot;]}, &quot;coherence_cohesion&quot;: {&quot;score&quot;: 4, &quot;issues&quot;: [&quot;D&#7845;u hi&#7879;u li&#234;n k&#7871;t h&#7841;n ch&#7871; (First, However&#8230;), thi&#7871;u therefore, moreover&#8230;&quot;, &quot;Thi&#7871;u ch&#7911; &#273;&#7873; c&#226;u (topic sentence) r&#245; r&#224;ng cho t&#7915;ng &#273;o&#7841;n th&#226;n.&quot;], &quot;feedback&quot;: &quot;B&#7889; c&#7909;c c&#243; m&#7903;-th&#226;n-k&#7871;t nh&#432;ng li&#234;n k&#7871;t l&#7887;ng l&#7867;o, t&#7915; n&#7889;i c&#242;n sai ho&#7863;c l&#7863;p. C&#226;u chuy&#7875;n &#273;o&#7841;n &#273;&#417;n gi&#7843;n n&#234;n &#253; t&#432;&#7903;ng r&#7901;i r&#7841;c.&quot;, &quot;improvements&quot;: [&quot;M&#7895;i &#273;o&#7841;n n&#234;n b&#7855;t &#273;&#7847;u b&#7857;ng c&#226;u ch&#7911; &#273;&#7873;, sau &#273;&#243; gi&#7843;i th&#237;ch v&#224; &#273;&#432;a v&#237; d&#7909;.&quot;, &quot;S&#7917; d&#7909;ng &#273;a d&#7841;ng cohesive devices: besides, as a result, in contrast&#8230; v&#224; d&#249;ng &#273;&#250;ng d&#7845;u ph&#7849;y.&quot;]}}</span>\"\n                  \"<span class=sf-dump-key>highlighted_corrections</span>\" => \"<span class=sf-dump-str title=\"3255 characters\">[{&quot;severity&quot;: &quot;high&quot;, &quot;error_type&quot;: &quot;grammar|spelling&quot;, &quot;explanation&quot;: &quot;thi&#7871;u &#039;s&#039; sau student, thi&#7871;u &#039;s&#039; sau sometimes, sai ch&#237;nh t&#7843; &#039;necessary&#039;&quot;, &quot;original_text&quot;: &quot;International news is good for student but sometime it is not nessessary.&quot;, &quot;suggested_correction&quot;: &quot;International news can be beneficial for students, but sometimes it is not necessary.&quot;}, {&quot;severity&quot;: &quot;high&quot;, &quot;error_type&quot;: &quot;grammar&quot;, &quot;explanation&quot;: &quot;thi&#7871;u s&#7889; nhi&#7873;u &#039;students&#039;, thi&#7871;u li&#234;n t&#7915; &#039;while&#039;, sai chia &#273;&#7897;ng t&#7915; &#039;think&#039;, thi&#7871;u &#273;&#7841;i t&#7915; &#039;it is&#039;.&quot;, &quot;original_text&quot;: &quot;Many people think student in secondary school need to study about international news, other think this waste their time.&quot;, &quot;suggested_correction&quot;: &quot;Many people think (that) secondary school students need to study international news, while others think it is a waste of time.&quot;}, {&quot;severity&quot;: &quot;medium&quot;, &quot;error_type&quot;: &quot;grammar&quot;, &quot;explanation&quot;: &quot;S-V agreement v&#224; t&#237;nh t&#7915; so s&#225;nh h&#417;n.&quot;, &quot;original_text&quot;: &quot;it make student smart.&quot;, &quot;suggested_correction&quot;: &quot;it makes students smarter.&quot;}, {&quot;severity&quot;: &quot;high&quot;, &quot;error_type&quot;: &quot;grammar|spelling|vocabulary&quot;, &quot;explanation&quot;: &quot;thi&#7871;u &#039;For&#039;, plural forms, s&#7903; h&#7919;u c&#225;ch, ch&#237;nh t&#7843; &#039;football&#039;.&quot;, &quot;original_text&quot;: &quot;Example, student can know weather in other country, famous people life, or footbal match.&quot;, &quot;suggested_correction&quot;: &quot;For example, students can learn about the weather in other countries, famous people&#039;s lives, or football matches.&quot;}, {&quot;severity&quot;: &quot;high&quot;, &quot;error_type&quot;: &quot;grammar|vocabulary&quot;, &quot;explanation&quot;: &quot;be interested in, plural &#039;topics&#039;.&quot;, &quot;original_text&quot;: &quot;They will very interesting about these topic&quot;, &quot;suggested_correction&quot;: &quot;They will be very interested in these topics&quot;}, {&quot;severity&quot;: &quot;high&quot;, &quot;error_type&quot;: &quot;grammar|spelling&quot;, &quot;explanation&quot;: &quot;thi&#7871;u &#039;s&#039; sau helps, c&#7847;n &#273;&#7897;ng t&#7915; &#039;improve&#039;, English vi&#7871;t hoa.&quot;, &quot;original_text&quot;: &quot;Learning international news is also help student speaking english well.&quot;, &quot;suggested_correction&quot;: &quot;Learning international news also helps students improve their English speaking skills.&quot;}, {&quot;severity&quot;: &quot;medium&quot;, &quot;error_type&quot;: &quot;grammar&quot;, &quot;explanation&quot;: &quot;sai th&#236; &#273;&#7897;ng t&#7915;, &#273;&#7843;o v&#7883; tr&#237; tr&#7841;ng t&#7915;.&quot;, &quot;original_text&quot;: &quot;However, I thinking international news sometime is boring.&quot;, &quot;suggested_correction&quot;: &quot;However, I think international news is sometimes boring.&quot;}, {&quot;severity&quot;: &quot;high&quot;, &quot;error_type&quot;: &quot;grammar&quot;, &quot;explanation&quot;: &quot;thi&#7871;u &#273;&#7897;ng t&#7915; &#039;is&#039;, thi&#7871;u m&#7841;o t&#7915; &#039;a&#039;.&quot;, &quot;original_text&quot;: &quot;International news not really important, only waste of time.&quot;, &quot;suggested_correction&quot;: &quot;International news is not really important; it is only a waste of time.&quot;}, {&quot;severity&quot;: &quot;medium&quot;, &quot;error_type&quot;: &quot;grammar|vocabulary&quot;, &quot;explanation&quot;: &quot;d&#249;ng &#273;&#7897;ng t&#7915; nguy&#234;n th&#7875; &#039;teach&#039;, plural teachers/students, d&#249;ng &#039;useful&#039;.&quot;, &quot;original_text&quot;: &quot;Teacher should teaching student good subject not international news.&quot;, &quot;suggested_correction&quot;: &quot;Teachers should teach students useful subjects rather than international news.&quot;}, {&quot;severity&quot;: &quot;high&quot;, &quot;error_type&quot;: &quot;grammar|vocabulary|spelling&quot;, &quot;explanation&quot;: &quot;In conclusion, S-V agreement &#039;news has&#039;, danh t&#7915; s&#7889; nhi&#7873;u &#039;disadvantages&#039;.&quot;, &quot;original_text&quot;: &quot;I conclusion, international news have advantage but more disadvantage.&quot;, &quot;suggested_correction&quot;: &quot;In conclusion, international news has some advantages but more disadvantages.&quot;}]</span>\"\n                  \"<span class=sf-dump-key>criteria_analysis</span>\" => \"<span class=sf-dump-str title=\"1665 characters\">[{&quot;criterion&quot;: &quot;Task Achievement&quot;, &quot;strengths&quot;: [&quot;C&#243; &#273;&#7873; c&#7853;p hai quan &#273;i&#7875;m nh&#432; y&#234;u c&#7847;u &#273;&#7873;.&quot;, &quot;N&#234;u &#273;&#432;&#7907;c quan &#273;i&#7875;m c&#225; nh&#226;n &#7903; &#273;o&#7841;n th&#226;n 2 v&#224; 3.&quot;], &quot;weaknesses&quot;: [&quot;B&#224;i d&#432;&#7899;i 250 t&#7915; n&#234;n b&#7883; tr&#7915; &#273;i&#7875;m n&#7863;ng.&quot;, &quot;L&#7853;p lu&#7853;n s&#417; s&#224;i, v&#237; d&#7909; ch&#432;a c&#7909; th&#7875;.&quot;, &quot;Thi&#7871;u ph&#7847;n gi&#7843;i th&#237;ch s&#226;u, kh&#244;ng c&#243; d&#7851;n ch&#7913;ng th&#7889;ng k&#234;.&quot;, &quot;Kh&#244;ng tr&#236;nh b&#224;y r&#245; r&#224;ng l&#253; do &#7911;ng h&#7897; ho&#7863;c ph&#7843;n &#273;&#7889;i.&quot;], &quot;band_justification&quot;: &quot;Theo m&#244; t&#7843; Band 4: tr&#7843; l&#7901;i m&#7897;t ph&#7847;n c&#226;u h&#7887;i, &#253; ch&#237;nh c&#242;n h&#7841;n ch&#7871; v&#224; thi&#7871;u h&#7895; tr&#7907;. B&#224;i n&#224;y ph&#249; h&#7907;p band 4.&quot;}, {&quot;criterion&quot;: &quot;Coherence &amp; Cohesion&quot;, &quot;strengths&quot;: [&quot;C&#243; m&#7903; b&#224;i, th&#226;n b&#224;i, k&#7871;t lu&#7853;n.&quot;, &quot;D&#249;ng &#273;&#432;&#7907;c &#039;Firstly&#039;, &#039;However&#039; &#273;&#7875; chuy&#7875;n &#253;.&quot;], &quot;weaknesses&quot;: [&quot;Li&#234;n k&#7871;t &#253; l&#7887;ng l&#7867;o, t&#7915; n&#7889;i l&#7863;p v&#224; thi&#7871;u.&quot;, &quot;C&#226;u ch&#7911; &#273;&#7873; ch&#432;a r&#245; r&#224;ng, s&#7855;p x&#7871;p &#253; thi&#7871;u logic.&quot;, &quot;Thi&#7871;u tham chi&#7871;u (this/that) v&#224; quan h&#7879; t&#7915;.&quot;, &quot;&#272;o&#7841;n 2 v&#224; 3 g&#7847;n nh&#432; tr&#249;ng n&#7897;i dung.&quot;], &quot;band_justification&quot;: &quot;Theo Band 4: t&#7893; ch&#7913;c th&#244;ng tin l&#7887;ng l&#7867;o, thi&#7871;t b&#7883; li&#234;n k&#7871;t c&#417; b&#7843;n v&#224; th&#432;&#7901;ng l&#7863;p; v&#236; v&#7853;y band 4.&quot;}, {&quot;criterion&quot;: &quot;Lexical Resource&quot;, &quot;strengths&quot;: [&quot;S&#7917; d&#7909;ng m&#7897;t s&#7889; t&#7915; v&#7921;ng li&#234;n quan ch&#7911; &#273;&#7873; nh&#432; &#039;weather&#039;, &#039;famous people&#039;.&quot;, &quot;C&#7889; g&#7855;ng d&#249;ng t&#237;nh t&#7915; (interesting, important).&quot;], &quot;weaknesses&quot;: [&quot;T&#7915; v&#7921;ng r&#7845;t h&#7841;n ch&#7871;, l&#7863;p nhi&#7873;u.&quot;, &quot;Nhi&#7873;u l&#7895;i ch&#237;nh t&#7843;, collocation sai.&quot;, &quot;&#205;t t&#7915; mang t&#237;nh h&#7885;c thu&#7853;t.&quot;], &quot;band_justification&quot;: &quot;Band 4: v&#7889;n t&#7915; h&#7841;n ch&#7871;, l&#7895;i th&#432;&#7901;ng xuy&#234;n g&#226;y tr&#7903; ng&#7841;i giao ti&#7871;p nh&#432;ng &#253; ch&#237;nh v&#7851;n hi&#7875;u.&quot;}, {&quot;criterion&quot;: &quot;Grammar &amp; Accuracy&quot;, &quot;strengths&quot;: [&quot;C&#243; d&#249;ng m&#7897;t s&#7889; th&#236; hi&#7879;n t&#7841;i &#273;&#417;n, c&#7845;u tr&#250;c &#273;&#417;n.&quot;], &quot;weaknesses&quot;: [&quot;L&#7895;i S-V agreement, m&#7841;o t&#7915;, gi&#7899;i t&#7915; ph&#7893; bi&#7871;n.&quot;, &quot;&#205;t c&#226;u ph&#7913;c; c&#7845;u tr&#250;c l&#7863;p.&quot;, &quot;Nhi&#7873;u l&#7895;i &#7843;nh h&#432;&#7903;ng s&#7921; hi&#7875;u.&quot;], &quot;band_justification&quot;: &quot;Ph&#249; h&#7907;p Band 4: L&#7895;i ng&#7919; ph&#225;p th&#432;&#7901;ng xuy&#234;n, &#237;t c&#7845;u tr&#250;c ph&#7913;c t&#7841;p.&quot;}]</span>\"\n                  \"<span class=sf-dump-key>statistics</span>\" => \"<span class=sf-dump-str title=\"144 characters\">{&quot;total_errors&quot;: 38, &quot;grammar_errors&quot;: 24, &quot;spelling_errors&quot;: 8, &quot;complexity_level&quot;: &quot;basic&quot;, &quot;sentence_variety&quot;: &quot;low&quot;, &quot;vocabulary_errors&quot;: 6}</span>\"\n                  \"<span class=sf-dump-key>word_count</span>\" => <span class=sf-dump-num>179</span>\n                  \"<span class=sf-dump-key>essay_length</span>\" => <span class=sf-dump-num>1184</span>\n                  \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"9 characters\">completed</span>\"\n                  \"<span class=sf-dump-key>error_message</span>\" => <span class=sf-dump-const>null</span>\n                  \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-18 15:06:07</span>\"\n                  \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-18 15:06:38</span>\"\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">previous</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:9</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>detailed_feedback</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                  \"<span class=sf-dump-key>highlighted_corrections</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                  \"<span class=sf-dump-key>criteria_analysis</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                  \"<span class=sf-dump-key>statistics</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                  \"<span class=sf-dump-key>overall_band_score</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:1</span>\"\n                  \"<span class=sf-dump-key>task_achievement</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:1</span>\"\n                  \"<span class=sf-dump-key>coherence_cohesion</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:1</span>\"\n                  \"<span class=sf-dump-key>lexical_resource</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:1</span>\"\n                  \"<span class=sf-dump-key>grammar_accuracy</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:1</span>\"\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>essayQuestion</span>\" => <span class=sf-dump-const>null</span>\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadCallback</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadContext</span>: <span class=sf-dump-const>null</span>\n                +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:18</span> [<samp data-depth=9 class=sf-dump-compact>\n                  <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">user_id</span>\"\n                  <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"17 characters\">essay_question_id</span>\"\n                  <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"13 characters\">essay_content</span>\"\n                  <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"9 characters\">task_type</span>\"\n                  <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"10 characters\">time_limit</span>\"\n                  <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"18 characters\">overall_band_score</span>\"\n                  <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"16 characters\">task_achievement</span>\"\n                  <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"18 characters\">coherence_cohesion</span>\"\n                  <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"16 characters\">lexical_resource</span>\"\n                  <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"16 characters\">grammar_accuracy</span>\"\n                  <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"17 characters\">detailed_feedback</span>\"\n                  <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"23 characters\">highlighted_corrections</span>\"\n                  <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"17 characters\">criteria_analysis</span>\"\n                  <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"10 characters\">statistics</span>\"\n                  <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"10 characters\">word_count</span>\"\n                  <span class=sf-dump-index>15</span> => \"<span class=sf-dump-str title=\"12 characters\">essay_length</span>\"\n                  <span class=sf-dump-index>16</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n                  <span class=sf-dump-index>17</span> => \"<span class=sf-dump-str title=\"13 characters\">error_message</span>\"\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=9 class=sf-dump-compact>\n                  <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n                </samp>]\n              </samp>}\n              <span class=sf-dump-index>1</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\ScoringAttempt\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ScoringAttempt</span></span> {<a class=sf-dump-ref>#1488</a><samp data-depth=8 class=sf-dump-compact>\n                #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"16 characters\">scoring_attempts</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:21</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>5</span>\n                  \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-num>1</span>\n                  \"<span class=sf-dump-key>essay_question_id</span>\" => <span class=sf-dump-const>null</span>\n                  \"<span class=sf-dump-key>essay_content</span>\" => \"\"\"\n                    <span class=sf-dump-str title=\"1184 characters\">International news is good for student but sometime it is not nessessary. Many people think student in secondary school need to study about international news, other think this waste their time. My essay will write about both of these thing.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">Firstly, International news is good because it make student smart. When student learn international news they can know many things. Example, student can know weather in other country, famous people life, or footbal match. They will very interesting about these topic and can relax in the classroom. Learning international news is also help student speaking english well.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">However, I thinking international news sometime is boring. Student have many important subject, example maths, literature, or chemistry. These subjects can help student become doctor or engineer in future. International news not really important, only waste of time.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">In my opinion, secondary student should play sport and learn math better than learn international news. Teacher should teaching student good subject not international news.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">I conclusion, international news have advantage but more disadvantage. Student no need study this subject, waste of their time.</span>\n                    \"\"\"\n                  \"<span class=sf-dump-key>task_type</span>\" => \"<span class=sf-dump-str title=\"5 characters\">task2</span>\"\n                  \"<span class=sf-dump-key>time_limit</span>\" => <span class=sf-dump-num>40</span>\n                  \"<span class=sf-dump-key>overall_band_score</span>\" => \"<span class=sf-dump-str title=\"3 characters\">5.0</span>\"\n                  \"<span class=sf-dump-key>task_achievement</span>\" => \"<span class=sf-dump-str title=\"3 characters\">5.1</span>\"\n                  \"<span class=sf-dump-key>coherence_cohesion</span>\" => \"<span class=sf-dump-str title=\"3 characters\">5.2</span>\"\n                  \"<span class=sf-dump-key>lexical_resource</span>\" => \"<span class=sf-dump-str title=\"3 characters\">5.0</span>\"\n                  \"<span class=sf-dump-key>grammar_accuracy</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.8</span>\"\n                  \"<span class=sf-dump-key>detailed_feedback</span>\" => \"<span class=sf-dump-str title=\"208 characters\">{&quot;task_achievement&quot;: {&quot;score&quot;: 5, &quot;issues&quot;: [&quot;&#272;&#226;y l&#224; ph&#7843;n h&#7891;i demo&quot;], &quot;feedback&quot;: &quot;H&#7879; th&#7889;ng &#273;ang trong ch&#7871; &#273;&#7897; demo. &#272;&#226;y l&#224; k&#7871;t qu&#7843; m&#7851;u.&quot;, &quot;improvements&quot;: [&quot;Vui l&#242;ng th&#7917; l&#7841;i sau khi h&#7879; th&#7889;ng &#273;&#432;&#7907;c kh&#244;i ph&#7909;c&quot;]}}</span>\"\n                  \"<span class=sf-dump-key>highlighted_corrections</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n                  \"<span class=sf-dump-key>criteria_analysis</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n                  \"<span class=sf-dump-key>statistics</span>\" => \"<span class=sf-dump-str title=\"152 characters\">{&quot;total_errors&quot;: 7, &quot;grammar_errors&quot;: 6, &quot;spelling_errors&quot;: 2, &quot;complexity_level&quot;: &quot;intermediate&quot;, &quot;sentence_variety&quot;: &quot;medium&quot;, &quot;vocabulary_errors&quot;: 1}</span>\"\n                  \"<span class=sf-dump-key>word_count</span>\" => <span class=sf-dump-num>179</span>\n                  \"<span class=sf-dump-key>essay_length</span>\" => <span class=sf-dump-num>1184</span>\n                  \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"9 characters\">completed</span>\"\n                  \"<span class=sf-dump-key>error_message</span>\" => <span class=sf-dump-const>null</span>\n                  \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-18 14:56:40</span>\"\n                  \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-18 14:56:44</span>\"\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:21</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>5</span>\n                  \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-num>1</span>\n                  \"<span class=sf-dump-key>essay_question_id</span>\" => <span class=sf-dump-const>null</span>\n                  \"<span class=sf-dump-key>essay_content</span>\" => \"\"\"\n                    <span class=sf-dump-str title=\"1184 characters\">International news is good for student but sometime it is not nessessary. Many people think student in secondary school need to study about international news, other think this waste their time. My essay will write about both of these thing.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">Firstly, International news is good because it make student smart. When student learn international news they can know many things. Example, student can know weather in other country, famous people life, or footbal match. They will very interesting about these topic and can relax in the classroom. Learning international news is also help student speaking english well.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">However, I thinking international news sometime is boring. Student have many important subject, example maths, literature, or chemistry. These subjects can help student become doctor or engineer in future. International news not really important, only waste of time.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">In my opinion, secondary student should play sport and learn math better than learn international news. Teacher should teaching student good subject not international news.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">I conclusion, international news have advantage but more disadvantage. Student no need study this subject, waste of their time.</span>\n                    \"\"\"\n                  \"<span class=sf-dump-key>task_type</span>\" => \"<span class=sf-dump-str title=\"5 characters\">task2</span>\"\n                  \"<span class=sf-dump-key>time_limit</span>\" => <span class=sf-dump-num>40</span>\n                  \"<span class=sf-dump-key>overall_band_score</span>\" => \"<span class=sf-dump-str title=\"3 characters\">5.0</span>\"\n                  \"<span class=sf-dump-key>task_achievement</span>\" => \"<span class=sf-dump-str title=\"3 characters\">5.1</span>\"\n                  \"<span class=sf-dump-key>coherence_cohesion</span>\" => \"<span class=sf-dump-str title=\"3 characters\">5.2</span>\"\n                  \"<span class=sf-dump-key>lexical_resource</span>\" => \"<span class=sf-dump-str title=\"3 characters\">5.0</span>\"\n                  \"<span class=sf-dump-key>grammar_accuracy</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.8</span>\"\n                  \"<span class=sf-dump-key>detailed_feedback</span>\" => \"<span class=sf-dump-str title=\"208 characters\">{&quot;task_achievement&quot;: {&quot;score&quot;: 5, &quot;issues&quot;: [&quot;&#272;&#226;y l&#224; ph&#7843;n h&#7891;i demo&quot;], &quot;feedback&quot;: &quot;H&#7879; th&#7889;ng &#273;ang trong ch&#7871; &#273;&#7897; demo. &#272;&#226;y l&#224; k&#7871;t qu&#7843; m&#7851;u.&quot;, &quot;improvements&quot;: [&quot;Vui l&#242;ng th&#7917; l&#7841;i sau khi h&#7879; th&#7889;ng &#273;&#432;&#7907;c kh&#244;i ph&#7909;c&quot;]}}</span>\"\n                  \"<span class=sf-dump-key>highlighted_corrections</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n                  \"<span class=sf-dump-key>criteria_analysis</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n                  \"<span class=sf-dump-key>statistics</span>\" => \"<span class=sf-dump-str title=\"152 characters\">{&quot;total_errors&quot;: 7, &quot;grammar_errors&quot;: 6, &quot;spelling_errors&quot;: 2, &quot;complexity_level&quot;: &quot;intermediate&quot;, &quot;sentence_variety&quot;: &quot;medium&quot;, &quot;vocabulary_errors&quot;: 1}</span>\"\n                  \"<span class=sf-dump-key>word_count</span>\" => <span class=sf-dump-num>179</span>\n                  \"<span class=sf-dump-key>essay_length</span>\" => <span class=sf-dump-num>1184</span>\n                  \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"9 characters\">completed</span>\"\n                  \"<span class=sf-dump-key>error_message</span>\" => <span class=sf-dump-const>null</span>\n                  \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-18 14:56:40</span>\"\n                  \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-18 14:56:44</span>\"\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">previous</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:9</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>detailed_feedback</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                  \"<span class=sf-dump-key>highlighted_corrections</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                  \"<span class=sf-dump-key>criteria_analysis</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                  \"<span class=sf-dump-key>statistics</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                  \"<span class=sf-dump-key>overall_band_score</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:1</span>\"\n                  \"<span class=sf-dump-key>task_achievement</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:1</span>\"\n                  \"<span class=sf-dump-key>coherence_cohesion</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:1</span>\"\n                  \"<span class=sf-dump-key>lexical_resource</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:1</span>\"\n                  \"<span class=sf-dump-key>grammar_accuracy</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:1</span>\"\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>essayQuestion</span>\" => <span class=sf-dump-const>null</span>\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadCallback</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadContext</span>: <span class=sf-dump-const>null</span>\n                +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:18</span> [<samp data-depth=9 class=sf-dump-compact>\n                  <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">user_id</span>\"\n                  <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"17 characters\">essay_question_id</span>\"\n                  <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"13 characters\">essay_content</span>\"\n                  <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"9 characters\">task_type</span>\"\n                  <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"10 characters\">time_limit</span>\"\n                  <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"18 characters\">overall_band_score</span>\"\n                  <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"16 characters\">task_achievement</span>\"\n                  <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"18 characters\">coherence_cohesion</span>\"\n                  <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"16 characters\">lexical_resource</span>\"\n                  <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"16 characters\">grammar_accuracy</span>\"\n                  <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"17 characters\">detailed_feedback</span>\"\n                  <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"23 characters\">highlighted_corrections</span>\"\n                  <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"17 characters\">criteria_analysis</span>\"\n                  <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"10 characters\">statistics</span>\"\n                  <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"10 characters\">word_count</span>\"\n                  <span class=sf-dump-index>15</span> => \"<span class=sf-dump-str title=\"12 characters\">essay_length</span>\"\n                  <span class=sf-dump-index>16</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n                  <span class=sf-dump-index>17</span> => \"<span class=sf-dump-str title=\"13 characters\">error_message</span>\"\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=9 class=sf-dump-compact>\n                  <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n                </samp>]\n              </samp>}\n              <span class=sf-dump-index>2</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\ScoringAttempt\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ScoringAttempt</span></span> {<a class=sf-dump-ref>#1487</a><samp data-depth=8 class=sf-dump-compact>\n                #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"16 characters\">scoring_attempts</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:21</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>4</span>\n                  \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-num>1</span>\n                  \"<span class=sf-dump-key>essay_question_id</span>\" => <span class=sf-dump-const>null</span>\n                  \"<span class=sf-dump-key>essay_content</span>\" => \"\"\"\n                    <span class=sf-dump-str title=\"1184 characters\">International news is good for student but sometime it is not nessessary. Many people think student in secondary school need to study about international news, other think this waste their time. My essay will write about both of these thing.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">Firstly, International news is good because it make student smart. When student learn international news they can know many things. Example, student can know weather in other country, famous people life, or footbal match. They will very interesting about these topic and can relax in the classroom. Learning international news is also help student speaking english well.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">However, I thinking international news sometime is boring. Student have many important subject, example maths, literature, or chemistry. These subjects can help student become doctor or engineer in future. International news not really important, only waste of time.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">In my opinion, secondary student should play sport and learn math better than learn international news. Teacher should teaching student good subject not international news.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">I conclusion, international news have advantage but more disadvantage. Student no need study this subject, waste of their time.</span>\n                    \"\"\"\n                  \"<span class=sf-dump-key>task_type</span>\" => \"<span class=sf-dump-str title=\"5 characters\">task2</span>\"\n                  \"<span class=sf-dump-key>time_limit</span>\" => <span class=sf-dump-num>40</span>\n                  \"<span class=sf-dump-key>overall_band_score</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.4</span>\"\n                  \"<span class=sf-dump-key>task_achievement</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.1</span>\"\n                  \"<span class=sf-dump-key>coherence_cohesion</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.4</span>\"\n                  \"<span class=sf-dump-key>lexical_resource</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.6</span>\"\n                  \"<span class=sf-dump-key>grammar_accuracy</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.3</span>\"\n                  \"<span class=sf-dump-key>detailed_feedback</span>\" => \"<span class=sf-dump-str title=\"210 characters\">{&quot;task_achievement&quot;: {&quot;score&quot;: 4.4, &quot;issues&quot;: [&quot;&#272;&#226;y l&#224; ph&#7843;n h&#7891;i demo&quot;], &quot;feedback&quot;: &quot;H&#7879; th&#7889;ng &#273;ang trong ch&#7871; &#273;&#7897; demo. &#272;&#226;y l&#224; k&#7871;t qu&#7843; m&#7851;u.&quot;, &quot;improvements&quot;: [&quot;Vui l&#242;ng th&#7917; l&#7841;i sau khi h&#7879; th&#7889;ng &#273;&#432;&#7907;c kh&#244;i ph&#7909;c&quot;]}}</span>\"\n                  \"<span class=sf-dump-key>highlighted_corrections</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n                  \"<span class=sf-dump-key>criteria_analysis</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n                  \"<span class=sf-dump-key>statistics</span>\" => \"<span class=sf-dump-str title=\"153 characters\">{&quot;total_errors&quot;: 10, &quot;grammar_errors&quot;: 4, &quot;spelling_errors&quot;: 4, &quot;complexity_level&quot;: &quot;intermediate&quot;, &quot;sentence_variety&quot;: &quot;medium&quot;, &quot;vocabulary_errors&quot;: 2}</span>\"\n                  \"<span class=sf-dump-key>word_count</span>\" => <span class=sf-dump-num>179</span>\n                  \"<span class=sf-dump-key>essay_length</span>\" => <span class=sf-dump-num>1184</span>\n                  \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"9 characters\">completed</span>\"\n                  \"<span class=sf-dump-key>error_message</span>\" => <span class=sf-dump-const>null</span>\n                  \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-18 14:54:37</span>\"\n                  \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-18 14:54:41</span>\"\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:21</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>4</span>\n                  \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-num>1</span>\n                  \"<span class=sf-dump-key>essay_question_id</span>\" => <span class=sf-dump-const>null</span>\n                  \"<span class=sf-dump-key>essay_content</span>\" => \"\"\"\n                    <span class=sf-dump-str title=\"1184 characters\">International news is good for student but sometime it is not nessessary. Many people think student in secondary school need to study about international news, other think this waste their time. My essay will write about both of these thing.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">Firstly, International news is good because it make student smart. When student learn international news they can know many things. Example, student can know weather in other country, famous people life, or footbal match. They will very interesting about these topic and can relax in the classroom. Learning international news is also help student speaking english well.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">However, I thinking international news sometime is boring. Student have many important subject, example maths, literature, or chemistry. These subjects can help student become doctor or engineer in future. International news not really important, only waste of time.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">In my opinion, secondary student should play sport and learn math better than learn international news. Teacher should teaching student good subject not international news.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">I conclusion, international news have advantage but more disadvantage. Student no need study this subject, waste of their time.</span>\n                    \"\"\"\n                  \"<span class=sf-dump-key>task_type</span>\" => \"<span class=sf-dump-str title=\"5 characters\">task2</span>\"\n                  \"<span class=sf-dump-key>time_limit</span>\" => <span class=sf-dump-num>40</span>\n                  \"<span class=sf-dump-key>overall_band_score</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.4</span>\"\n                  \"<span class=sf-dump-key>task_achievement</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.1</span>\"\n                  \"<span class=sf-dump-key>coherence_cohesion</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.4</span>\"\n                  \"<span class=sf-dump-key>lexical_resource</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.6</span>\"\n                  \"<span class=sf-dump-key>grammar_accuracy</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.3</span>\"\n                  \"<span class=sf-dump-key>detailed_feedback</span>\" => \"<span class=sf-dump-str title=\"210 characters\">{&quot;task_achievement&quot;: {&quot;score&quot;: 4.4, &quot;issues&quot;: [&quot;&#272;&#226;y l&#224; ph&#7843;n h&#7891;i demo&quot;], &quot;feedback&quot;: &quot;H&#7879; th&#7889;ng &#273;ang trong ch&#7871; &#273;&#7897; demo. &#272;&#226;y l&#224; k&#7871;t qu&#7843; m&#7851;u.&quot;, &quot;improvements&quot;: [&quot;Vui l&#242;ng th&#7917; l&#7841;i sau khi h&#7879; th&#7889;ng &#273;&#432;&#7907;c kh&#244;i ph&#7909;c&quot;]}}</span>\"\n                  \"<span class=sf-dump-key>highlighted_corrections</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n                  \"<span class=sf-dump-key>criteria_analysis</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n                  \"<span class=sf-dump-key>statistics</span>\" => \"<span class=sf-dump-str title=\"153 characters\">{&quot;total_errors&quot;: 10, &quot;grammar_errors&quot;: 4, &quot;spelling_errors&quot;: 4, &quot;complexity_level&quot;: &quot;intermediate&quot;, &quot;sentence_variety&quot;: &quot;medium&quot;, &quot;vocabulary_errors&quot;: 2}</span>\"\n                  \"<span class=sf-dump-key>word_count</span>\" => <span class=sf-dump-num>179</span>\n                  \"<span class=sf-dump-key>essay_length</span>\" => <span class=sf-dump-num>1184</span>\n                  \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"9 characters\">completed</span>\"\n                  \"<span class=sf-dump-key>error_message</span>\" => <span class=sf-dump-const>null</span>\n                  \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-18 14:54:37</span>\"\n                  \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-18 14:54:41</span>\"\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">previous</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:9</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>detailed_feedback</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                  \"<span class=sf-dump-key>highlighted_corrections</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                  \"<span class=sf-dump-key>criteria_analysis</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                  \"<span class=sf-dump-key>statistics</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                  \"<span class=sf-dump-key>overall_band_score</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:1</span>\"\n                  \"<span class=sf-dump-key>task_achievement</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:1</span>\"\n                  \"<span class=sf-dump-key>coherence_cohesion</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:1</span>\"\n                  \"<span class=sf-dump-key>lexical_resource</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:1</span>\"\n                  \"<span class=sf-dump-key>grammar_accuracy</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:1</span>\"\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>essayQuestion</span>\" => <span class=sf-dump-const>null</span>\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadCallback</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadContext</span>: <span class=sf-dump-const>null</span>\n                +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:18</span> [<samp data-depth=9 class=sf-dump-compact>\n                  <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">user_id</span>\"\n                  <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"17 characters\">essay_question_id</span>\"\n                  <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"13 characters\">essay_content</span>\"\n                  <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"9 characters\">task_type</span>\"\n                  <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"10 characters\">time_limit</span>\"\n                  <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"18 characters\">overall_band_score</span>\"\n                  <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"16 characters\">task_achievement</span>\"\n                  <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"18 characters\">coherence_cohesion</span>\"\n                  <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"16 characters\">lexical_resource</span>\"\n                  <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"16 characters\">grammar_accuracy</span>\"\n                  <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"17 characters\">detailed_feedback</span>\"\n                  <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"23 characters\">highlighted_corrections</span>\"\n                  <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"17 characters\">criteria_analysis</span>\"\n                  <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"10 characters\">statistics</span>\"\n                  <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"10 characters\">word_count</span>\"\n                  <span class=sf-dump-index>15</span> => \"<span class=sf-dump-str title=\"12 characters\">essay_length</span>\"\n                  <span class=sf-dump-index>16</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n                  <span class=sf-dump-index>17</span> => \"<span class=sf-dump-str title=\"13 characters\">error_message</span>\"\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=9 class=sf-dump-compact>\n                  <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n                </samp>]\n              </samp>}\n              <span class=sf-dump-index>3</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\ScoringAttempt\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ScoringAttempt</span></span> {<a class=sf-dump-ref>#1486</a><samp data-depth=8 class=sf-dump-compact>\n                #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"16 characters\">scoring_attempts</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:21</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>2</span>\n                  \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-num>1</span>\n                  \"<span class=sf-dump-key>essay_question_id</span>\" => <span class=sf-dump-const>null</span>\n                  \"<span class=sf-dump-key>essay_content</span>\" => \"\"\"\n                    <span class=sf-dump-str title=\"1184 characters\">International news is good for student but sometime it is not nessessary. Many people think student in secondary school need to study about international news, other think this waste their time. My essay will write about both of these thing.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">Firstly, International news is good because it make student smart. When student learn international news they can know many things. Example, student can know weather in other country, famous people life, or footbal match. They will very interesting about these topic and can relax in the classroom. Learning international news is also help student speaking english well.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">However, I thinking international news sometime is boring. Student have many important subject, example maths, literature, or chemistry. These subjects can help student become doctor or engineer in future. International news not really important, only waste of time.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">In my opinion, secondary student should play sport and learn math better than learn international news. Teacher should teaching student good subject not international news.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">I conclusion, international news have advantage but more disadvantage. Student no need study this subject, waste of their time.</span>\n                    \"\"\"\n                  \"<span class=sf-dump-key>task_type</span>\" => \"<span class=sf-dump-str title=\"5 characters\">task2</span>\"\n                  \"<span class=sf-dump-key>time_limit</span>\" => <span class=sf-dump-num>40</span>\n                  \"<span class=sf-dump-key>overall_band_score</span>\" => \"<span class=sf-dump-str title=\"3 characters\">3.9</span>\"\n                  \"<span class=sf-dump-key>task_achievement</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.2</span>\"\n                  \"<span class=sf-dump-key>coherence_cohesion</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.0</span>\"\n                  \"<span class=sf-dump-key>lexical_resource</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.1</span>\"\n                  \"<span class=sf-dump-key>grammar_accuracy</span>\" => \"<span class=sf-dump-str title=\"3 characters\">3.8</span>\"\n                  \"<span class=sf-dump-key>detailed_feedback</span>\" => \"<span class=sf-dump-str title=\"210 characters\">{&quot;task_achievement&quot;: {&quot;score&quot;: 3.9, &quot;issues&quot;: [&quot;&#272;&#226;y l&#224; ph&#7843;n h&#7891;i demo&quot;], &quot;feedback&quot;: &quot;H&#7879; th&#7889;ng &#273;ang trong ch&#7871; &#273;&#7897; demo. &#272;&#226;y l&#224; k&#7871;t qu&#7843; m&#7851;u.&quot;, &quot;improvements&quot;: [&quot;Vui l&#242;ng th&#7917; l&#7841;i sau khi h&#7879; th&#7889;ng &#273;&#432;&#7907;c kh&#244;i ph&#7909;c&quot;]}}</span>\"\n                  \"<span class=sf-dump-key>highlighted_corrections</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n                  \"<span class=sf-dump-key>criteria_analysis</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n                  \"<span class=sf-dump-key>statistics</span>\" => \"<span class=sf-dump-str title=\"152 characters\">{&quot;total_errors&quot;: 8, &quot;grammar_errors&quot;: 3, &quot;spelling_errors&quot;: 5, &quot;complexity_level&quot;: &quot;intermediate&quot;, &quot;sentence_variety&quot;: &quot;medium&quot;, &quot;vocabulary_errors&quot;: 2}</span>\"\n                  \"<span class=sf-dump-key>word_count</span>\" => <span class=sf-dump-num>179</span>\n                  \"<span class=sf-dump-key>essay_length</span>\" => <span class=sf-dump-num>1184</span>\n                  \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"9 characters\">completed</span>\"\n                  \"<span class=sf-dump-key>error_message</span>\" => <span class=sf-dump-const>null</span>\n                  \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-18 14:46:07</span>\"\n                  \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-18 14:46:11</span>\"\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:21</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>2</span>\n                  \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-num>1</span>\n                  \"<span class=sf-dump-key>essay_question_id</span>\" => <span class=sf-dump-const>null</span>\n                  \"<span class=sf-dump-key>essay_content</span>\" => \"\"\"\n                    <span class=sf-dump-str title=\"1184 characters\">International news is good for student but sometime it is not nessessary. Many people think student in secondary school need to study about international news, other think this waste their time. My essay will write about both of these thing.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">Firstly, International news is good because it make student smart. When student learn international news they can know many things. Example, student can know weather in other country, famous people life, or footbal match. They will very interesting about these topic and can relax in the classroom. Learning international news is also help student speaking english well.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">However, I thinking international news sometime is boring. Student have many important subject, example maths, literature, or chemistry. These subjects can help student become doctor or engineer in future. International news not really important, only waste of time.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">In my opinion, secondary student should play sport and learn math better than learn international news. Teacher should teaching student good subject not international news.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">I conclusion, international news have advantage but more disadvantage. Student no need study this subject, waste of their time.</span>\n                    \"\"\"\n                  \"<span class=sf-dump-key>task_type</span>\" => \"<span class=sf-dump-str title=\"5 characters\">task2</span>\"\n                  \"<span class=sf-dump-key>time_limit</span>\" => <span class=sf-dump-num>40</span>\n                  \"<span class=sf-dump-key>overall_band_score</span>\" => \"<span class=sf-dump-str title=\"3 characters\">3.9</span>\"\n                  \"<span class=sf-dump-key>task_achievement</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.2</span>\"\n                  \"<span class=sf-dump-key>coherence_cohesion</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.0</span>\"\n                  \"<span class=sf-dump-key>lexical_resource</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.1</span>\"\n                  \"<span class=sf-dump-key>grammar_accuracy</span>\" => \"<span class=sf-dump-str title=\"3 characters\">3.8</span>\"\n                  \"<span class=sf-dump-key>detailed_feedback</span>\" => \"<span class=sf-dump-str title=\"210 characters\">{&quot;task_achievement&quot;: {&quot;score&quot;: 3.9, &quot;issues&quot;: [&quot;&#272;&#226;y l&#224; ph&#7843;n h&#7891;i demo&quot;], &quot;feedback&quot;: &quot;H&#7879; th&#7889;ng &#273;ang trong ch&#7871; &#273;&#7897; demo. &#272;&#226;y l&#224; k&#7871;t qu&#7843; m&#7851;u.&quot;, &quot;improvements&quot;: [&quot;Vui l&#242;ng th&#7917; l&#7841;i sau khi h&#7879; th&#7889;ng &#273;&#432;&#7907;c kh&#244;i ph&#7909;c&quot;]}}</span>\"\n                  \"<span class=sf-dump-key>highlighted_corrections</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n                  \"<span class=sf-dump-key>criteria_analysis</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n                  \"<span class=sf-dump-key>statistics</span>\" => \"<span class=sf-dump-str title=\"152 characters\">{&quot;total_errors&quot;: 8, &quot;grammar_errors&quot;: 3, &quot;spelling_errors&quot;: 5, &quot;complexity_level&quot;: &quot;intermediate&quot;, &quot;sentence_variety&quot;: &quot;medium&quot;, &quot;vocabulary_errors&quot;: 2}</span>\"\n                  \"<span class=sf-dump-key>word_count</span>\" => <span class=sf-dump-num>179</span>\n                  \"<span class=sf-dump-key>essay_length</span>\" => <span class=sf-dump-num>1184</span>\n                  \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"9 characters\">completed</span>\"\n                  \"<span class=sf-dump-key>error_message</span>\" => <span class=sf-dump-const>null</span>\n                  \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-18 14:46:07</span>\"\n                  \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-18 14:46:11</span>\"\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">previous</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:9</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>detailed_feedback</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                  \"<span class=sf-dump-key>highlighted_corrections</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                  \"<span class=sf-dump-key>criteria_analysis</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                  \"<span class=sf-dump-key>statistics</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                  \"<span class=sf-dump-key>overall_band_score</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:1</span>\"\n                  \"<span class=sf-dump-key>task_achievement</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:1</span>\"\n                  \"<span class=sf-dump-key>coherence_cohesion</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:1</span>\"\n                  \"<span class=sf-dump-key>lexical_resource</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:1</span>\"\n                  \"<span class=sf-dump-key>grammar_accuracy</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:1</span>\"\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>essayQuestion</span>\" => <span class=sf-dump-const>null</span>\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadCallback</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadContext</span>: <span class=sf-dump-const>null</span>\n                +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:18</span> [<samp data-depth=9 class=sf-dump-compact>\n                  <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">user_id</span>\"\n                  <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"17 characters\">essay_question_id</span>\"\n                  <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"13 characters\">essay_content</span>\"\n                  <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"9 characters\">task_type</span>\"\n                  <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"10 characters\">time_limit</span>\"\n                  <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"18 characters\">overall_band_score</span>\"\n                  <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"16 characters\">task_achievement</span>\"\n                  <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"18 characters\">coherence_cohesion</span>\"\n                  <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"16 characters\">lexical_resource</span>\"\n                  <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"16 characters\">grammar_accuracy</span>\"\n                  <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"17 characters\">detailed_feedback</span>\"\n                  <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"23 characters\">highlighted_corrections</span>\"\n                  <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"17 characters\">criteria_analysis</span>\"\n                  <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"10 characters\">statistics</span>\"\n                  <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"10 characters\">word_count</span>\"\n                  <span class=sf-dump-index>15</span> => \"<span class=sf-dump-str title=\"12 characters\">essay_length</span>\"\n                  <span class=sf-dump-index>16</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n                  <span class=sf-dump-index>17</span> => \"<span class=sf-dump-str title=\"13 characters\">error_message</span>\"\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=9 class=sf-dump-compact>\n                  <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n                </samp>]\n              </samp>}\n              <span class=sf-dump-index>4</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\ScoringAttempt\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ScoringAttempt</span></span> {<a class=sf-dump-ref>#1485</a><samp data-depth=8 class=sf-dump-compact>\n                #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"16 characters\">scoring_attempts</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:21</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n                  \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-num>1</span>\n                  \"<span class=sf-dump-key>essay_question_id</span>\" => <span class=sf-dump-const>null</span>\n                  \"<span class=sf-dump-key>essay_content</span>\" => \"\"\"\n                    <span class=sf-dump-str title=\"1184 characters\">International news is good for student but sometime it is not nessessary. Many people think student in secondary school need to study about international news, other think this waste their time. My essay will write about both of these thing.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">Firstly, International news is good because it make student smart. When student learn international news they can know many things. Example, student can know weather in other country, famous people life, or footbal match. They will very interesting about these topic and can relax in the classroom. Learning international news is also help student speaking english well.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">However, I thinking international news sometime is boring. Student have many important subject, example maths, literature, or chemistry. These subjects can help student become doctor or engineer in future. International news not really important, only waste of time.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">In my opinion, secondary student should play sport and learn math better than learn international news. Teacher should teaching student good subject not international news.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">I conclusion, international news have advantage but more disadvantage. Student no need study this subject, waste of their time.</span>\n                    \"\"\"\n                  \"<span class=sf-dump-key>task_type</span>\" => \"<span class=sf-dump-str title=\"5 characters\">task2</span>\"\n                  \"<span class=sf-dump-key>time_limit</span>\" => <span class=sf-dump-num>40</span>\n                  \"<span class=sf-dump-key>overall_band_score</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.7</span>\"\n                  \"<span class=sf-dump-key>task_achievement</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.4</span>\"\n                  \"<span class=sf-dump-key>coherence_cohesion</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.5</span>\"\n                  \"<span class=sf-dump-key>lexical_resource</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.8</span>\"\n                  \"<span class=sf-dump-key>grammar_accuracy</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.4</span>\"\n                  \"<span class=sf-dump-key>detailed_feedback</span>\" => \"<span class=sf-dump-str title=\"210 characters\">{&quot;task_achievement&quot;: {&quot;score&quot;: 4.7, &quot;issues&quot;: [&quot;&#272;&#226;y l&#224; ph&#7843;n h&#7891;i demo&quot;], &quot;feedback&quot;: &quot;H&#7879; th&#7889;ng &#273;ang trong ch&#7871; &#273;&#7897; demo. &#272;&#226;y l&#224; k&#7871;t qu&#7843; m&#7851;u.&quot;, &quot;improvements&quot;: [&quot;Vui l&#242;ng th&#7917; l&#7841;i sau khi h&#7879; th&#7889;ng &#273;&#432;&#7907;c kh&#244;i ph&#7909;c&quot;]}}</span>\"\n                  \"<span class=sf-dump-key>highlighted_corrections</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n                  \"<span class=sf-dump-key>criteria_analysis</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n                  \"<span class=sf-dump-key>statistics</span>\" => \"<span class=sf-dump-str title=\"153 characters\">{&quot;total_errors&quot;: 12, &quot;grammar_errors&quot;: 7, &quot;spelling_errors&quot;: 4, &quot;complexity_level&quot;: &quot;intermediate&quot;, &quot;sentence_variety&quot;: &quot;medium&quot;, &quot;vocabulary_errors&quot;: 2}</span>\"\n                  \"<span class=sf-dump-key>word_count</span>\" => <span class=sf-dump-num>179</span>\n                  \"<span class=sf-dump-key>essay_length</span>\" => <span class=sf-dump-num>1184</span>\n                  \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"9 characters\">completed</span>\"\n                  \"<span class=sf-dump-key>error_message</span>\" => <span class=sf-dump-const>null</span>\n                  \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-18 14:44:34</span>\"\n                  \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-18 14:44:38</span>\"\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:21</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n                  \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-num>1</span>\n                  \"<span class=sf-dump-key>essay_question_id</span>\" => <span class=sf-dump-const>null</span>\n                  \"<span class=sf-dump-key>essay_content</span>\" => \"\"\"\n                    <span class=sf-dump-str title=\"1184 characters\">International news is good for student but sometime it is not nessessary. Many people think student in secondary school need to study about international news, other think this waste their time. My essay will write about both of these thing.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">Firstly, International news is good because it make student smart. When student learn international news they can know many things. Example, student can know weather in other country, famous people life, or footbal match. They will very interesting about these topic and can relax in the classroom. Learning international news is also help student speaking english well.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">However, I thinking international news sometime is boring. Student have many important subject, example maths, literature, or chemistry. These subjects can help student become doctor or engineer in future. International news not really important, only waste of time.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">In my opinion, secondary student should play sport and learn math better than learn international news. Teacher should teaching student good subject not international news.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">I conclusion, international news have advantage but more disadvantage. Student no need study this subject, waste of their time.</span>\n                    \"\"\"\n                  \"<span class=sf-dump-key>task_type</span>\" => \"<span class=sf-dump-str title=\"5 characters\">task2</span>\"\n                  \"<span class=sf-dump-key>time_limit</span>\" => <span class=sf-dump-num>40</span>\n                  \"<span class=sf-dump-key>overall_band_score</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.7</span>\"\n                  \"<span class=sf-dump-key>task_achievement</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.4</span>\"\n                  \"<span class=sf-dump-key>coherence_cohesion</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.5</span>\"\n                  \"<span class=sf-dump-key>lexical_resource</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.8</span>\"\n                  \"<span class=sf-dump-key>grammar_accuracy</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.4</span>\"\n                  \"<span class=sf-dump-key>detailed_feedback</span>\" => \"<span class=sf-dump-str title=\"210 characters\">{&quot;task_achievement&quot;: {&quot;score&quot;: 4.7, &quot;issues&quot;: [&quot;&#272;&#226;y l&#224; ph&#7843;n h&#7891;i demo&quot;], &quot;feedback&quot;: &quot;H&#7879; th&#7889;ng &#273;ang trong ch&#7871; &#273;&#7897; demo. &#272;&#226;y l&#224; k&#7871;t qu&#7843; m&#7851;u.&quot;, &quot;improvements&quot;: [&quot;Vui l&#242;ng th&#7917; l&#7841;i sau khi h&#7879; th&#7889;ng &#273;&#432;&#7907;c kh&#244;i ph&#7909;c&quot;]}}</span>\"\n                  \"<span class=sf-dump-key>highlighted_corrections</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n                  \"<span class=sf-dump-key>criteria_analysis</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n                  \"<span class=sf-dump-key>statistics</span>\" => \"<span class=sf-dump-str title=\"153 characters\">{&quot;total_errors&quot;: 12, &quot;grammar_errors&quot;: 7, &quot;spelling_errors&quot;: 4, &quot;complexity_level&quot;: &quot;intermediate&quot;, &quot;sentence_variety&quot;: &quot;medium&quot;, &quot;vocabulary_errors&quot;: 2}</span>\"\n                  \"<span class=sf-dump-key>word_count</span>\" => <span class=sf-dump-num>179</span>\n                  \"<span class=sf-dump-key>essay_length</span>\" => <span class=sf-dump-num>1184</span>\n                  \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"9 characters\">completed</span>\"\n                  \"<span class=sf-dump-key>error_message</span>\" => <span class=sf-dump-const>null</span>\n                  \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-18 14:44:34</span>\"\n                  \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-18 14:44:38</span>\"\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">previous</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:9</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>detailed_feedback</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                  \"<span class=sf-dump-key>highlighted_corrections</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                  \"<span class=sf-dump-key>criteria_analysis</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                  \"<span class=sf-dump-key>statistics</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                  \"<span class=sf-dump-key>overall_band_score</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:1</span>\"\n                  \"<span class=sf-dump-key>task_achievement</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:1</span>\"\n                  \"<span class=sf-dump-key>coherence_cohesion</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:1</span>\"\n                  \"<span class=sf-dump-key>lexical_resource</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:1</span>\"\n                  \"<span class=sf-dump-key>grammar_accuracy</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:1</span>\"\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>essayQuestion</span>\" => <span class=sf-dump-const>null</span>\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadCallback</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadContext</span>: <span class=sf-dump-const>null</span>\n                +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:18</span> [<samp data-depth=9 class=sf-dump-compact>\n                  <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">user_id</span>\"\n                  <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"17 characters\">essay_question_id</span>\"\n                  <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"13 characters\">essay_content</span>\"\n                  <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"9 characters\">task_type</span>\"\n                  <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"10 characters\">time_limit</span>\"\n                  <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"18 characters\">overall_band_score</span>\"\n                  <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"16 characters\">task_achievement</span>\"\n                  <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"18 characters\">coherence_cohesion</span>\"\n                  <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"16 characters\">lexical_resource</span>\"\n                  <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"16 characters\">grammar_accuracy</span>\"\n                  <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"17 characters\">detailed_feedback</span>\"\n                  <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"23 characters\">highlighted_corrections</span>\"\n                  <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"17 characters\">criteria_analysis</span>\"\n                  <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"10 characters\">statistics</span>\"\n                  <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"10 characters\">word_count</span>\"\n                  <span class=sf-dump-index>15</span> => \"<span class=sf-dump-str title=\"12 characters\">essay_length</span>\"\n                  <span class=sf-dump-index>16</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n                  <span class=sf-dump-index>17</span> => \"<span class=sf-dump-str title=\"13 characters\">error_message</span>\"\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=9 class=sf-dump-compact>\n                  <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n                </samp>]\n              </samp>}\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n          </samp>}\n          #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>10</span>\n          #<span class=sf-dump-protected title=\"Protected property\">currentPage</span>: <span class=sf-dump-num>1</span>\n          #<span class=sf-dump-protected title=\"Protected property\">path</span>: \"<span class=sf-dump-str title=\"37 characters\">http://127.0.0.1:8000/scoring/history</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">query</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">fragment</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">pageName</span>: \"<span class=sf-dump-str title=\"4 characters\">page</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n          +<span class=sf-dump-public title=\"Public property\">onEachSide</span>: <span class=sf-dump-num>3</span>\n          #<span class=sf-dump-protected title=\"Protected property\">options</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://127.0.0.1:8000/scoring/history</span>\"\n            \"<span class=sf-dump-key>pageName</span>\" => \"<span class=sf-dump-str title=\"4 characters\">page</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">total</span>: <span class=sf-dump-num>5</span>\n          #<span class=sf-dump-protected title=\"Protected property\">lastPage</span>: <span class=sf-dump-num>1</span>\n        </samp>}\n      </samp>]\n      <span class=sf-dump-index>2</span> => []\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"42 characters\">app/Http/Controllers/ScoringController.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>238</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">view</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">scoring.history</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>attempts</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Pagination\\LengthAwarePaginator\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Pagination</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">LengthAwarePaginator</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref21484 title=\"2 occurrences\">#1484</a>}\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"62 characters\">vendor/laravel/framework/src/Illuminate/Routing/Controller.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>54</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"7 characters\">history</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"38 characters\">App\\Http\\Controllers\\ScoringController</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>43</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callAction</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Routing\\Controller</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">history</span>\"\n      <span class=sf-dump-index>1</span> => []\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"57 characters\">vendor/laravel/framework/src/Illuminate/Routing/Route.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>265</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">dispatch</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Routing\\ControllerDispatcher</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"47 characters\">[object App\\Http\\Controllers\\ScoringController]</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"7 characters\">history</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"57 characters\">vendor/laravel/framework/src/Illuminate/Routing/Route.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>211</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"13 characters\">runController</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Illuminate\\Routing\\Route</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>808</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"3 characters\">run</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Illuminate\\Routing\\Route</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>169</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Routing\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>66</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>11</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Barryvdh\\Debugbar\\Middleware\\InjectDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>12</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"43 characters\">app/Http/Middleware/EnsureUserHasCredit.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>32</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>13</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">App\\Http\\Middleware\\EnsureUserHasCredit</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>14</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"81 characters\">vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>50</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>15</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"48 characters\">Illuminate\\Routing\\Middleware\\SubstituteBindings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>16</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>63</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>17</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Auth\\Middleware\\Authenticate</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>18</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"86 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>87</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>19</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"53 characters\">Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>20</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>48</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>21</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\View\\Middleware\\ShareErrorsFromSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>22</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"75 characters\">vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>120</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>23</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"75 characters\">vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>63</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"21 characters\">handleStatefulRequest</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Illuminate\\Session\\Middleware\\StartSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Session\\Store]</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>24</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Illuminate\\Session\\Middleware\\StartSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>25</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>36</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>26</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>27</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"76 characters\">vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>74</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>28</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Illuminate\\Cookie\\Middleware\\EncryptCookies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>29</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>126</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>30</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>807</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>31</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>786</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"19 characters\">runRouteWithinStack</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>32</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>750</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">runRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>33</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>739</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"15 characters\">dispatchToRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>34</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>200</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">dispatch</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>35</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>169</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Illuminate\\Foundation\\Http\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>36</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>66</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>37</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Barryvdh\\Debugbar\\Middleware\\InjectDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>38</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>39</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"96 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>31</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>40</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"63 characters\">Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>41</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>42</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>51</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>43</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\Foundation\\Http\\Middleware\\TrimStrings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>44</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"76 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>27</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>45</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Illuminate\\Http\\Middleware\\ValidatePostSize</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>46</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"103 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>109</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>47</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"70 characters\">Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>48</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"70 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>48</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>49</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"37 characters\">Illuminate\\Http\\Middleware\\HandleCors</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>50</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>58</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>51</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Http\\Middleware\\TrustProxies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>52</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"94 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>22</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>53</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"61 characters\">Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>54</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"80 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePathEncoding.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>26</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>55</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"47 characters\">Illuminate\\Http\\Middleware\\ValidatePathEncoding</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>56</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>126</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>57</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>175</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>58</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>144</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"24 characters\">sendRequestThroughRouter</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>59</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Application.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>1219</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>60</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"16 characters\">public/index.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>20</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"13 characters\">handleRequest</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Application</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>61</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"71 characters\">vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>23</span>\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"58 characters\">C:\\laragon\\www\\ielts\\ielts-scoring-system\\public\\index.php</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"12 characters\">require_once</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "surrounding_lines": ["            }\n", "        }\n", "\n", "        throw new InvalidArgumentException(\"View [{$name}] not found.\");\n", "    }\n", "\n", "    /**\n"], "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FView%2FFileViewFinder.php&line=138", "ajax": false, "filename": "FileViewFinder.php", "line": "138"}}]}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.20.0", "PHP Version": "8.2.27", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost", "Timezone": "Asia/Ho_Chi_Minh", "Locale": "en"}}, "views": {"count": 22, "nb_templates": 22, "templates": [{"name": "laravel-exceptions-renderer::show", "param_count": null, "params": [], "start": **********.052542, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/show.blade.phplaravel-exceptions-renderer::show", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fshow.blade.php&line=1", "ajax": false, "filename": "show.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.navigation", "param_count": null, "params": [], "start": **********.054092, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/navigation.blade.phplaravel-exceptions-renderer::components.navigation", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Fnavigation.blade.php&line=1", "ajax": false, "filename": "navigation.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.theme-switcher", "param_count": null, "params": [], "start": **********.05445, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/theme-switcher.blade.phplaravel-exceptions-renderer::components.theme-switcher", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ftheme-switcher.blade.php&line=1", "ajax": false, "filename": "theme-switcher.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.icons.sun", "param_count": null, "params": [], "start": **********.054876, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/icons/sun.blade.phplaravel-exceptions-renderer::components.icons.sun", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ficons%2Fsun.blade.php&line=1", "ajax": false, "filename": "sun.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.icons.moon", "param_count": null, "params": [], "start": **********.055174, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/icons/moon.blade.phplaravel-exceptions-renderer::components.icons.moon", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ficons%2Fmoon.blade.php&line=1", "ajax": false, "filename": "moon.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.icons.sun", "param_count": null, "params": [], "start": **********.055425, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/icons/sun.blade.phplaravel-exceptions-renderer::components.icons.sun", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ficons%2Fsun.blade.php&line=1", "ajax": false, "filename": "sun.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.icons.moon", "param_count": null, "params": [], "start": **********.055623, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/icons/moon.blade.phplaravel-exceptions-renderer::components.icons.moon", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ficons%2Fmoon.blade.php&line=1", "ajax": false, "filename": "moon.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.icons.computer-desktop", "param_count": null, "params": [], "start": **********.055857, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/icons/computer-desktop.blade.phplaravel-exceptions-renderer::components.icons.computer-desktop", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ficons%2Fcomputer-desktop.blade.php&line=1", "ajax": false, "filename": "computer-desktop.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.header", "param_count": null, "params": [], "start": **********.056149, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/header.blade.phplaravel-exceptions-renderer::components.header", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.card", "param_count": null, "params": [], "start": **********.056568, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/card.blade.phplaravel-exceptions-renderer::components.card", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Fcard.blade.php&line=1", "ajax": false, "filename": "card.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.trace-and-editor", "param_count": null, "params": [], "start": **********.056939, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/trace-and-editor.blade.phplaravel-exceptions-renderer::components.trace-and-editor", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ftrace-and-editor.blade.php&line=1", "ajax": false, "filename": "trace-and-editor.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.trace", "param_count": null, "params": [], "start": **********.066901, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/trace.blade.phplaravel-exceptions-renderer::components.trace", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ftrace.blade.php&line=1", "ajax": false, "filename": "trace.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.icons.chevron-down", "param_count": null, "params": [], "start": **********.067423, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/icons/chevron-down.blade.phplaravel-exceptions-renderer::components.icons.chevron-down", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ficons%2Fchevron-down.blade.php&line=1", "ajax": false, "filename": "chevron-down.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.icons.chevron-up", "param_count": null, "params": [], "start": **********.067726, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/icons/chevron-up.blade.phplaravel-exceptions-renderer::components.icons.chevron-up", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ficons%2Fchevron-up.blade.php&line=1", "ajax": false, "filename": "chevron-up.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.icons.chevron-up", "param_count": null, "params": [], "start": **********.067968, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/icons/chevron-up.blade.phplaravel-exceptions-renderer::components.icons.chevron-up", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ficons%2Fchevron-up.blade.php&line=1", "ajax": false, "filename": "chevron-up.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.icons.chevron-down", "param_count": null, "params": [], "start": **********.068121, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/icons/chevron-down.blade.phplaravel-exceptions-renderer::components.icons.chevron-down", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ficons%2Fchevron-down.blade.php&line=1", "ajax": false, "filename": "chevron-down.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.editor", "param_count": null, "params": [], "start": **********.08603, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/editor.blade.phplaravel-exceptions-renderer::components.editor", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Feditor.blade.php&line=1", "ajax": false, "filename": "editor.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.card", "param_count": null, "params": [], "start": **********.124113, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/card.blade.phplaravel-exceptions-renderer::components.card", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Fcard.blade.php&line=1", "ajax": false, "filename": "card.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.context", "param_count": null, "params": [], "start": **********.124496, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/context.blade.phplaravel-exceptions-renderer::components.context", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Fcontext.blade.php&line=1", "ajax": false, "filename": "context.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.card", "param_count": null, "params": [], "start": **********.125036, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/card.blade.phplaravel-exceptions-renderer::components.card", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Fcard.blade.php&line=1", "ajax": false, "filename": "card.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.card", "param_count": null, "params": [], "start": **********.125336, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/card.blade.phplaravel-exceptions-renderer::components.card", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Fcard.blade.php&line=1", "ajax": false, "filename": "card.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.layout", "param_count": null, "params": [], "start": **********.125561, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/layout.blade.phplaravel-exceptions-renderer::components.layout", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Flayout.blade.php&line=1", "ajax": false, "filename": "layout.blade.php", "line": "?"}}]}, "queries": {"count": 6, "nb_statements": 5, "nb_visible_statements": 6, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.026439999999999998, "accumulated_duration_str": "26.44ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 158}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 56}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}], "start": **********.889903, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "ietls", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `sessions` where `id` = 'TGbnOTYIBZUzNJj0xv9nDBkjG9tZftJvLifPfv1e' limit 1", "type": "query", "params": [], "bindings": ["TGbnOTYIBZUzNJj0xv9nDBkjG9tZftJvLifPfv1e"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.8938699, "duration": 0.02431, "duration_str": "24.31ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "ietls", "explain": null, "start_percent": 0, "width_percent": 91.944}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 82}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.926023, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "ietls", "explain": null, "start_percent": 91.944, "width_percent": 1.929}, {"sql": "select * from `user_credits` where `user_credits`.`user_id` = 1 and `user_credits`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "middleware", "name": "ensure.credit", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\app\\Http\\Middleware\\EnsureUserHasCredit.php", "line": 22}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}], "start": **********.931733, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "ensure.credit:22", "source": {"index": 21, "namespace": "middleware", "name": "ensure.credit", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\app\\Http\\Middleware\\EnsureUserHasCredit.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fapp%2FHttp%2FMiddleware%2FEnsureUserHasCredit.php&line=22", "ajax": false, "filename": "EnsureUserHasCredit.php", "line": "22"}, "connection": "ietls", "explain": null, "start_percent": 93.873, "width_percent": 2.231}, {"sql": "select count(*) as aggregate from `scoring_attempts` where `user_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ScoringController.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\app\\Http\\Controllers\\ScoringController.php", "line": 236}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.9338932, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "ScoringController.php:236", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/ScoringController.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\app\\Http\\Controllers\\ScoringController.php", "line": 236}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fapp%2FHttp%2FControllers%2FScoringController.php&line=236", "ajax": false, "filename": "ScoringController.php", "line": "236"}, "connection": "ietls", "explain": null, "start_percent": 96.104, "width_percent": 1.74}, {"sql": "select * from `scoring_attempts` where `user_id` = 1 order by `created_at` desc limit 10 offset 0", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ScoringController.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\app\\Http\\Controllers\\ScoringController.php", "line": 236}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.935329, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "ScoringController.php:236", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/ScoringController.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\app\\Http\\Controllers\\ScoringController.php", "line": 236}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fapp%2FHttp%2FControllers%2FScoringController.php&line=236", "ajax": false, "filename": "ScoringController.php", "line": "236"}, "connection": "ietls", "explain": null, "start_percent": 97.844, "width_percent": 2.156}]}, "models": {"data": {"App\\Models\\ScoringAttempt": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fapp%2FModels%2FScoringAttempt.php&line=1", "ajax": false, "filename": "ScoringAttempt.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\UserCredit": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fapp%2FModels%2FUserCredit.php&line=1", "ajax": false, "filename": "UserCredit.php", "line": "?"}}}, "count": 7, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "500 Internal Server Error", "full_url": "http://127.0.0.1:8000/scoring/history", "action_name": "scoring.history", "controller_action": "App\\Http\\Controllers\\ScoringController@history", "uri": "GET scoring/history", "controller": "App\\Http\\Controllers\\ScoringController@history<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fapp%2FHttp%2FControllers%2FScoringController.php&line=231\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "/scoring", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fapp%2FHttp%2FControllers%2FScoringController.php&line=231\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/ScoringController.php:231-239</a>", "middleware": "web, auth", "duration": "411ms", "peak_memory": "28MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1708233550 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1708233550\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-791196123 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-791196123\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://127.0.0.1:8000/scoring/history</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,vi;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1636 characters\">laravel_session=eyJpdiI6ImJ0YWE2SlNlVGxuMjBoeFlMc29JOXc9PSIsInZhbHVlIjoiVDNHR0hmKzlzZGZOZUFJYmJGYnBXRHJIdnpGYXdBMXZwL1dQbk1uLzNnU2w0Z2NqdVp1RGlPbjBheDd5dXlQUzNZbkdnMHYzL0lsajF5d2FldllyL2hzUXJ3Qjdjc0xOUElDOGVTcWZKOGJZUjdZSW5sNjFlek5GSGNveU4xRFIiLCJtYWMiOiI5MTI2NDA0NzFlYmQ3NGVjNmY0NzljZTVmMDAxOTVmZGVhNDhjMTJiNWJjNWZiNzczNmE2YjU4Y2QzNDUzMDQ4IiwidGFnIjoiIn0%3D; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Imw5QVM5cFVDbmZqVFBYdXlBK1RMdmc9PSIsInZhbHVlIjoiVzhoc2pFTmlSUm9iNUtmQk82eVR4UDJRRVI5QWt6T3oxQTM2ZFgrZ2cyZ1FXOEVPSlhQYTE1NXF5eVNHaFdJNDF5ZlBvL0wvS053L081YjdxTnBaV0RGSnBPQTJ1Q0JybVR3aUV4dmlkaGFiR3RyWlFNajhXeGtpSVdCSEowd0lSNjBGQlR0UVpoaG84M2pTekJNQlBhWmhJODIxRVFGb1E1VFlUTjZYLy92aDJLTG96ODlRSWhSODJ5WmJCZHJJWTB3VWVydXAybWEyQW45OWF3MzI1dEdDU1c5TEpxaG5NVnR2dXhiRU1UVT0iLCJtYWMiOiI3OWFjMDU0MDBkNDdhNjc2ODNjYmQ5ODRiZmRiZWIwNTdmZjE4NTNhYjM5YzljYWEyY2M2MzkwZGU2ZmYyMWMxIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Im05VnhXNDRRYWJNeVhwdWNEWkxSM3c9PSIsInZhbHVlIjoidjhVNTYvalE1T1ZDMElnWFZTbFF3TWVDUXhZQ0phd21Zd2Vydm1jcVJiTjcvM1hVZ25VYjRMWjBzcEZ6L1ZSTUNNaXJOamFBQ05ER0U1OXdxZXRjS2E5NWpvd1prYURBM0NmaDJUR3RHODJvdE9HZ2hqUjV0VlZ6bEtTWlZHV28iLCJtYWMiOiIzOWMyNTNkYzdhYThiOTJlNGNmNjgzN2RiYWE0OWNiNDUwZTI0NTgwYmU3NDdmZTNhM2M5YjdmOTlmMWFmMWI4IiwidGFnIjoiIn0%3D; i_e_l_t_s_a_i_scoring_system_session=eyJpdiI6IkZZZHVETlhPbVdrbzRxNW9ZMnlCenc9PSIsInZhbHVlIjoiTFA4cVZ1d3pUR0ZiNjlCajkrZWpqem1GeURobldXVTNqNkNIc1Y2Q1pzcWdwZUJWSDh3dU9veFBrcXhIVFRjbTZpdmJRZDQ5ZnpIVDlsRExVSEQ1RnZtZjJMSkJ2RCt1OWY3ZzdEM284bjlZcmVxQ1k2YjRKeWxXZ3NiaGFLUHIiLCJtYWMiOiJmOTFmMjJiZDBhOWI1ZDYyN2I5MmQyMGM0YmQzMjdhNGY4MjM3NzE3NzRiZWY3ZWJmZDNlNmZjYjE3ZGMxMzM1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>laravel_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|3wccpylwRhXpahJKUyCq5OB1acJKoA1uVT8wR590398beZmetuIix9Oz31TE|$2y$12$w9Xe5C9oBSghC7wjYhaL8e4NH7n0qFNzXvZwq4eFPiPRNhYyYy9zq</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PcVgzRJ2u4aLQUh3Wf0zWNjBL5JJ2KyXi8PQjBGb</span>\"\n  \"<span class=sf-dump-key>i_e_l_t_s_a_i_scoring_system_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TGbnOTYIBZUzNJj0xv9nDBkjG9tZftJvLifPfv1e</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1893751851 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 18 Jul 2025 17:26:52 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1893751851\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1310474723 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PcVgzRJ2u4aLQUh3Wf0zWNjBL5JJ2KyXi8PQjBGb</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://127.0.0.1:8000/scoring/history</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1752849859</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1310474723\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "500 Internal Server Error", "full_url": "http://127.0.0.1:8000/scoring/history", "action_name": "scoring.history", "controller_action": "App\\Http\\Controllers\\ScoringController@history"}, "badge": "500 Internal Server Error"}}