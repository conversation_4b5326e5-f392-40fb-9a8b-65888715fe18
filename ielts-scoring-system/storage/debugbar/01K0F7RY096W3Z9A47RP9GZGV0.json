{"__meta": {"id": "01K0F7RY096W3Z9A47RP9GZGV0", "datetime": "2025-07-18 17:02:10", "utime": **********.44251, "method": "GET", "uri": "/scoring/show/1", "ip": "127.0.0.1"}, "messages": {"count": 1, "messages": [{"message": "[17:02:10] LOG.info: IELTSScorer initialized {\n    \"api_url\": \"https:\\/\\/api.v3.cm\\/v1\\/chat\\/completions\",\n    \"model\": \"o3\",\n    \"api_key_length\": 51\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.372696, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.179793, "end": **********.442535, "duration": 0.2627420425415039, "duration_str": "263ms", "measures": [{"label": "Booting", "start": **********.179793, "relative_start": 0, "end": **********.34581, "relative_end": **********.34581, "duration": 0.****************, "duration_str": "166ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.345821, "relative_start": 0.*****************, "end": **********.442538, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "96.72ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.367056, "relative_start": 0.*****************, "end": **********.369596, "relative_end": **********.369596, "duration": 0.002540111541748047, "duration_str": "2.54ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.42426, "relative_start": 0.*****************, "end": **********.440198, "relative_end": **********.440198, "duration": 0.*****************, "duration_str": "15.94ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: scoring.show", "start": **********.42673, "relative_start": 0.*****************, "end": **********.42673, "relative_end": **********.42673, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: layouts.app", "start": **********.437805, "relative_start": 0.***************, "end": **********.437805, "relative_end": **********.437805, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 24953896, "peak_usage_str": "24MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.20.0", "PHP Version": "8.2.27", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 2, "nb_templates": 2, "templates": [{"name": "scoring.show", "param_count": null, "params": [], "start": **********.426695, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\resources\\views/scoring/show.blade.phpscoring.show", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fresources%2Fviews%2Fscoring%2Fshow.blade.php&line=1", "ajax": false, "filename": "show.blade.php", "line": "?"}}, {"name": "layouts.app", "param_count": null, "params": [], "start": **********.437771, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\resources\\views/layouts/app.blade.phplayouts.app", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fresources%2Fviews%2Flayouts%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}}]}, "queries": {"count": 5, "nb_statements": 4, "nb_visible_statements": 5, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.0038899999999999994, "accumulated_duration_str": "3.89ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 158}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 56}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}], "start": **********.387725, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "ietls", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `sessions` where `id` = 'TGbnOTYIBZUzNJj0xv9nDBkjG9tZftJvLifPfv1e' limit 1", "type": "query", "params": [], "bindings": ["TGbnOTYIBZUzNJj0xv9nDBkjG9tZftJvLifPfv1e"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.393731, "duration": 0.0023799999999999997, "duration_str": "2.38ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "ietls", "explain": null, "start_percent": 0, "width_percent": 61.183}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 82}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.406112, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "ietls", "explain": null, "start_percent": 61.183, "width_percent": 11.054}, {"sql": "select * from `scoring_attempts` where `id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ImplicitRouteBinding.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ImplicitRouteBinding.php", "line": 60}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 965}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 41}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 23, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}], "start": **********.4121141, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "ImplicitRouteBinding.php:60", "source": {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ImplicitRouteBinding.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ImplicitRouteBinding.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FRouting%2FImplicitRouteBinding.php&line=60", "ajax": false, "filename": "ImplicitRouteBinding.php", "line": "60"}, "connection": "ietls", "explain": null, "start_percent": 72.237, "width_percent": 16.967}, {"sql": "select * from `user_credits` where `user_credits`.`user_id` = 1 and `user_credits`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "middleware", "name": "ensure.credit", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\app\\Http\\Middleware\\EnsureUserHasCredit.php", "line": 22}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}], "start": **********.4166589, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "ensure.credit:22", "source": {"index": 21, "namespace": "middleware", "name": "ensure.credit", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\app\\Http\\Middleware\\EnsureUserHasCredit.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fapp%2FHttp%2FMiddleware%2FEnsureUserHasCredit.php&line=22", "ajax": false, "filename": "EnsureUserHasCredit.php", "line": "22"}, "connection": "ietls", "explain": null, "start_percent": 89.203, "width_percent": 10.797}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\ScoringAttempt": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fapp%2FModels%2FScoringAttempt.php&line=1", "ajax": false, "filename": "ScoringAttempt.php", "line": "?"}}, "App\\Models\\UserCredit": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fapp%2FModels%2FUserCredit.php&line=1", "ajax": false, "filename": "UserCredit.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/scoring/show/1", "action_name": "scoring.show", "controller_action": "App\\Http\\Controllers\\ScoringController@show", "uri": "GET scoring/show/{attempt}", "controller": "App\\Http\\Controllers\\ScoringController@show<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fapp%2FHttp%2FControllers%2FScoringController.php&line=186\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "/scoring", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fapp%2FHttp%2FControllers%2FScoringController.php&line=186\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/ScoringController.php:186-194</a>", "middleware": "web, auth", "duration": "264ms", "peak_memory": "26MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-376662517 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-376662517\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2010773541 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2010773541\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">http://127.0.0.1:8000/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,vi;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1636 characters\">laravel_session=eyJpdiI6ImJ0YWE2SlNlVGxuMjBoeFlMc29JOXc9PSIsInZhbHVlIjoiVDNHR0hmKzlzZGZOZUFJYmJGYnBXRHJIdnpGYXdBMXZwL1dQbk1uLzNnU2w0Z2NqdVp1RGlPbjBheDd5dXlQUzNZbkdnMHYzL0lsajF5d2FldllyL2hzUXJ3Qjdjc0xOUElDOGVTcWZKOGJZUjdZSW5sNjFlek5GSGNveU4xRFIiLCJtYWMiOiI5MTI2NDA0NzFlYmQ3NGVjNmY0NzljZTVmMDAxOTVmZGVhNDhjMTJiNWJjNWZiNzczNmE2YjU4Y2QzNDUzMDQ4IiwidGFnIjoiIn0%3D; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Imw5QVM5cFVDbmZqVFBYdXlBK1RMdmc9PSIsInZhbHVlIjoiVzhoc2pFTmlSUm9iNUtmQk82eVR4UDJRRVI5QWt6T3oxQTM2ZFgrZ2cyZ1FXOEVPSlhQYTE1NXF5eVNHaFdJNDF5ZlBvL0wvS053L081YjdxTnBaV0RGSnBPQTJ1Q0JybVR3aUV4dmlkaGFiR3RyWlFNajhXeGtpSVdCSEowd0lSNjBGQlR0UVpoaG84M2pTekJNQlBhWmhJODIxRVFGb1E1VFlUTjZYLy92aDJLTG96ODlRSWhSODJ5WmJCZHJJWTB3VWVydXAybWEyQW45OWF3MzI1dEdDU1c5TEpxaG5NVnR2dXhiRU1UVT0iLCJtYWMiOiI3OWFjMDU0MDBkNDdhNjc2ODNjYmQ5ODRiZmRiZWIwNTdmZjE4NTNhYjM5YzljYWEyY2M2MzkwZGU2ZmYyMWMxIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InBROTVsSkpZRUR4a1lwNFBnSW5XU3c9PSIsInZhbHVlIjoib3hqa2Q4ZGNhR3lXbVlVVjAvZzNsbExJRVV5OS94bjEyVXh0UzEyWUIrTnk4YkNueWRWai9BdEZBTzJwUTJyYjRHeWRucjVvckw5ZU16MmtjWGRIOUxYWjZSUGYxd0Ixc1htcThHSXRPZnU2WWhCelNnbVFBYVhFWmJWMzhNZGQiLCJtYWMiOiI3NWI1YzFhY2RiYTBjNjc4ZGUyMjc2NjAwZjNkMjUyYjkzMDkzMDNmNjkyOTk2YjJmNDBkODM0M2EwZmUyYjgxIiwidGFnIjoiIn0%3D; i_e_l_t_s_a_i_scoring_system_session=eyJpdiI6IlFQUk10OWFMcXV0YmNMekhicUxnVnc9PSIsInZhbHVlIjoiUFdsYXZ3dElIRSsyOHordWdaMkhKZjBWRjMvQ0tjcGViU0xMNWdyZ3BEYjRuejFGY1pYeGwycVprZGtHeENsRytVL0Ywc0V6TnBBL1NkUS9BZzZNT0dpTTJMeVRDZ2NsQnc4TmtMMmk0MXgvT244QmUyOG1iZU5TVlFDUDhBY24iLCJtYWMiOiIyNjBmZjlkNDkyMDRiMDM0MTZjMTY5N2QxZmQyMGFlNzJkZDUxMDE3NTY5ZjU4OGI1ZmFkMzlmNTZhMDUyODY2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1554854793 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>laravel_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|3wccpylwRhXpahJKUyCq5OB1acJKoA1uVT8wR590398beZmetuIix9Oz31TE|$2y$12$w9Xe5C9oBSghC7wjYhaL8e4NH7n0qFNzXvZwq4eFPiPRNhYyYy9zq</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PcVgzRJ2u4aLQUh3Wf0zWNjBL5JJ2KyXi8PQjBGb</span>\"\n  \"<span class=sf-dump-key>i_e_l_t_s_a_i_scoring_system_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TGbnOTYIBZUzNJj0xv9nDBkjG9tZftJvLifPfv1e</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1554854793\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1284794611 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 18 Jul 2025 17:02:10 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1284794611\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1054628900 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PcVgzRJ2u4aLQUh3Wf0zWNjBL5JJ2KyXi8PQjBGb</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/scoring/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1752849859</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1054628900\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/scoring/show/1", "action_name": "scoring.show", "controller_action": "App\\Http\\Controllers\\ScoringController@show"}, "badge": null}}