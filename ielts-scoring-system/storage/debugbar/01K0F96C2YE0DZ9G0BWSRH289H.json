{"__meta": {"id": "01K0F96C2YE0DZ9G0BWSRH289H", "datetime": "2025-07-19 00:26:59", "utime": **********.422826, "method": "GET", "uri": "/scoring/history", "ip": "127.0.0.1"}, "messages": {"count": 2, "messages": [{"message": "[00:26:59] LOG.info: IELTSScorer initialized {\n    \"api_url\": \"https:\\/\\/api.v3.cm\\/v1\\/chat\\/completions\",\n    \"model\": \"o3\",\n    \"api_key_length\": 51\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.041508, "xdebug_link": null, "collector": "log"}, {"message": "[00:26:59] LOG.error: Missing required parameter for [Route: scoring.destroy] [URI: scoring/delete/{attempt}] [Missing parameter: attempt]. (View: C:\\laragon\\www\\ielts\\ielts-scoring-system\\resources\\views\\scoring\\history.blade.php) {\n    \"userId\": 1,\n    \"exception\": {}\n}", "message_html": null, "is_string": false, "label": "error", "time": **********.091526, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1752859618.88231, "end": **********.422841, "duration": 0.5405311584472656, "duration_str": "541ms", "measures": [{"label": "Booting", "start": 1752859618.88231, "relative_start": 0, "end": **********.017034, "relative_end": **********.017034, "duration": 0.*****************, "duration_str": "135ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.017045, "relative_start": 0.***************, "end": **********.422842, "relative_end": 9.5367431640625e-07, "duration": 0.*****************, "duration_str": "406ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.036693, "relative_start": 0.*****************, "end": **********.038508, "relative_end": **********.038508, "duration": 0.0018148422241210938, "duration_str": "1.81ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.082211, "relative_start": 0.*****************, "end": **********.422849, "relative_end": 7.867813110351562e-06, "duration": 0.****************, "duration_str": "341ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: scoring.history", "start": **********.083698, "relative_start": 0.*****************, "end": **********.083698, "relative_end": **********.083698, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::show", "start": **********.329303, "relative_start": 0.****************, "end": **********.329303, "relative_end": **********.329303, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.navigation", "start": **********.331021, "relative_start": 0.4487111568450928, "end": **********.331021, "relative_end": **********.331021, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.theme-switcher", "start": **********.33139, "relative_start": 0.4490799903869629, "end": **********.33139, "relative_end": **********.33139, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.icons.sun", "start": **********.331821, "relative_start": 0.4495110511779785, "end": **********.331821, "relative_end": **********.331821, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.icons.moon", "start": **********.332118, "relative_start": 0.44980812072753906, "end": **********.332118, "relative_end": **********.332118, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.icons.sun", "start": **********.332371, "relative_start": 0.4500610828399658, "end": **********.332371, "relative_end": **********.332371, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.icons.moon", "start": **********.332567, "relative_start": 0.4502570629119873, "end": **********.332567, "relative_end": **********.332567, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.icons.computer-desktop", "start": **********.332801, "relative_start": 0.45049118995666504, "end": **********.332801, "relative_end": **********.332801, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.header", "start": **********.333093, "relative_start": 0.*****************, "end": **********.333093, "relative_end": **********.333093, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.card", "start": **********.333472, "relative_start": 0.*****************, "end": **********.333472, "relative_end": **********.333472, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.trace-and-editor", "start": **********.333794, "relative_start": 0.*****************, "end": **********.333794, "relative_end": **********.333794, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.trace", "start": **********.343443, "relative_start": 0.****************, "end": **********.343443, "relative_end": **********.343443, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.icons.chevron-down", "start": **********.344084, "relative_start": 0.4617741107940674, "end": **********.344084, "relative_end": **********.344084, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.icons.chevron-up", "start": **********.344517, "relative_start": 0.4622070789337158, "end": **********.344517, "relative_end": **********.344517, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.icons.chevron-up", "start": **********.344856, "relative_start": 0.46254611015319824, "end": **********.344856, "relative_end": **********.344856, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.icons.chevron-down", "start": **********.345054, "relative_start": 0.46274399757385254, "end": **********.345054, "relative_end": **********.345054, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.editor", "start": **********.365582, "relative_start": 0.*****************, "end": **********.365582, "relative_end": **********.365582, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.card", "start": **********.408116, "relative_start": 0.525806188583374, "end": **********.408116, "relative_end": **********.408116, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.context", "start": **********.408496, "relative_start": 0.****************, "end": **********.408496, "relative_end": **********.408496, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.card", "start": **********.409048, "relative_start": 0.526738166809082, "end": **********.409048, "relative_end": **********.409048, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.card", "start": **********.409353, "relative_start": 0.****************, "end": **********.409353, "relative_end": **********.409353, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.layout", "start": **********.409582, "relative_start": 0.****************, "end": **********.409582, "relative_end": **********.409582, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "27MB"}, "exceptions": {"count": 2, "exceptions": [{"type": "Illuminate\\View\\ViewException", "message": "Missing required parameter for [Route: scoring.destroy] [URI: scoring/delete/{attempt}] [Missing parameter: attempt]. (View: C:\\laragon\\www\\ielts\\ielts-scoring-system\\resources\\views\\scoring\\history.blade.php)", "code": 0, "file": "vendor/laravel/framework/src/Illuminate/Routing/Exceptions/UrlGenerationException.php", "line": 35, "stack_trace": null, "stack_trace_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:63</span> [<samp data-depth=1 class=sf-dump-expanded>\n  <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>59</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"19 characters\">handleViewException</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"38 characters\">Illuminate\\View\\Engines\\CompilerEngine</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"61 characters\">[object Illuminate\\Routing\\Exceptions\\UrlGenerationException]</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-num>1</span>\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"71 characters\">vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>76</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"12 characters\">evaluatePath</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\View\\Engines\\PhpEngine</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"102 characters\">C:\\laragon\\www\\ielts\\ielts-scoring-system\\storage\\framework\\views/3d0e4b2b3d75180b3dbc322942eb1a4c.php</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>__env</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\View\\Factory\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\View</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Factory</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21305 title=\"2 occurrences\">#1305</a> &#8230;25}\n        \"<span class=sf-dump-key>app</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref24 title=\"2 occurrences\">#4</a> &#8230;44}\n        \"<span class=sf-dump-key>errors</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\ViewErrorBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ViewErrorBag</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21430 title=\"2 occurrences\">#1430</a><samp data-depth=5 id=sf-dump-**********-ref21430 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []\n        </samp>}\n        \"<span class=sf-dump-key>attempts</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Pagination\\LengthAwarePaginator\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Pagination</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">LengthAwarePaginator</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21484 title=\"2 occurrences\">#1484</a><samp data-depth=5 id=sf-dump-**********-ref21484 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#1456</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\ScoringAttempt\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ScoringAttempt</span></span> {<a class=sf-dump-ref>#1478</a><samp data-depth=8 class=sf-dump-compact>\n                #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"16 characters\">scoring_attempts</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:21</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>9</span>\n                  \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-num>1</span>\n                  \"<span class=sf-dump-key>essay_question_id</span>\" => <span class=sf-dump-const>null</span>\n                  \"<span class=sf-dump-key>essay_content</span>\" => \"\"\"\n                    <span class=sf-dump-str title=\"1184 characters\">International news is good for student but sometime it is not nessessary. Many people think student in secondary school need to study about international news, other think this waste their time. My essay will write about both of these thing.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">Firstly, International news is good because it make student smart. When student learn international news they can know many things. Example, student can know weather in other country, famous people life, or footbal match. They will very interesting about these topic and can relax in the classroom. Learning international news is also help student speaking english well.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">However, I thinking international news sometime is boring. Student have many important subject, example maths, literature, or chemistry. These subjects can help student become doctor or engineer in future. International news not really important, only waste of time.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">In my opinion, secondary student should play sport and learn math better than learn international news. Teacher should teaching student good subject not international news.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">I conclusion, international news have advantage but more disadvantage. Student no need study this subject, waste of their time.</span>\n                    \"\"\"\n                  \"<span class=sf-dump-key>task_type</span>\" => \"<span class=sf-dump-str title=\"5 characters\">task2</span>\"\n                  \"<span class=sf-dump-key>time_limit</span>\" => <span class=sf-dump-num>40</span>\n                  \"<span class=sf-dump-key>overall_band_score</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.0</span>\"\n                  \"<span class=sf-dump-key>task_achievement</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.0</span>\"\n                  \"<span class=sf-dump-key>coherence_cohesion</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.0</span>\"\n                  \"<span class=sf-dump-key>lexical_resource</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.0</span>\"\n                  \"<span class=sf-dump-key>grammar_accuracy</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.0</span>\"\n                  \"<span class=sf-dump-key>detailed_feedback</span>\" => \"<span class=sf-dump-str title=\"2086 characters\">{&quot;grammar_accuracy&quot;: {&quot;score&quot;: 4, &quot;issues&quot;: [&quot;S-V: \\&quot;news is &#8230; it make student\\&quot;, \\&quot;Learning international news is also help&#8230;\\&quot;&quot;, &quot;Thi&#7871;u m&#7841;o t&#7915;: \\&quot;in other country\\&quot;, \\&quot;famous people life\\&quot;&quot;], &quot;feedback&quot;: &quot;L&#7895;i S-V agreement, thi&#7871;u m&#7841;o t&#7915;, gi&#7899;i t&#7915;, th&#236;, c&#7845;u tr&#250;c c&#226;u h&#7847;u h&#7871;t &#7903; m&#7913;c &#273;&#417;n gi&#7843;n.&quot;, &quot;improvements&quot;: [&quot;&#212;n quy t&#7855;c S-V agreement, &#273;&#7863;c bi&#7879;t v&#7899;i danh t&#7915; s&#7889; nhi&#7873;u/kh&#244;ng &#273;&#7871;m &#273;&#432;&#7907;c.&quot;, &quot;K&#7871;t h&#7907;p c&#226;u ph&#7913;c (because, although, which), v&#224; d&#249;ng &#273;&#250;ng th&#236; hi&#7879;n t&#7841;i &#273;&#417;n/hi&#7879;n t&#7841;i ti&#7871;p di&#7877;n.&quot;]}, &quot;lexical_resource&quot;: {&quot;score&quot;: 4, &quot;issues&quot;: [&quot;Ch&#237;nh t&#7843;: nessessary, footbal, interesting (d&#249;ng sai d&#7841;ng), English kh&#244;ng vi&#7871;t hoa&#8230;&quot;, &quot;Collocation sai: \\&quot;very interesting about\\&quot;, \\&quot;good subject\\&quot;, \\&quot;help student speaking\\&quot;&#8230;&quot;], &quot;feedback&quot;: &quot;T&#7915; v&#7921;ng ngh&#232;o, l&#7863;p t&#7915; &#8220;student&#8221;, &#8220;international news&#8221;, sai ch&#237;nh t&#7843; nhi&#7873;u, collocation ch&#432;a ch&#237;nh x&#225;c.&quot;, &quot;improvements&quot;: [&quot;Ghi nh&#7899; ch&#237;nh t&#7843; chu&#7849;n, tra t&#7915; &#273;i&#7875;n tr&#432;&#7899;c khi vi&#7871;t.&quot;, &quot;H&#7885;c collocation: be interested in, improve speaking skills, beneficial subject&#8230; v&#224; thay th&#7871; t&#7915; l&#7863;p b&#7857;ng synonyms.&quot;]}, &quot;task_achievement&quot;: {&quot;score&quot;: 4, &quot;issues&quot;: [&quot;Thi&#7871;u t&#7889;i thi&#7875;u 250 t&#7915;.&quot;, &quot;L&#7853;p lu&#7853;n n&#244;ng, v&#237; d&#7909; chung chung, ch&#432;a ch&#7913;ng minh quan &#273;i&#7875;m.&quot;], &quot;feedback&quot;: &quot;B&#224;i vi&#7871;t ch&#432;a &#273;&#225;p &#7913;ng &#273;&#7847;y &#273;&#7911; y&#234;u c&#7847;u Task 2. B&#224;i ch&#7881; c&#243; 179 t&#7915; (&lt; 250 t&#7915; b&#7855;t bu&#7897;c), quan &#273;i&#7875;m c&#242;n m&#417; h&#7891; v&#224; ch&#432;a &#273;&#432;&#7907;c ph&#225;t tri&#7875;n v&#7899;i d&#7851;n ch&#7913;ng thuy&#7871;t ph&#7909;c.&quot;, &quot;improvements&quot;: [&quot;M&#7903; r&#7897;ng b&#224;i l&#234;n &#237;t nh&#7845;t 260-280 t&#7915;, ph&#225;t tri&#7875;n th&#234;m &#253; &#7903; m&#7895;i &#273;o&#7841;n v&#7899;i v&#237; d&#7909; c&#7909; th&#7875; (s&#7889; li&#7879;u, t&#236;nh hu&#7889;ng th&#7921;c t&#7871;).&quot;, &quot;N&#234;u r&#245; l&#7853;p tr&#432;&#7901;ng ngay t&#7915; m&#7903; b&#224;i v&#224; nh&#7855;c l&#7841;i nh&#7845;t qu&#225;n, s&#7917; d&#7909;ng l&#7853;p lu&#7853;n so s&#225;nh &#8211; ph&#226;n t&#237;ch l&#7907;i h&#7841;i thay v&#236; li&#7879;t k&#234;.&quot;]}, &quot;coherence_cohesion&quot;: {&quot;score&quot;: 4, &quot;issues&quot;: [&quot;D&#7845;u hi&#7879;u li&#234;n k&#7871;t h&#7841;n ch&#7871; (First, However&#8230;), thi&#7871;u therefore, moreover&#8230;&quot;, &quot;Thi&#7871;u ch&#7911; &#273;&#7873; c&#226;u (topic sentence) r&#245; r&#224;ng cho t&#7915;ng &#273;o&#7841;n th&#226;n.&quot;], &quot;feedback&quot;: &quot;B&#7889; c&#7909;c c&#243; m&#7903;-th&#226;n-k&#7871;t nh&#432;ng li&#234;n k&#7871;t l&#7887;ng l&#7867;o, t&#7915; n&#7889;i c&#242;n sai ho&#7863;c l&#7863;p. C&#226;u chuy&#7875;n &#273;o&#7841;n &#273;&#417;n gi&#7843;n n&#234;n &#253; t&#432;&#7903;ng r&#7901;i r&#7841;c.&quot;, &quot;improvements&quot;: [&quot;M&#7895;i &#273;o&#7841;n n&#234;n b&#7855;t &#273;&#7847;u b&#7857;ng c&#226;u ch&#7911; &#273;&#7873;, sau &#273;&#243; gi&#7843;i th&#237;ch v&#224; &#273;&#432;a v&#237; d&#7909;.&quot;, &quot;S&#7917; d&#7909;ng &#273;a d&#7841;ng cohesive devices: besides, as a result, in contrast&#8230; v&#224; d&#249;ng &#273;&#250;ng d&#7845;u ph&#7849;y.&quot;]}}</span>\"\n                  \"<span class=sf-dump-key>highlighted_corrections</span>\" => \"<span class=sf-dump-str title=\"3255 characters\">[{&quot;severity&quot;: &quot;high&quot;, &quot;error_type&quot;: &quot;grammar|spelling&quot;, &quot;explanation&quot;: &quot;thi&#7871;u &#039;s&#039; sau student, thi&#7871;u &#039;s&#039; sau sometimes, sai ch&#237;nh t&#7843; &#039;necessary&#039;&quot;, &quot;original_text&quot;: &quot;International news is good for student but sometime it is not nessessary.&quot;, &quot;suggested_correction&quot;: &quot;International news can be beneficial for students, but sometimes it is not necessary.&quot;}, {&quot;severity&quot;: &quot;high&quot;, &quot;error_type&quot;: &quot;grammar&quot;, &quot;explanation&quot;: &quot;thi&#7871;u s&#7889; nhi&#7873;u &#039;students&#039;, thi&#7871;u li&#234;n t&#7915; &#039;while&#039;, sai chia &#273;&#7897;ng t&#7915; &#039;think&#039;, thi&#7871;u &#273;&#7841;i t&#7915; &#039;it is&#039;.&quot;, &quot;original_text&quot;: &quot;Many people think student in secondary school need to study about international news, other think this waste their time.&quot;, &quot;suggested_correction&quot;: &quot;Many people think (that) secondary school students need to study international news, while others think it is a waste of time.&quot;}, {&quot;severity&quot;: &quot;medium&quot;, &quot;error_type&quot;: &quot;grammar&quot;, &quot;explanation&quot;: &quot;S-V agreement v&#224; t&#237;nh t&#7915; so s&#225;nh h&#417;n.&quot;, &quot;original_text&quot;: &quot;it make student smart.&quot;, &quot;suggested_correction&quot;: &quot;it makes students smarter.&quot;}, {&quot;severity&quot;: &quot;high&quot;, &quot;error_type&quot;: &quot;grammar|spelling|vocabulary&quot;, &quot;explanation&quot;: &quot;thi&#7871;u &#039;For&#039;, plural forms, s&#7903; h&#7919;u c&#225;ch, ch&#237;nh t&#7843; &#039;football&#039;.&quot;, &quot;original_text&quot;: &quot;Example, student can know weather in other country, famous people life, or footbal match.&quot;, &quot;suggested_correction&quot;: &quot;For example, students can learn about the weather in other countries, famous people&#039;s lives, or football matches.&quot;}, {&quot;severity&quot;: &quot;high&quot;, &quot;error_type&quot;: &quot;grammar|vocabulary&quot;, &quot;explanation&quot;: &quot;be interested in, plural &#039;topics&#039;.&quot;, &quot;original_text&quot;: &quot;They will very interesting about these topic&quot;, &quot;suggested_correction&quot;: &quot;They will be very interested in these topics&quot;}, {&quot;severity&quot;: &quot;high&quot;, &quot;error_type&quot;: &quot;grammar|spelling&quot;, &quot;explanation&quot;: &quot;thi&#7871;u &#039;s&#039; sau helps, c&#7847;n &#273;&#7897;ng t&#7915; &#039;improve&#039;, English vi&#7871;t hoa.&quot;, &quot;original_text&quot;: &quot;Learning international news is also help student speaking english well.&quot;, &quot;suggested_correction&quot;: &quot;Learning international news also helps students improve their English speaking skills.&quot;}, {&quot;severity&quot;: &quot;medium&quot;, &quot;error_type&quot;: &quot;grammar&quot;, &quot;explanation&quot;: &quot;sai th&#236; &#273;&#7897;ng t&#7915;, &#273;&#7843;o v&#7883; tr&#237; tr&#7841;ng t&#7915;.&quot;, &quot;original_text&quot;: &quot;However, I thinking international news sometime is boring.&quot;, &quot;suggested_correction&quot;: &quot;However, I think international news is sometimes boring.&quot;}, {&quot;severity&quot;: &quot;high&quot;, &quot;error_type&quot;: &quot;grammar&quot;, &quot;explanation&quot;: &quot;thi&#7871;u &#273;&#7897;ng t&#7915; &#039;is&#039;, thi&#7871;u m&#7841;o t&#7915; &#039;a&#039;.&quot;, &quot;original_text&quot;: &quot;International news not really important, only waste of time.&quot;, &quot;suggested_correction&quot;: &quot;International news is not really important; it is only a waste of time.&quot;}, {&quot;severity&quot;: &quot;medium&quot;, &quot;error_type&quot;: &quot;grammar|vocabulary&quot;, &quot;explanation&quot;: &quot;d&#249;ng &#273;&#7897;ng t&#7915; nguy&#234;n th&#7875; &#039;teach&#039;, plural teachers/students, d&#249;ng &#039;useful&#039;.&quot;, &quot;original_text&quot;: &quot;Teacher should teaching student good subject not international news.&quot;, &quot;suggested_correction&quot;: &quot;Teachers should teach students useful subjects rather than international news.&quot;}, {&quot;severity&quot;: &quot;high&quot;, &quot;error_type&quot;: &quot;grammar|vocabulary|spelling&quot;, &quot;explanation&quot;: &quot;In conclusion, S-V agreement &#039;news has&#039;, danh t&#7915; s&#7889; nhi&#7873;u &#039;disadvantages&#039;.&quot;, &quot;original_text&quot;: &quot;I conclusion, international news have advantage but more disadvantage.&quot;, &quot;suggested_correction&quot;: &quot;In conclusion, international news has some advantages but more disadvantages.&quot;}]</span>\"\n                  \"<span class=sf-dump-key>criteria_analysis</span>\" => \"<span class=sf-dump-str title=\"1665 characters\">[{&quot;criterion&quot;: &quot;Task Achievement&quot;, &quot;strengths&quot;: [&quot;C&#243; &#273;&#7873; c&#7853;p hai quan &#273;i&#7875;m nh&#432; y&#234;u c&#7847;u &#273;&#7873;.&quot;, &quot;N&#234;u &#273;&#432;&#7907;c quan &#273;i&#7875;m c&#225; nh&#226;n &#7903; &#273;o&#7841;n th&#226;n 2 v&#224; 3.&quot;], &quot;weaknesses&quot;: [&quot;B&#224;i d&#432;&#7899;i 250 t&#7915; n&#234;n b&#7883; tr&#7915; &#273;i&#7875;m n&#7863;ng.&quot;, &quot;L&#7853;p lu&#7853;n s&#417; s&#224;i, v&#237; d&#7909; ch&#432;a c&#7909; th&#7875;.&quot;, &quot;Thi&#7871;u ph&#7847;n gi&#7843;i th&#237;ch s&#226;u, kh&#244;ng c&#243; d&#7851;n ch&#7913;ng th&#7889;ng k&#234;.&quot;, &quot;Kh&#244;ng tr&#236;nh b&#224;y r&#245; r&#224;ng l&#253; do &#7911;ng h&#7897; ho&#7863;c ph&#7843;n &#273;&#7889;i.&quot;], &quot;band_justification&quot;: &quot;Theo m&#244; t&#7843; Band 4: tr&#7843; l&#7901;i m&#7897;t ph&#7847;n c&#226;u h&#7887;i, &#253; ch&#237;nh c&#242;n h&#7841;n ch&#7871; v&#224; thi&#7871;u h&#7895; tr&#7907;. B&#224;i n&#224;y ph&#249; h&#7907;p band 4.&quot;}, {&quot;criterion&quot;: &quot;Coherence &amp; Cohesion&quot;, &quot;strengths&quot;: [&quot;C&#243; m&#7903; b&#224;i, th&#226;n b&#224;i, k&#7871;t lu&#7853;n.&quot;, &quot;D&#249;ng &#273;&#432;&#7907;c &#039;Firstly&#039;, &#039;However&#039; &#273;&#7875; chuy&#7875;n &#253;.&quot;], &quot;weaknesses&quot;: [&quot;Li&#234;n k&#7871;t &#253; l&#7887;ng l&#7867;o, t&#7915; n&#7889;i l&#7863;p v&#224; thi&#7871;u.&quot;, &quot;C&#226;u ch&#7911; &#273;&#7873; ch&#432;a r&#245; r&#224;ng, s&#7855;p x&#7871;p &#253; thi&#7871;u logic.&quot;, &quot;Thi&#7871;u tham chi&#7871;u (this/that) v&#224; quan h&#7879; t&#7915;.&quot;, &quot;&#272;o&#7841;n 2 v&#224; 3 g&#7847;n nh&#432; tr&#249;ng n&#7897;i dung.&quot;], &quot;band_justification&quot;: &quot;Theo Band 4: t&#7893; ch&#7913;c th&#244;ng tin l&#7887;ng l&#7867;o, thi&#7871;t b&#7883; li&#234;n k&#7871;t c&#417; b&#7843;n v&#224; th&#432;&#7901;ng l&#7863;p; v&#236; v&#7853;y band 4.&quot;}, {&quot;criterion&quot;: &quot;Lexical Resource&quot;, &quot;strengths&quot;: [&quot;S&#7917; d&#7909;ng m&#7897;t s&#7889; t&#7915; v&#7921;ng li&#234;n quan ch&#7911; &#273;&#7873; nh&#432; &#039;weather&#039;, &#039;famous people&#039;.&quot;, &quot;C&#7889; g&#7855;ng d&#249;ng t&#237;nh t&#7915; (interesting, important).&quot;], &quot;weaknesses&quot;: [&quot;T&#7915; v&#7921;ng r&#7845;t h&#7841;n ch&#7871;, l&#7863;p nhi&#7873;u.&quot;, &quot;Nhi&#7873;u l&#7895;i ch&#237;nh t&#7843;, collocation sai.&quot;, &quot;&#205;t t&#7915; mang t&#237;nh h&#7885;c thu&#7853;t.&quot;], &quot;band_justification&quot;: &quot;Band 4: v&#7889;n t&#7915; h&#7841;n ch&#7871;, l&#7895;i th&#432;&#7901;ng xuy&#234;n g&#226;y tr&#7903; ng&#7841;i giao ti&#7871;p nh&#432;ng &#253; ch&#237;nh v&#7851;n hi&#7875;u.&quot;}, {&quot;criterion&quot;: &quot;Grammar &amp; Accuracy&quot;, &quot;strengths&quot;: [&quot;C&#243; d&#249;ng m&#7897;t s&#7889; th&#236; hi&#7879;n t&#7841;i &#273;&#417;n, c&#7845;u tr&#250;c &#273;&#417;n.&quot;], &quot;weaknesses&quot;: [&quot;L&#7895;i S-V agreement, m&#7841;o t&#7915;, gi&#7899;i t&#7915; ph&#7893; bi&#7871;n.&quot;, &quot;&#205;t c&#226;u ph&#7913;c; c&#7845;u tr&#250;c l&#7863;p.&quot;, &quot;Nhi&#7873;u l&#7895;i &#7843;nh h&#432;&#7903;ng s&#7921; hi&#7875;u.&quot;], &quot;band_justification&quot;: &quot;Ph&#249; h&#7907;p Band 4: L&#7895;i ng&#7919; ph&#225;p th&#432;&#7901;ng xuy&#234;n, &#237;t c&#7845;u tr&#250;c ph&#7913;c t&#7841;p.&quot;}]</span>\"\n                  \"<span class=sf-dump-key>statistics</span>\" => \"<span class=sf-dump-str title=\"144 characters\">{&quot;total_errors&quot;: 38, &quot;grammar_errors&quot;: 24, &quot;spelling_errors&quot;: 8, &quot;complexity_level&quot;: &quot;basic&quot;, &quot;sentence_variety&quot;: &quot;low&quot;, &quot;vocabulary_errors&quot;: 6}</span>\"\n                  \"<span class=sf-dump-key>word_count</span>\" => <span class=sf-dump-num>179</span>\n                  \"<span class=sf-dump-key>essay_length</span>\" => <span class=sf-dump-num>1184</span>\n                  \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"9 characters\">completed</span>\"\n                  \"<span class=sf-dump-key>error_message</span>\" => <span class=sf-dump-const>null</span>\n                  \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-18 15:06:07</span>\"\n                  \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-18 15:06:38</span>\"\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:21</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>9</span>\n                  \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-num>1</span>\n                  \"<span class=sf-dump-key>essay_question_id</span>\" => <span class=sf-dump-const>null</span>\n                  \"<span class=sf-dump-key>essay_content</span>\" => \"\"\"\n                    <span class=sf-dump-str title=\"1184 characters\">International news is good for student but sometime it is not nessessary. Many people think student in secondary school need to study about international news, other think this waste their time. My essay will write about both of these thing.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">Firstly, International news is good because it make student smart. When student learn international news they can know many things. Example, student can know weather in other country, famous people life, or footbal match. They will very interesting about these topic and can relax in the classroom. Learning international news is also help student speaking english well.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">However, I thinking international news sometime is boring. Student have many important subject, example maths, literature, or chemistry. These subjects can help student become doctor or engineer in future. International news not really important, only waste of time.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">In my opinion, secondary student should play sport and learn math better than learn international news. Teacher should teaching student good subject not international news.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">I conclusion, international news have advantage but more disadvantage. Student no need study this subject, waste of their time.</span>\n                    \"\"\"\n                  \"<span class=sf-dump-key>task_type</span>\" => \"<span class=sf-dump-str title=\"5 characters\">task2</span>\"\n                  \"<span class=sf-dump-key>time_limit</span>\" => <span class=sf-dump-num>40</span>\n                  \"<span class=sf-dump-key>overall_band_score</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.0</span>\"\n                  \"<span class=sf-dump-key>task_achievement</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.0</span>\"\n                  \"<span class=sf-dump-key>coherence_cohesion</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.0</span>\"\n                  \"<span class=sf-dump-key>lexical_resource</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.0</span>\"\n                  \"<span class=sf-dump-key>grammar_accuracy</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.0</span>\"\n                  \"<span class=sf-dump-key>detailed_feedback</span>\" => \"<span class=sf-dump-str title=\"2086 characters\">{&quot;grammar_accuracy&quot;: {&quot;score&quot;: 4, &quot;issues&quot;: [&quot;S-V: \\&quot;news is &#8230; it make student\\&quot;, \\&quot;Learning international news is also help&#8230;\\&quot;&quot;, &quot;Thi&#7871;u m&#7841;o t&#7915;: \\&quot;in other country\\&quot;, \\&quot;famous people life\\&quot;&quot;], &quot;feedback&quot;: &quot;L&#7895;i S-V agreement, thi&#7871;u m&#7841;o t&#7915;, gi&#7899;i t&#7915;, th&#236;, c&#7845;u tr&#250;c c&#226;u h&#7847;u h&#7871;t &#7903; m&#7913;c &#273;&#417;n gi&#7843;n.&quot;, &quot;improvements&quot;: [&quot;&#212;n quy t&#7855;c S-V agreement, &#273;&#7863;c bi&#7879;t v&#7899;i danh t&#7915; s&#7889; nhi&#7873;u/kh&#244;ng &#273;&#7871;m &#273;&#432;&#7907;c.&quot;, &quot;K&#7871;t h&#7907;p c&#226;u ph&#7913;c (because, although, which), v&#224; d&#249;ng &#273;&#250;ng th&#236; hi&#7879;n t&#7841;i &#273;&#417;n/hi&#7879;n t&#7841;i ti&#7871;p di&#7877;n.&quot;]}, &quot;lexical_resource&quot;: {&quot;score&quot;: 4, &quot;issues&quot;: [&quot;Ch&#237;nh t&#7843;: nessessary, footbal, interesting (d&#249;ng sai d&#7841;ng), English kh&#244;ng vi&#7871;t hoa&#8230;&quot;, &quot;Collocation sai: \\&quot;very interesting about\\&quot;, \\&quot;good subject\\&quot;, \\&quot;help student speaking\\&quot;&#8230;&quot;], &quot;feedback&quot;: &quot;T&#7915; v&#7921;ng ngh&#232;o, l&#7863;p t&#7915; &#8220;student&#8221;, &#8220;international news&#8221;, sai ch&#237;nh t&#7843; nhi&#7873;u, collocation ch&#432;a ch&#237;nh x&#225;c.&quot;, &quot;improvements&quot;: [&quot;Ghi nh&#7899; ch&#237;nh t&#7843; chu&#7849;n, tra t&#7915; &#273;i&#7875;n tr&#432;&#7899;c khi vi&#7871;t.&quot;, &quot;H&#7885;c collocation: be interested in, improve speaking skills, beneficial subject&#8230; v&#224; thay th&#7871; t&#7915; l&#7863;p b&#7857;ng synonyms.&quot;]}, &quot;task_achievement&quot;: {&quot;score&quot;: 4, &quot;issues&quot;: [&quot;Thi&#7871;u t&#7889;i thi&#7875;u 250 t&#7915;.&quot;, &quot;L&#7853;p lu&#7853;n n&#244;ng, v&#237; d&#7909; chung chung, ch&#432;a ch&#7913;ng minh quan &#273;i&#7875;m.&quot;], &quot;feedback&quot;: &quot;B&#224;i vi&#7871;t ch&#432;a &#273;&#225;p &#7913;ng &#273;&#7847;y &#273;&#7911; y&#234;u c&#7847;u Task 2. B&#224;i ch&#7881; c&#243; 179 t&#7915; (&lt; 250 t&#7915; b&#7855;t bu&#7897;c), quan &#273;i&#7875;m c&#242;n m&#417; h&#7891; v&#224; ch&#432;a &#273;&#432;&#7907;c ph&#225;t tri&#7875;n v&#7899;i d&#7851;n ch&#7913;ng thuy&#7871;t ph&#7909;c.&quot;, &quot;improvements&quot;: [&quot;M&#7903; r&#7897;ng b&#224;i l&#234;n &#237;t nh&#7845;t 260-280 t&#7915;, ph&#225;t tri&#7875;n th&#234;m &#253; &#7903; m&#7895;i &#273;o&#7841;n v&#7899;i v&#237; d&#7909; c&#7909; th&#7875; (s&#7889; li&#7879;u, t&#236;nh hu&#7889;ng th&#7921;c t&#7871;).&quot;, &quot;N&#234;u r&#245; l&#7853;p tr&#432;&#7901;ng ngay t&#7915; m&#7903; b&#224;i v&#224; nh&#7855;c l&#7841;i nh&#7845;t qu&#225;n, s&#7917; d&#7909;ng l&#7853;p lu&#7853;n so s&#225;nh &#8211; ph&#226;n t&#237;ch l&#7907;i h&#7841;i thay v&#236; li&#7879;t k&#234;.&quot;]}, &quot;coherence_cohesion&quot;: {&quot;score&quot;: 4, &quot;issues&quot;: [&quot;D&#7845;u hi&#7879;u li&#234;n k&#7871;t h&#7841;n ch&#7871; (First, However&#8230;), thi&#7871;u therefore, moreover&#8230;&quot;, &quot;Thi&#7871;u ch&#7911; &#273;&#7873; c&#226;u (topic sentence) r&#245; r&#224;ng cho t&#7915;ng &#273;o&#7841;n th&#226;n.&quot;], &quot;feedback&quot;: &quot;B&#7889; c&#7909;c c&#243; m&#7903;-th&#226;n-k&#7871;t nh&#432;ng li&#234;n k&#7871;t l&#7887;ng l&#7867;o, t&#7915; n&#7889;i c&#242;n sai ho&#7863;c l&#7863;p. C&#226;u chuy&#7875;n &#273;o&#7841;n &#273;&#417;n gi&#7843;n n&#234;n &#253; t&#432;&#7903;ng r&#7901;i r&#7841;c.&quot;, &quot;improvements&quot;: [&quot;M&#7895;i &#273;o&#7841;n n&#234;n b&#7855;t &#273;&#7847;u b&#7857;ng c&#226;u ch&#7911; &#273;&#7873;, sau &#273;&#243; gi&#7843;i th&#237;ch v&#224; &#273;&#432;a v&#237; d&#7909;.&quot;, &quot;S&#7917; d&#7909;ng &#273;a d&#7841;ng cohesive devices: besides, as a result, in contrast&#8230; v&#224; d&#249;ng &#273;&#250;ng d&#7845;u ph&#7849;y.&quot;]}}</span>\"\n                  \"<span class=sf-dump-key>highlighted_corrections</span>\" => \"<span class=sf-dump-str title=\"3255 characters\">[{&quot;severity&quot;: &quot;high&quot;, &quot;error_type&quot;: &quot;grammar|spelling&quot;, &quot;explanation&quot;: &quot;thi&#7871;u &#039;s&#039; sau student, thi&#7871;u &#039;s&#039; sau sometimes, sai ch&#237;nh t&#7843; &#039;necessary&#039;&quot;, &quot;original_text&quot;: &quot;International news is good for student but sometime it is not nessessary.&quot;, &quot;suggested_correction&quot;: &quot;International news can be beneficial for students, but sometimes it is not necessary.&quot;}, {&quot;severity&quot;: &quot;high&quot;, &quot;error_type&quot;: &quot;grammar&quot;, &quot;explanation&quot;: &quot;thi&#7871;u s&#7889; nhi&#7873;u &#039;students&#039;, thi&#7871;u li&#234;n t&#7915; &#039;while&#039;, sai chia &#273;&#7897;ng t&#7915; &#039;think&#039;, thi&#7871;u &#273;&#7841;i t&#7915; &#039;it is&#039;.&quot;, &quot;original_text&quot;: &quot;Many people think student in secondary school need to study about international news, other think this waste their time.&quot;, &quot;suggested_correction&quot;: &quot;Many people think (that) secondary school students need to study international news, while others think it is a waste of time.&quot;}, {&quot;severity&quot;: &quot;medium&quot;, &quot;error_type&quot;: &quot;grammar&quot;, &quot;explanation&quot;: &quot;S-V agreement v&#224; t&#237;nh t&#7915; so s&#225;nh h&#417;n.&quot;, &quot;original_text&quot;: &quot;it make student smart.&quot;, &quot;suggested_correction&quot;: &quot;it makes students smarter.&quot;}, {&quot;severity&quot;: &quot;high&quot;, &quot;error_type&quot;: &quot;grammar|spelling|vocabulary&quot;, &quot;explanation&quot;: &quot;thi&#7871;u &#039;For&#039;, plural forms, s&#7903; h&#7919;u c&#225;ch, ch&#237;nh t&#7843; &#039;football&#039;.&quot;, &quot;original_text&quot;: &quot;Example, student can know weather in other country, famous people life, or footbal match.&quot;, &quot;suggested_correction&quot;: &quot;For example, students can learn about the weather in other countries, famous people&#039;s lives, or football matches.&quot;}, {&quot;severity&quot;: &quot;high&quot;, &quot;error_type&quot;: &quot;grammar|vocabulary&quot;, &quot;explanation&quot;: &quot;be interested in, plural &#039;topics&#039;.&quot;, &quot;original_text&quot;: &quot;They will very interesting about these topic&quot;, &quot;suggested_correction&quot;: &quot;They will be very interested in these topics&quot;}, {&quot;severity&quot;: &quot;high&quot;, &quot;error_type&quot;: &quot;grammar|spelling&quot;, &quot;explanation&quot;: &quot;thi&#7871;u &#039;s&#039; sau helps, c&#7847;n &#273;&#7897;ng t&#7915; &#039;improve&#039;, English vi&#7871;t hoa.&quot;, &quot;original_text&quot;: &quot;Learning international news is also help student speaking english well.&quot;, &quot;suggested_correction&quot;: &quot;Learning international news also helps students improve their English speaking skills.&quot;}, {&quot;severity&quot;: &quot;medium&quot;, &quot;error_type&quot;: &quot;grammar&quot;, &quot;explanation&quot;: &quot;sai th&#236; &#273;&#7897;ng t&#7915;, &#273;&#7843;o v&#7883; tr&#237; tr&#7841;ng t&#7915;.&quot;, &quot;original_text&quot;: &quot;However, I thinking international news sometime is boring.&quot;, &quot;suggested_correction&quot;: &quot;However, I think international news is sometimes boring.&quot;}, {&quot;severity&quot;: &quot;high&quot;, &quot;error_type&quot;: &quot;grammar&quot;, &quot;explanation&quot;: &quot;thi&#7871;u &#273;&#7897;ng t&#7915; &#039;is&#039;, thi&#7871;u m&#7841;o t&#7915; &#039;a&#039;.&quot;, &quot;original_text&quot;: &quot;International news not really important, only waste of time.&quot;, &quot;suggested_correction&quot;: &quot;International news is not really important; it is only a waste of time.&quot;}, {&quot;severity&quot;: &quot;medium&quot;, &quot;error_type&quot;: &quot;grammar|vocabulary&quot;, &quot;explanation&quot;: &quot;d&#249;ng &#273;&#7897;ng t&#7915; nguy&#234;n th&#7875; &#039;teach&#039;, plural teachers/students, d&#249;ng &#039;useful&#039;.&quot;, &quot;original_text&quot;: &quot;Teacher should teaching student good subject not international news.&quot;, &quot;suggested_correction&quot;: &quot;Teachers should teach students useful subjects rather than international news.&quot;}, {&quot;severity&quot;: &quot;high&quot;, &quot;error_type&quot;: &quot;grammar|vocabulary|spelling&quot;, &quot;explanation&quot;: &quot;In conclusion, S-V agreement &#039;news has&#039;, danh t&#7915; s&#7889; nhi&#7873;u &#039;disadvantages&#039;.&quot;, &quot;original_text&quot;: &quot;I conclusion, international news have advantage but more disadvantage.&quot;, &quot;suggested_correction&quot;: &quot;In conclusion, international news has some advantages but more disadvantages.&quot;}]</span>\"\n                  \"<span class=sf-dump-key>criteria_analysis</span>\" => \"<span class=sf-dump-str title=\"1665 characters\">[{&quot;criterion&quot;: &quot;Task Achievement&quot;, &quot;strengths&quot;: [&quot;C&#243; &#273;&#7873; c&#7853;p hai quan &#273;i&#7875;m nh&#432; y&#234;u c&#7847;u &#273;&#7873;.&quot;, &quot;N&#234;u &#273;&#432;&#7907;c quan &#273;i&#7875;m c&#225; nh&#226;n &#7903; &#273;o&#7841;n th&#226;n 2 v&#224; 3.&quot;], &quot;weaknesses&quot;: [&quot;B&#224;i d&#432;&#7899;i 250 t&#7915; n&#234;n b&#7883; tr&#7915; &#273;i&#7875;m n&#7863;ng.&quot;, &quot;L&#7853;p lu&#7853;n s&#417; s&#224;i, v&#237; d&#7909; ch&#432;a c&#7909; th&#7875;.&quot;, &quot;Thi&#7871;u ph&#7847;n gi&#7843;i th&#237;ch s&#226;u, kh&#244;ng c&#243; d&#7851;n ch&#7913;ng th&#7889;ng k&#234;.&quot;, &quot;Kh&#244;ng tr&#236;nh b&#224;y r&#245; r&#224;ng l&#253; do &#7911;ng h&#7897; ho&#7863;c ph&#7843;n &#273;&#7889;i.&quot;], &quot;band_justification&quot;: &quot;Theo m&#244; t&#7843; Band 4: tr&#7843; l&#7901;i m&#7897;t ph&#7847;n c&#226;u h&#7887;i, &#253; ch&#237;nh c&#242;n h&#7841;n ch&#7871; v&#224; thi&#7871;u h&#7895; tr&#7907;. B&#224;i n&#224;y ph&#249; h&#7907;p band 4.&quot;}, {&quot;criterion&quot;: &quot;Coherence &amp; Cohesion&quot;, &quot;strengths&quot;: [&quot;C&#243; m&#7903; b&#224;i, th&#226;n b&#224;i, k&#7871;t lu&#7853;n.&quot;, &quot;D&#249;ng &#273;&#432;&#7907;c &#039;Firstly&#039;, &#039;However&#039; &#273;&#7875; chuy&#7875;n &#253;.&quot;], &quot;weaknesses&quot;: [&quot;Li&#234;n k&#7871;t &#253; l&#7887;ng l&#7867;o, t&#7915; n&#7889;i l&#7863;p v&#224; thi&#7871;u.&quot;, &quot;C&#226;u ch&#7911; &#273;&#7873; ch&#432;a r&#245; r&#224;ng, s&#7855;p x&#7871;p &#253; thi&#7871;u logic.&quot;, &quot;Thi&#7871;u tham chi&#7871;u (this/that) v&#224; quan h&#7879; t&#7915;.&quot;, &quot;&#272;o&#7841;n 2 v&#224; 3 g&#7847;n nh&#432; tr&#249;ng n&#7897;i dung.&quot;], &quot;band_justification&quot;: &quot;Theo Band 4: t&#7893; ch&#7913;c th&#244;ng tin l&#7887;ng l&#7867;o, thi&#7871;t b&#7883; li&#234;n k&#7871;t c&#417; b&#7843;n v&#224; th&#432;&#7901;ng l&#7863;p; v&#236; v&#7853;y band 4.&quot;}, {&quot;criterion&quot;: &quot;Lexical Resource&quot;, &quot;strengths&quot;: [&quot;S&#7917; d&#7909;ng m&#7897;t s&#7889; t&#7915; v&#7921;ng li&#234;n quan ch&#7911; &#273;&#7873; nh&#432; &#039;weather&#039;, &#039;famous people&#039;.&quot;, &quot;C&#7889; g&#7855;ng d&#249;ng t&#237;nh t&#7915; (interesting, important).&quot;], &quot;weaknesses&quot;: [&quot;T&#7915; v&#7921;ng r&#7845;t h&#7841;n ch&#7871;, l&#7863;p nhi&#7873;u.&quot;, &quot;Nhi&#7873;u l&#7895;i ch&#237;nh t&#7843;, collocation sai.&quot;, &quot;&#205;t t&#7915; mang t&#237;nh h&#7885;c thu&#7853;t.&quot;], &quot;band_justification&quot;: &quot;Band 4: v&#7889;n t&#7915; h&#7841;n ch&#7871;, l&#7895;i th&#432;&#7901;ng xuy&#234;n g&#226;y tr&#7903; ng&#7841;i giao ti&#7871;p nh&#432;ng &#253; ch&#237;nh v&#7851;n hi&#7875;u.&quot;}, {&quot;criterion&quot;: &quot;Grammar &amp; Accuracy&quot;, &quot;strengths&quot;: [&quot;C&#243; d&#249;ng m&#7897;t s&#7889; th&#236; hi&#7879;n t&#7841;i &#273;&#417;n, c&#7845;u tr&#250;c &#273;&#417;n.&quot;], &quot;weaknesses&quot;: [&quot;L&#7895;i S-V agreement, m&#7841;o t&#7915;, gi&#7899;i t&#7915; ph&#7893; bi&#7871;n.&quot;, &quot;&#205;t c&#226;u ph&#7913;c; c&#7845;u tr&#250;c l&#7863;p.&quot;, &quot;Nhi&#7873;u l&#7895;i &#7843;nh h&#432;&#7903;ng s&#7921; hi&#7875;u.&quot;], &quot;band_justification&quot;: &quot;Ph&#249; h&#7907;p Band 4: L&#7895;i ng&#7919; ph&#225;p th&#432;&#7901;ng xuy&#234;n, &#237;t c&#7845;u tr&#250;c ph&#7913;c t&#7841;p.&quot;}]</span>\"\n                  \"<span class=sf-dump-key>statistics</span>\" => \"<span class=sf-dump-str title=\"144 characters\">{&quot;total_errors&quot;: 38, &quot;grammar_errors&quot;: 24, &quot;spelling_errors&quot;: 8, &quot;complexity_level&quot;: &quot;basic&quot;, &quot;sentence_variety&quot;: &quot;low&quot;, &quot;vocabulary_errors&quot;: 6}</span>\"\n                  \"<span class=sf-dump-key>word_count</span>\" => <span class=sf-dump-num>179</span>\n                  \"<span class=sf-dump-key>essay_length</span>\" => <span class=sf-dump-num>1184</span>\n                  \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"9 characters\">completed</span>\"\n                  \"<span class=sf-dump-key>error_message</span>\" => <span class=sf-dump-const>null</span>\n                  \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-18 15:06:07</span>\"\n                  \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-18 15:06:38</span>\"\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">previous</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:9</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>detailed_feedback</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                  \"<span class=sf-dump-key>highlighted_corrections</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                  \"<span class=sf-dump-key>criteria_analysis</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                  \"<span class=sf-dump-key>statistics</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                  \"<span class=sf-dump-key>overall_band_score</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:1</span>\"\n                  \"<span class=sf-dump-key>task_achievement</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:1</span>\"\n                  \"<span class=sf-dump-key>coherence_cohesion</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:1</span>\"\n                  \"<span class=sf-dump-key>lexical_resource</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:1</span>\"\n                  \"<span class=sf-dump-key>grammar_accuracy</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:1</span>\"\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>essayQuestion</span>\" => <span class=sf-dump-const>null</span>\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadCallback</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadContext</span>: <span class=sf-dump-const>null</span>\n                +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:18</span> [<samp data-depth=9 class=sf-dump-compact>\n                  <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">user_id</span>\"\n                  <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"17 characters\">essay_question_id</span>\"\n                  <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"13 characters\">essay_content</span>\"\n                  <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"9 characters\">task_type</span>\"\n                  <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"10 characters\">time_limit</span>\"\n                  <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"18 characters\">overall_band_score</span>\"\n                  <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"16 characters\">task_achievement</span>\"\n                  <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"18 characters\">coherence_cohesion</span>\"\n                  <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"16 characters\">lexical_resource</span>\"\n                  <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"16 characters\">grammar_accuracy</span>\"\n                  <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"17 characters\">detailed_feedback</span>\"\n                  <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"23 characters\">highlighted_corrections</span>\"\n                  <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"17 characters\">criteria_analysis</span>\"\n                  <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"10 characters\">statistics</span>\"\n                  <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"10 characters\">word_count</span>\"\n                  <span class=sf-dump-index>15</span> => \"<span class=sf-dump-str title=\"12 characters\">essay_length</span>\"\n                  <span class=sf-dump-index>16</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n                  <span class=sf-dump-index>17</span> => \"<span class=sf-dump-str title=\"13 characters\">error_message</span>\"\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=9 class=sf-dump-compact>\n                  <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n                </samp>]\n              </samp>}\n              <span class=sf-dump-index>1</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\ScoringAttempt\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ScoringAttempt</span></span> {<a class=sf-dump-ref>#1488</a><samp data-depth=8 class=sf-dump-compact>\n                #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"16 characters\">scoring_attempts</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:21</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>5</span>\n                  \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-num>1</span>\n                  \"<span class=sf-dump-key>essay_question_id</span>\" => <span class=sf-dump-const>null</span>\n                  \"<span class=sf-dump-key>essay_content</span>\" => \"\"\"\n                    <span class=sf-dump-str title=\"1184 characters\">International news is good for student but sometime it is not nessessary. Many people think student in secondary school need to study about international news, other think this waste their time. My essay will write about both of these thing.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">Firstly, International news is good because it make student smart. When student learn international news they can know many things. Example, student can know weather in other country, famous people life, or footbal match. They will very interesting about these topic and can relax in the classroom. Learning international news is also help student speaking english well.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">However, I thinking international news sometime is boring. Student have many important subject, example maths, literature, or chemistry. These subjects can help student become doctor or engineer in future. International news not really important, only waste of time.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">In my opinion, secondary student should play sport and learn math better than learn international news. Teacher should teaching student good subject not international news.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">I conclusion, international news have advantage but more disadvantage. Student no need study this subject, waste of their time.</span>\n                    \"\"\"\n                  \"<span class=sf-dump-key>task_type</span>\" => \"<span class=sf-dump-str title=\"5 characters\">task2</span>\"\n                  \"<span class=sf-dump-key>time_limit</span>\" => <span class=sf-dump-num>40</span>\n                  \"<span class=sf-dump-key>overall_band_score</span>\" => \"<span class=sf-dump-str title=\"3 characters\">5.0</span>\"\n                  \"<span class=sf-dump-key>task_achievement</span>\" => \"<span class=sf-dump-str title=\"3 characters\">5.1</span>\"\n                  \"<span class=sf-dump-key>coherence_cohesion</span>\" => \"<span class=sf-dump-str title=\"3 characters\">5.2</span>\"\n                  \"<span class=sf-dump-key>lexical_resource</span>\" => \"<span class=sf-dump-str title=\"3 characters\">5.0</span>\"\n                  \"<span class=sf-dump-key>grammar_accuracy</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.8</span>\"\n                  \"<span class=sf-dump-key>detailed_feedback</span>\" => \"<span class=sf-dump-str title=\"208 characters\">{&quot;task_achievement&quot;: {&quot;score&quot;: 5, &quot;issues&quot;: [&quot;&#272;&#226;y l&#224; ph&#7843;n h&#7891;i demo&quot;], &quot;feedback&quot;: &quot;H&#7879; th&#7889;ng &#273;ang trong ch&#7871; &#273;&#7897; demo. &#272;&#226;y l&#224; k&#7871;t qu&#7843; m&#7851;u.&quot;, &quot;improvements&quot;: [&quot;Vui l&#242;ng th&#7917; l&#7841;i sau khi h&#7879; th&#7889;ng &#273;&#432;&#7907;c kh&#244;i ph&#7909;c&quot;]}}</span>\"\n                  \"<span class=sf-dump-key>highlighted_corrections</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n                  \"<span class=sf-dump-key>criteria_analysis</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n                  \"<span class=sf-dump-key>statistics</span>\" => \"<span class=sf-dump-str title=\"152 characters\">{&quot;total_errors&quot;: 7, &quot;grammar_errors&quot;: 6, &quot;spelling_errors&quot;: 2, &quot;complexity_level&quot;: &quot;intermediate&quot;, &quot;sentence_variety&quot;: &quot;medium&quot;, &quot;vocabulary_errors&quot;: 1}</span>\"\n                  \"<span class=sf-dump-key>word_count</span>\" => <span class=sf-dump-num>179</span>\n                  \"<span class=sf-dump-key>essay_length</span>\" => <span class=sf-dump-num>1184</span>\n                  \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"9 characters\">completed</span>\"\n                  \"<span class=sf-dump-key>error_message</span>\" => <span class=sf-dump-const>null</span>\n                  \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-18 14:56:40</span>\"\n                  \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-18 14:56:44</span>\"\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:21</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>5</span>\n                  \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-num>1</span>\n                  \"<span class=sf-dump-key>essay_question_id</span>\" => <span class=sf-dump-const>null</span>\n                  \"<span class=sf-dump-key>essay_content</span>\" => \"\"\"\n                    <span class=sf-dump-str title=\"1184 characters\">International news is good for student but sometime it is not nessessary. Many people think student in secondary school need to study about international news, other think this waste their time. My essay will write about both of these thing.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">Firstly, International news is good because it make student smart. When student learn international news they can know many things. Example, student can know weather in other country, famous people life, or footbal match. They will very interesting about these topic and can relax in the classroom. Learning international news is also help student speaking english well.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">However, I thinking international news sometime is boring. Student have many important subject, example maths, literature, or chemistry. These subjects can help student become doctor or engineer in future. International news not really important, only waste of time.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">In my opinion, secondary student should play sport and learn math better than learn international news. Teacher should teaching student good subject not international news.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">I conclusion, international news have advantage but more disadvantage. Student no need study this subject, waste of their time.</span>\n                    \"\"\"\n                  \"<span class=sf-dump-key>task_type</span>\" => \"<span class=sf-dump-str title=\"5 characters\">task2</span>\"\n                  \"<span class=sf-dump-key>time_limit</span>\" => <span class=sf-dump-num>40</span>\n                  \"<span class=sf-dump-key>overall_band_score</span>\" => \"<span class=sf-dump-str title=\"3 characters\">5.0</span>\"\n                  \"<span class=sf-dump-key>task_achievement</span>\" => \"<span class=sf-dump-str title=\"3 characters\">5.1</span>\"\n                  \"<span class=sf-dump-key>coherence_cohesion</span>\" => \"<span class=sf-dump-str title=\"3 characters\">5.2</span>\"\n                  \"<span class=sf-dump-key>lexical_resource</span>\" => \"<span class=sf-dump-str title=\"3 characters\">5.0</span>\"\n                  \"<span class=sf-dump-key>grammar_accuracy</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.8</span>\"\n                  \"<span class=sf-dump-key>detailed_feedback</span>\" => \"<span class=sf-dump-str title=\"208 characters\">{&quot;task_achievement&quot;: {&quot;score&quot;: 5, &quot;issues&quot;: [&quot;&#272;&#226;y l&#224; ph&#7843;n h&#7891;i demo&quot;], &quot;feedback&quot;: &quot;H&#7879; th&#7889;ng &#273;ang trong ch&#7871; &#273;&#7897; demo. &#272;&#226;y l&#224; k&#7871;t qu&#7843; m&#7851;u.&quot;, &quot;improvements&quot;: [&quot;Vui l&#242;ng th&#7917; l&#7841;i sau khi h&#7879; th&#7889;ng &#273;&#432;&#7907;c kh&#244;i ph&#7909;c&quot;]}}</span>\"\n                  \"<span class=sf-dump-key>highlighted_corrections</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n                  \"<span class=sf-dump-key>criteria_analysis</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n                  \"<span class=sf-dump-key>statistics</span>\" => \"<span class=sf-dump-str title=\"152 characters\">{&quot;total_errors&quot;: 7, &quot;grammar_errors&quot;: 6, &quot;spelling_errors&quot;: 2, &quot;complexity_level&quot;: &quot;intermediate&quot;, &quot;sentence_variety&quot;: &quot;medium&quot;, &quot;vocabulary_errors&quot;: 1}</span>\"\n                  \"<span class=sf-dump-key>word_count</span>\" => <span class=sf-dump-num>179</span>\n                  \"<span class=sf-dump-key>essay_length</span>\" => <span class=sf-dump-num>1184</span>\n                  \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"9 characters\">completed</span>\"\n                  \"<span class=sf-dump-key>error_message</span>\" => <span class=sf-dump-const>null</span>\n                  \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-18 14:56:40</span>\"\n                  \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-18 14:56:44</span>\"\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">previous</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:9</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>detailed_feedback</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                  \"<span class=sf-dump-key>highlighted_corrections</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                  \"<span class=sf-dump-key>criteria_analysis</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                  \"<span class=sf-dump-key>statistics</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                  \"<span class=sf-dump-key>overall_band_score</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:1</span>\"\n                  \"<span class=sf-dump-key>task_achievement</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:1</span>\"\n                  \"<span class=sf-dump-key>coherence_cohesion</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:1</span>\"\n                  \"<span class=sf-dump-key>lexical_resource</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:1</span>\"\n                  \"<span class=sf-dump-key>grammar_accuracy</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:1</span>\"\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>essayQuestion</span>\" => <span class=sf-dump-const>null</span>\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadCallback</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadContext</span>: <span class=sf-dump-const>null</span>\n                +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:18</span> [<samp data-depth=9 class=sf-dump-compact>\n                  <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">user_id</span>\"\n                  <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"17 characters\">essay_question_id</span>\"\n                  <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"13 characters\">essay_content</span>\"\n                  <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"9 characters\">task_type</span>\"\n                  <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"10 characters\">time_limit</span>\"\n                  <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"18 characters\">overall_band_score</span>\"\n                  <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"16 characters\">task_achievement</span>\"\n                  <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"18 characters\">coherence_cohesion</span>\"\n                  <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"16 characters\">lexical_resource</span>\"\n                  <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"16 characters\">grammar_accuracy</span>\"\n                  <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"17 characters\">detailed_feedback</span>\"\n                  <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"23 characters\">highlighted_corrections</span>\"\n                  <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"17 characters\">criteria_analysis</span>\"\n                  <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"10 characters\">statistics</span>\"\n                  <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"10 characters\">word_count</span>\"\n                  <span class=sf-dump-index>15</span> => \"<span class=sf-dump-str title=\"12 characters\">essay_length</span>\"\n                  <span class=sf-dump-index>16</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n                  <span class=sf-dump-index>17</span> => \"<span class=sf-dump-str title=\"13 characters\">error_message</span>\"\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=9 class=sf-dump-compact>\n                  <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n                </samp>]\n              </samp>}\n              <span class=sf-dump-index>2</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\ScoringAttempt\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ScoringAttempt</span></span> {<a class=sf-dump-ref>#1487</a><samp data-depth=8 class=sf-dump-compact>\n                #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"16 characters\">scoring_attempts</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:21</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>4</span>\n                  \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-num>1</span>\n                  \"<span class=sf-dump-key>essay_question_id</span>\" => <span class=sf-dump-const>null</span>\n                  \"<span class=sf-dump-key>essay_content</span>\" => \"\"\"\n                    <span class=sf-dump-str title=\"1184 characters\">International news is good for student but sometime it is not nessessary. Many people think student in secondary school need to study about international news, other think this waste their time. My essay will write about both of these thing.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">Firstly, International news is good because it make student smart. When student learn international news they can know many things. Example, student can know weather in other country, famous people life, or footbal match. They will very interesting about these topic and can relax in the classroom. Learning international news is also help student speaking english well.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">However, I thinking international news sometime is boring. Student have many important subject, example maths, literature, or chemistry. These subjects can help student become doctor or engineer in future. International news not really important, only waste of time.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">In my opinion, secondary student should play sport and learn math better than learn international news. Teacher should teaching student good subject not international news.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">I conclusion, international news have advantage but more disadvantage. Student no need study this subject, waste of their time.</span>\n                    \"\"\"\n                  \"<span class=sf-dump-key>task_type</span>\" => \"<span class=sf-dump-str title=\"5 characters\">task2</span>\"\n                  \"<span class=sf-dump-key>time_limit</span>\" => <span class=sf-dump-num>40</span>\n                  \"<span class=sf-dump-key>overall_band_score</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.4</span>\"\n                  \"<span class=sf-dump-key>task_achievement</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.1</span>\"\n                  \"<span class=sf-dump-key>coherence_cohesion</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.4</span>\"\n                  \"<span class=sf-dump-key>lexical_resource</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.6</span>\"\n                  \"<span class=sf-dump-key>grammar_accuracy</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.3</span>\"\n                  \"<span class=sf-dump-key>detailed_feedback</span>\" => \"<span class=sf-dump-str title=\"210 characters\">{&quot;task_achievement&quot;: {&quot;score&quot;: 4.4, &quot;issues&quot;: [&quot;&#272;&#226;y l&#224; ph&#7843;n h&#7891;i demo&quot;], &quot;feedback&quot;: &quot;H&#7879; th&#7889;ng &#273;ang trong ch&#7871; &#273;&#7897; demo. &#272;&#226;y l&#224; k&#7871;t qu&#7843; m&#7851;u.&quot;, &quot;improvements&quot;: [&quot;Vui l&#242;ng th&#7917; l&#7841;i sau khi h&#7879; th&#7889;ng &#273;&#432;&#7907;c kh&#244;i ph&#7909;c&quot;]}}</span>\"\n                  \"<span class=sf-dump-key>highlighted_corrections</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n                  \"<span class=sf-dump-key>criteria_analysis</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n                  \"<span class=sf-dump-key>statistics</span>\" => \"<span class=sf-dump-str title=\"153 characters\">{&quot;total_errors&quot;: 10, &quot;grammar_errors&quot;: 4, &quot;spelling_errors&quot;: 4, &quot;complexity_level&quot;: &quot;intermediate&quot;, &quot;sentence_variety&quot;: &quot;medium&quot;, &quot;vocabulary_errors&quot;: 2}</span>\"\n                  \"<span class=sf-dump-key>word_count</span>\" => <span class=sf-dump-num>179</span>\n                  \"<span class=sf-dump-key>essay_length</span>\" => <span class=sf-dump-num>1184</span>\n                  \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"9 characters\">completed</span>\"\n                  \"<span class=sf-dump-key>error_message</span>\" => <span class=sf-dump-const>null</span>\n                  \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-18 14:54:37</span>\"\n                  \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-18 14:54:41</span>\"\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:21</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>4</span>\n                  \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-num>1</span>\n                  \"<span class=sf-dump-key>essay_question_id</span>\" => <span class=sf-dump-const>null</span>\n                  \"<span class=sf-dump-key>essay_content</span>\" => \"\"\"\n                    <span class=sf-dump-str title=\"1184 characters\">International news is good for student but sometime it is not nessessary. Many people think student in secondary school need to study about international news, other think this waste their time. My essay will write about both of these thing.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">Firstly, International news is good because it make student smart. When student learn international news they can know many things. Example, student can know weather in other country, famous people life, or footbal match. They will very interesting about these topic and can relax in the classroom. Learning international news is also help student speaking english well.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">However, I thinking international news sometime is boring. Student have many important subject, example maths, literature, or chemistry. These subjects can help student become doctor or engineer in future. International news not really important, only waste of time.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">In my opinion, secondary student should play sport and learn math better than learn international news. Teacher should teaching student good subject not international news.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">I conclusion, international news have advantage but more disadvantage. Student no need study this subject, waste of their time.</span>\n                    \"\"\"\n                  \"<span class=sf-dump-key>task_type</span>\" => \"<span class=sf-dump-str title=\"5 characters\">task2</span>\"\n                  \"<span class=sf-dump-key>time_limit</span>\" => <span class=sf-dump-num>40</span>\n                  \"<span class=sf-dump-key>overall_band_score</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.4</span>\"\n                  \"<span class=sf-dump-key>task_achievement</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.1</span>\"\n                  \"<span class=sf-dump-key>coherence_cohesion</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.4</span>\"\n                  \"<span class=sf-dump-key>lexical_resource</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.6</span>\"\n                  \"<span class=sf-dump-key>grammar_accuracy</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.3</span>\"\n                  \"<span class=sf-dump-key>detailed_feedback</span>\" => \"<span class=sf-dump-str title=\"210 characters\">{&quot;task_achievement&quot;: {&quot;score&quot;: 4.4, &quot;issues&quot;: [&quot;&#272;&#226;y l&#224; ph&#7843;n h&#7891;i demo&quot;], &quot;feedback&quot;: &quot;H&#7879; th&#7889;ng &#273;ang trong ch&#7871; &#273;&#7897; demo. &#272;&#226;y l&#224; k&#7871;t qu&#7843; m&#7851;u.&quot;, &quot;improvements&quot;: [&quot;Vui l&#242;ng th&#7917; l&#7841;i sau khi h&#7879; th&#7889;ng &#273;&#432;&#7907;c kh&#244;i ph&#7909;c&quot;]}}</span>\"\n                  \"<span class=sf-dump-key>highlighted_corrections</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n                  \"<span class=sf-dump-key>criteria_analysis</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n                  \"<span class=sf-dump-key>statistics</span>\" => \"<span class=sf-dump-str title=\"153 characters\">{&quot;total_errors&quot;: 10, &quot;grammar_errors&quot;: 4, &quot;spelling_errors&quot;: 4, &quot;complexity_level&quot;: &quot;intermediate&quot;, &quot;sentence_variety&quot;: &quot;medium&quot;, &quot;vocabulary_errors&quot;: 2}</span>\"\n                  \"<span class=sf-dump-key>word_count</span>\" => <span class=sf-dump-num>179</span>\n                  \"<span class=sf-dump-key>essay_length</span>\" => <span class=sf-dump-num>1184</span>\n                  \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"9 characters\">completed</span>\"\n                  \"<span class=sf-dump-key>error_message</span>\" => <span class=sf-dump-const>null</span>\n                  \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-18 14:54:37</span>\"\n                  \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-18 14:54:41</span>\"\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">previous</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:9</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>detailed_feedback</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                  \"<span class=sf-dump-key>highlighted_corrections</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                  \"<span class=sf-dump-key>criteria_analysis</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                  \"<span class=sf-dump-key>statistics</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                  \"<span class=sf-dump-key>overall_band_score</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:1</span>\"\n                  \"<span class=sf-dump-key>task_achievement</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:1</span>\"\n                  \"<span class=sf-dump-key>coherence_cohesion</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:1</span>\"\n                  \"<span class=sf-dump-key>lexical_resource</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:1</span>\"\n                  \"<span class=sf-dump-key>grammar_accuracy</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:1</span>\"\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>essayQuestion</span>\" => <span class=sf-dump-const>null</span>\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadCallback</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadContext</span>: <span class=sf-dump-const>null</span>\n                +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:18</span> [<samp data-depth=9 class=sf-dump-compact>\n                  <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">user_id</span>\"\n                  <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"17 characters\">essay_question_id</span>\"\n                  <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"13 characters\">essay_content</span>\"\n                  <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"9 characters\">task_type</span>\"\n                  <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"10 characters\">time_limit</span>\"\n                  <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"18 characters\">overall_band_score</span>\"\n                  <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"16 characters\">task_achievement</span>\"\n                  <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"18 characters\">coherence_cohesion</span>\"\n                  <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"16 characters\">lexical_resource</span>\"\n                  <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"16 characters\">grammar_accuracy</span>\"\n                  <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"17 characters\">detailed_feedback</span>\"\n                  <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"23 characters\">highlighted_corrections</span>\"\n                  <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"17 characters\">criteria_analysis</span>\"\n                  <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"10 characters\">statistics</span>\"\n                  <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"10 characters\">word_count</span>\"\n                  <span class=sf-dump-index>15</span> => \"<span class=sf-dump-str title=\"12 characters\">essay_length</span>\"\n                  <span class=sf-dump-index>16</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n                  <span class=sf-dump-index>17</span> => \"<span class=sf-dump-str title=\"13 characters\">error_message</span>\"\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=9 class=sf-dump-compact>\n                  <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n                </samp>]\n              </samp>}\n              <span class=sf-dump-index>3</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\ScoringAttempt\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ScoringAttempt</span></span> {<a class=sf-dump-ref>#1486</a><samp data-depth=8 class=sf-dump-compact>\n                #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"16 characters\">scoring_attempts</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:21</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>2</span>\n                  \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-num>1</span>\n                  \"<span class=sf-dump-key>essay_question_id</span>\" => <span class=sf-dump-const>null</span>\n                  \"<span class=sf-dump-key>essay_content</span>\" => \"\"\"\n                    <span class=sf-dump-str title=\"1184 characters\">International news is good for student but sometime it is not nessessary. Many people think student in secondary school need to study about international news, other think this waste their time. My essay will write about both of these thing.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">Firstly, International news is good because it make student smart. When student learn international news they can know many things. Example, student can know weather in other country, famous people life, or footbal match. They will very interesting about these topic and can relax in the classroom. Learning international news is also help student speaking english well.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">However, I thinking international news sometime is boring. Student have many important subject, example maths, literature, or chemistry. These subjects can help student become doctor or engineer in future. International news not really important, only waste of time.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">In my opinion, secondary student should play sport and learn math better than learn international news. Teacher should teaching student good subject not international news.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">I conclusion, international news have advantage but more disadvantage. Student no need study this subject, waste of their time.</span>\n                    \"\"\"\n                  \"<span class=sf-dump-key>task_type</span>\" => \"<span class=sf-dump-str title=\"5 characters\">task2</span>\"\n                  \"<span class=sf-dump-key>time_limit</span>\" => <span class=sf-dump-num>40</span>\n                  \"<span class=sf-dump-key>overall_band_score</span>\" => \"<span class=sf-dump-str title=\"3 characters\">3.9</span>\"\n                  \"<span class=sf-dump-key>task_achievement</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.2</span>\"\n                  \"<span class=sf-dump-key>coherence_cohesion</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.0</span>\"\n                  \"<span class=sf-dump-key>lexical_resource</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.1</span>\"\n                  \"<span class=sf-dump-key>grammar_accuracy</span>\" => \"<span class=sf-dump-str title=\"3 characters\">3.8</span>\"\n                  \"<span class=sf-dump-key>detailed_feedback</span>\" => \"<span class=sf-dump-str title=\"210 characters\">{&quot;task_achievement&quot;: {&quot;score&quot;: 3.9, &quot;issues&quot;: [&quot;&#272;&#226;y l&#224; ph&#7843;n h&#7891;i demo&quot;], &quot;feedback&quot;: &quot;H&#7879; th&#7889;ng &#273;ang trong ch&#7871; &#273;&#7897; demo. &#272;&#226;y l&#224; k&#7871;t qu&#7843; m&#7851;u.&quot;, &quot;improvements&quot;: [&quot;Vui l&#242;ng th&#7917; l&#7841;i sau khi h&#7879; th&#7889;ng &#273;&#432;&#7907;c kh&#244;i ph&#7909;c&quot;]}}</span>\"\n                  \"<span class=sf-dump-key>highlighted_corrections</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n                  \"<span class=sf-dump-key>criteria_analysis</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n                  \"<span class=sf-dump-key>statistics</span>\" => \"<span class=sf-dump-str title=\"152 characters\">{&quot;total_errors&quot;: 8, &quot;grammar_errors&quot;: 3, &quot;spelling_errors&quot;: 5, &quot;complexity_level&quot;: &quot;intermediate&quot;, &quot;sentence_variety&quot;: &quot;medium&quot;, &quot;vocabulary_errors&quot;: 2}</span>\"\n                  \"<span class=sf-dump-key>word_count</span>\" => <span class=sf-dump-num>179</span>\n                  \"<span class=sf-dump-key>essay_length</span>\" => <span class=sf-dump-num>1184</span>\n                  \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"9 characters\">completed</span>\"\n                  \"<span class=sf-dump-key>error_message</span>\" => <span class=sf-dump-const>null</span>\n                  \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-18 14:46:07</span>\"\n                  \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-18 14:46:11</span>\"\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:21</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>2</span>\n                  \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-num>1</span>\n                  \"<span class=sf-dump-key>essay_question_id</span>\" => <span class=sf-dump-const>null</span>\n                  \"<span class=sf-dump-key>essay_content</span>\" => \"\"\"\n                    <span class=sf-dump-str title=\"1184 characters\">International news is good for student but sometime it is not nessessary. Many people think student in secondary school need to study about international news, other think this waste their time. My essay will write about both of these thing.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">Firstly, International news is good because it make student smart. When student learn international news they can know many things. Example, student can know weather in other country, famous people life, or footbal match. They will very interesting about these topic and can relax in the classroom. Learning international news is also help student speaking english well.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">However, I thinking international news sometime is boring. Student have many important subject, example maths, literature, or chemistry. These subjects can help student become doctor or engineer in future. International news not really important, only waste of time.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">In my opinion, secondary student should play sport and learn math better than learn international news. Teacher should teaching student good subject not international news.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">I conclusion, international news have advantage but more disadvantage. Student no need study this subject, waste of their time.</span>\n                    \"\"\"\n                  \"<span class=sf-dump-key>task_type</span>\" => \"<span class=sf-dump-str title=\"5 characters\">task2</span>\"\n                  \"<span class=sf-dump-key>time_limit</span>\" => <span class=sf-dump-num>40</span>\n                  \"<span class=sf-dump-key>overall_band_score</span>\" => \"<span class=sf-dump-str title=\"3 characters\">3.9</span>\"\n                  \"<span class=sf-dump-key>task_achievement</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.2</span>\"\n                  \"<span class=sf-dump-key>coherence_cohesion</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.0</span>\"\n                  \"<span class=sf-dump-key>lexical_resource</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.1</span>\"\n                  \"<span class=sf-dump-key>grammar_accuracy</span>\" => \"<span class=sf-dump-str title=\"3 characters\">3.8</span>\"\n                  \"<span class=sf-dump-key>detailed_feedback</span>\" => \"<span class=sf-dump-str title=\"210 characters\">{&quot;task_achievement&quot;: {&quot;score&quot;: 3.9, &quot;issues&quot;: [&quot;&#272;&#226;y l&#224; ph&#7843;n h&#7891;i demo&quot;], &quot;feedback&quot;: &quot;H&#7879; th&#7889;ng &#273;ang trong ch&#7871; &#273;&#7897; demo. &#272;&#226;y l&#224; k&#7871;t qu&#7843; m&#7851;u.&quot;, &quot;improvements&quot;: [&quot;Vui l&#242;ng th&#7917; l&#7841;i sau khi h&#7879; th&#7889;ng &#273;&#432;&#7907;c kh&#244;i ph&#7909;c&quot;]}}</span>\"\n                  \"<span class=sf-dump-key>highlighted_corrections</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n                  \"<span class=sf-dump-key>criteria_analysis</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n                  \"<span class=sf-dump-key>statistics</span>\" => \"<span class=sf-dump-str title=\"152 characters\">{&quot;total_errors&quot;: 8, &quot;grammar_errors&quot;: 3, &quot;spelling_errors&quot;: 5, &quot;complexity_level&quot;: &quot;intermediate&quot;, &quot;sentence_variety&quot;: &quot;medium&quot;, &quot;vocabulary_errors&quot;: 2}</span>\"\n                  \"<span class=sf-dump-key>word_count</span>\" => <span class=sf-dump-num>179</span>\n                  \"<span class=sf-dump-key>essay_length</span>\" => <span class=sf-dump-num>1184</span>\n                  \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"9 characters\">completed</span>\"\n                  \"<span class=sf-dump-key>error_message</span>\" => <span class=sf-dump-const>null</span>\n                  \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-18 14:46:07</span>\"\n                  \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-18 14:46:11</span>\"\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">previous</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:9</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>detailed_feedback</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                  \"<span class=sf-dump-key>highlighted_corrections</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                  \"<span class=sf-dump-key>criteria_analysis</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                  \"<span class=sf-dump-key>statistics</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                  \"<span class=sf-dump-key>overall_band_score</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:1</span>\"\n                  \"<span class=sf-dump-key>task_achievement</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:1</span>\"\n                  \"<span class=sf-dump-key>coherence_cohesion</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:1</span>\"\n                  \"<span class=sf-dump-key>lexical_resource</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:1</span>\"\n                  \"<span class=sf-dump-key>grammar_accuracy</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:1</span>\"\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>essayQuestion</span>\" => <span class=sf-dump-const>null</span>\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadCallback</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadContext</span>: <span class=sf-dump-const>null</span>\n                +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:18</span> [<samp data-depth=9 class=sf-dump-compact>\n                  <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">user_id</span>\"\n                  <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"17 characters\">essay_question_id</span>\"\n                  <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"13 characters\">essay_content</span>\"\n                  <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"9 characters\">task_type</span>\"\n                  <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"10 characters\">time_limit</span>\"\n                  <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"18 characters\">overall_band_score</span>\"\n                  <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"16 characters\">task_achievement</span>\"\n                  <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"18 characters\">coherence_cohesion</span>\"\n                  <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"16 characters\">lexical_resource</span>\"\n                  <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"16 characters\">grammar_accuracy</span>\"\n                  <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"17 characters\">detailed_feedback</span>\"\n                  <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"23 characters\">highlighted_corrections</span>\"\n                  <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"17 characters\">criteria_analysis</span>\"\n                  <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"10 characters\">statistics</span>\"\n                  <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"10 characters\">word_count</span>\"\n                  <span class=sf-dump-index>15</span> => \"<span class=sf-dump-str title=\"12 characters\">essay_length</span>\"\n                  <span class=sf-dump-index>16</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n                  <span class=sf-dump-index>17</span> => \"<span class=sf-dump-str title=\"13 characters\">error_message</span>\"\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=9 class=sf-dump-compact>\n                  <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n                </samp>]\n              </samp>}\n              <span class=sf-dump-index>4</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\ScoringAttempt\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ScoringAttempt</span></span> {<a class=sf-dump-ref>#1485</a><samp data-depth=8 class=sf-dump-compact>\n                #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"16 characters\">scoring_attempts</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:21</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n                  \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-num>1</span>\n                  \"<span class=sf-dump-key>essay_question_id</span>\" => <span class=sf-dump-const>null</span>\n                  \"<span class=sf-dump-key>essay_content</span>\" => \"\"\"\n                    <span class=sf-dump-str title=\"1184 characters\">International news is good for student but sometime it is not nessessary. Many people think student in secondary school need to study about international news, other think this waste their time. My essay will write about both of these thing.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">Firstly, International news is good because it make student smart. When student learn international news they can know many things. Example, student can know weather in other country, famous people life, or footbal match. They will very interesting about these topic and can relax in the classroom. Learning international news is also help student speaking english well.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">However, I thinking international news sometime is boring. Student have many important subject, example maths, literature, or chemistry. These subjects can help student become doctor or engineer in future. International news not really important, only waste of time.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">In my opinion, secondary student should play sport and learn math better than learn international news. Teacher should teaching student good subject not international news.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">I conclusion, international news have advantage but more disadvantage. Student no need study this subject, waste of their time.</span>\n                    \"\"\"\n                  \"<span class=sf-dump-key>task_type</span>\" => \"<span class=sf-dump-str title=\"5 characters\">task2</span>\"\n                  \"<span class=sf-dump-key>time_limit</span>\" => <span class=sf-dump-num>40</span>\n                  \"<span class=sf-dump-key>overall_band_score</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.7</span>\"\n                  \"<span class=sf-dump-key>task_achievement</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.4</span>\"\n                  \"<span class=sf-dump-key>coherence_cohesion</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.5</span>\"\n                  \"<span class=sf-dump-key>lexical_resource</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.8</span>\"\n                  \"<span class=sf-dump-key>grammar_accuracy</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.4</span>\"\n                  \"<span class=sf-dump-key>detailed_feedback</span>\" => \"<span class=sf-dump-str title=\"210 characters\">{&quot;task_achievement&quot;: {&quot;score&quot;: 4.7, &quot;issues&quot;: [&quot;&#272;&#226;y l&#224; ph&#7843;n h&#7891;i demo&quot;], &quot;feedback&quot;: &quot;H&#7879; th&#7889;ng &#273;ang trong ch&#7871; &#273;&#7897; demo. &#272;&#226;y l&#224; k&#7871;t qu&#7843; m&#7851;u.&quot;, &quot;improvements&quot;: [&quot;Vui l&#242;ng th&#7917; l&#7841;i sau khi h&#7879; th&#7889;ng &#273;&#432;&#7907;c kh&#244;i ph&#7909;c&quot;]}}</span>\"\n                  \"<span class=sf-dump-key>highlighted_corrections</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n                  \"<span class=sf-dump-key>criteria_analysis</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n                  \"<span class=sf-dump-key>statistics</span>\" => \"<span class=sf-dump-str title=\"153 characters\">{&quot;total_errors&quot;: 12, &quot;grammar_errors&quot;: 7, &quot;spelling_errors&quot;: 4, &quot;complexity_level&quot;: &quot;intermediate&quot;, &quot;sentence_variety&quot;: &quot;medium&quot;, &quot;vocabulary_errors&quot;: 2}</span>\"\n                  \"<span class=sf-dump-key>word_count</span>\" => <span class=sf-dump-num>179</span>\n                  \"<span class=sf-dump-key>essay_length</span>\" => <span class=sf-dump-num>1184</span>\n                  \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"9 characters\">completed</span>\"\n                  \"<span class=sf-dump-key>error_message</span>\" => <span class=sf-dump-const>null</span>\n                  \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-18 14:44:34</span>\"\n                  \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-18 14:44:38</span>\"\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:21</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n                  \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-num>1</span>\n                  \"<span class=sf-dump-key>essay_question_id</span>\" => <span class=sf-dump-const>null</span>\n                  \"<span class=sf-dump-key>essay_content</span>\" => \"\"\"\n                    <span class=sf-dump-str title=\"1184 characters\">International news is good for student but sometime it is not nessessary. Many people think student in secondary school need to study about international news, other think this waste their time. My essay will write about both of these thing.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">Firstly, International news is good because it make student smart. When student learn international news they can know many things. Example, student can know weather in other country, famous people life, or footbal match. They will very interesting about these topic and can relax in the classroom. Learning international news is also help student speaking english well.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">However, I thinking international news sometime is boring. Student have many important subject, example maths, literature, or chemistry. These subjects can help student become doctor or engineer in future. International news not really important, only waste of time.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">In my opinion, secondary student should play sport and learn math better than learn international news. Teacher should teaching student good subject not international news.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">I conclusion, international news have advantage but more disadvantage. Student no need study this subject, waste of their time.</span>\n                    \"\"\"\n                  \"<span class=sf-dump-key>task_type</span>\" => \"<span class=sf-dump-str title=\"5 characters\">task2</span>\"\n                  \"<span class=sf-dump-key>time_limit</span>\" => <span class=sf-dump-num>40</span>\n                  \"<span class=sf-dump-key>overall_band_score</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.7</span>\"\n                  \"<span class=sf-dump-key>task_achievement</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.4</span>\"\n                  \"<span class=sf-dump-key>coherence_cohesion</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.5</span>\"\n                  \"<span class=sf-dump-key>lexical_resource</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.8</span>\"\n                  \"<span class=sf-dump-key>grammar_accuracy</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.4</span>\"\n                  \"<span class=sf-dump-key>detailed_feedback</span>\" => \"<span class=sf-dump-str title=\"210 characters\">{&quot;task_achievement&quot;: {&quot;score&quot;: 4.7, &quot;issues&quot;: [&quot;&#272;&#226;y l&#224; ph&#7843;n h&#7891;i demo&quot;], &quot;feedback&quot;: &quot;H&#7879; th&#7889;ng &#273;ang trong ch&#7871; &#273;&#7897; demo. &#272;&#226;y l&#224; k&#7871;t qu&#7843; m&#7851;u.&quot;, &quot;improvements&quot;: [&quot;Vui l&#242;ng th&#7917; l&#7841;i sau khi h&#7879; th&#7889;ng &#273;&#432;&#7907;c kh&#244;i ph&#7909;c&quot;]}}</span>\"\n                  \"<span class=sf-dump-key>highlighted_corrections</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n                  \"<span class=sf-dump-key>criteria_analysis</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n                  \"<span class=sf-dump-key>statistics</span>\" => \"<span class=sf-dump-str title=\"153 characters\">{&quot;total_errors&quot;: 12, &quot;grammar_errors&quot;: 7, &quot;spelling_errors&quot;: 4, &quot;complexity_level&quot;: &quot;intermediate&quot;, &quot;sentence_variety&quot;: &quot;medium&quot;, &quot;vocabulary_errors&quot;: 2}</span>\"\n                  \"<span class=sf-dump-key>word_count</span>\" => <span class=sf-dump-num>179</span>\n                  \"<span class=sf-dump-key>essay_length</span>\" => <span class=sf-dump-num>1184</span>\n                  \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"9 characters\">completed</span>\"\n                  \"<span class=sf-dump-key>error_message</span>\" => <span class=sf-dump-const>null</span>\n                  \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-18 14:44:34</span>\"\n                  \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-18 14:44:38</span>\"\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">previous</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:9</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>detailed_feedback</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                  \"<span class=sf-dump-key>highlighted_corrections</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                  \"<span class=sf-dump-key>criteria_analysis</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                  \"<span class=sf-dump-key>statistics</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                  \"<span class=sf-dump-key>overall_band_score</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:1</span>\"\n                  \"<span class=sf-dump-key>task_achievement</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:1</span>\"\n                  \"<span class=sf-dump-key>coherence_cohesion</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:1</span>\"\n                  \"<span class=sf-dump-key>lexical_resource</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:1</span>\"\n                  \"<span class=sf-dump-key>grammar_accuracy</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:1</span>\"\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>essayQuestion</span>\" => <span class=sf-dump-const>null</span>\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadCallback</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadContext</span>: <span class=sf-dump-const>null</span>\n                +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:18</span> [<samp data-depth=9 class=sf-dump-compact>\n                  <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">user_id</span>\"\n                  <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"17 characters\">essay_question_id</span>\"\n                  <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"13 characters\">essay_content</span>\"\n                  <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"9 characters\">task_type</span>\"\n                  <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"10 characters\">time_limit</span>\"\n                  <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"18 characters\">overall_band_score</span>\"\n                  <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"16 characters\">task_achievement</span>\"\n                  <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"18 characters\">coherence_cohesion</span>\"\n                  <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"16 characters\">lexical_resource</span>\"\n                  <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"16 characters\">grammar_accuracy</span>\"\n                  <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"17 characters\">detailed_feedback</span>\"\n                  <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"23 characters\">highlighted_corrections</span>\"\n                  <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"17 characters\">criteria_analysis</span>\"\n                  <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"10 characters\">statistics</span>\"\n                  <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"10 characters\">word_count</span>\"\n                  <span class=sf-dump-index>15</span> => \"<span class=sf-dump-str title=\"12 characters\">essay_length</span>\"\n                  <span class=sf-dump-index>16</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n                  <span class=sf-dump-index>17</span> => \"<span class=sf-dump-str title=\"13 characters\">error_message</span>\"\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=9 class=sf-dump-compact>\n                  <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n                </samp>]\n              </samp>}\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n          </samp>}\n          #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>10</span>\n          #<span class=sf-dump-protected title=\"Protected property\">currentPage</span>: <span class=sf-dump-num>1</span>\n          #<span class=sf-dump-protected title=\"Protected property\">path</span>: \"<span class=sf-dump-str title=\"37 characters\">http://127.0.0.1:8000/scoring/history</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">query</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">fragment</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">pageName</span>: \"<span class=sf-dump-str title=\"4 characters\">page</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n          +<span class=sf-dump-public title=\"Public property\">onEachSide</span>: <span class=sf-dump-num>3</span>\n          #<span class=sf-dump-protected title=\"Protected property\">options</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://127.0.0.1:8000/scoring/history</span>\"\n            \"<span class=sf-dump-key>pageName</span>\" => \"<span class=sf-dump-str title=\"4 characters\">page</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">total</span>: <span class=sf-dump-num>5</span>\n          #<span class=sf-dump-protected title=\"Protected property\">lastPage</span>: <span class=sf-dump-num>1</span>\n        </samp>}\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"53 characters\">vendor/laravel/framework/src/Illuminate/View/View.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"3 characters\">get</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"38 characters\">Illuminate\\View\\Engines\\CompilerEngine</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"83 characters\">C:\\laragon\\www\\ielts\\ielts-scoring-system\\resources\\views/scoring/history.blade.php</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>__env</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\View\\Factory\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\View</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Factory</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21305 title=\"2 occurrences\">#1305</a> &#8230;25}\n        \"<span class=sf-dump-key>app</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref24 title=\"2 occurrences\">#4</a> &#8230;44}\n        \"<span class=sf-dump-key>errors</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\ViewErrorBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ViewErrorBag</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21430 title=\"2 occurrences\">#1430</a>}\n        \"<span class=sf-dump-key>attempts</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Pagination\\LengthAwarePaginator\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Pagination</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">LengthAwarePaginator</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21484 title=\"2 occurrences\">#1484</a>}\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"53 characters\">vendor/laravel/framework/src/Illuminate/View/View.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>191</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"11 characters\">getContents</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Illuminate\\View\\View</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"53 characters\">vendor/laravel/framework/src/Illuminate/View/View.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>160</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"14 characters\">renderContents</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Illuminate\\View\\View</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"57 characters\">vendor/laravel/framework/src/Illuminate/Http/Response.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>78</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">render</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Illuminate\\View\\View</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"57 characters\">vendor/laravel/framework/src/Illuminate/Http/Response.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>34</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"10 characters\">setContent</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Illuminate\\Http\\Response</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">[object Illuminate\\View\\View]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>924</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"11 characters\">__construct</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Illuminate\\Http\\Response</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">[object Illuminate\\View\\View]</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-num>200</span>\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>Content-Type</span>\" => \"<span class=sf-dump-str title=\"9 characters\">text/html</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>891</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"10 characters\">toResponse</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">::</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"29 characters\">[object Illuminate\\View\\View]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>807</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"15 characters\">prepareResponse</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"29 characters\">[object Illuminate\\View\\View]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>169</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Routing\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>11</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>66</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>12</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Barryvdh\\Debugbar\\Middleware\\InjectDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>13</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"43 characters\">app/Http/Middleware/EnsureUserHasCredit.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>32</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>14</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">App\\Http\\Middleware\\EnsureUserHasCredit</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>15</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"81 characters\">vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>50</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>16</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"48 characters\">Illuminate\\Routing\\Middleware\\SubstituteBindings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>17</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>63</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>18</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Auth\\Middleware\\Authenticate</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>19</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"86 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>87</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>20</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"53 characters\">Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>21</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>48</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>22</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\View\\Middleware\\ShareErrorsFromSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>23</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"75 characters\">vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>120</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>24</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"75 characters\">vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>63</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"21 characters\">handleStatefulRequest</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Illuminate\\Session\\Middleware\\StartSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Session\\Store]</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>25</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Illuminate\\Session\\Middleware\\StartSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>26</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>36</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>27</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>28</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"76 characters\">vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>74</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>29</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Illuminate\\Cookie\\Middleware\\EncryptCookies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>30</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>126</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>31</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>807</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>32</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>786</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"19 characters\">runRouteWithinStack</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>33</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>750</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">runRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>34</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>739</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"15 characters\">dispatchToRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>35</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>200</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">dispatch</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>36</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>169</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Illuminate\\Foundation\\Http\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>37</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>66</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>38</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Barryvdh\\Debugbar\\Middleware\\InjectDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>39</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>40</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"96 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>31</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>41</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"63 characters\">Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>42</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>43</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>51</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>44</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\Foundation\\Http\\Middleware\\TrimStrings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>45</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"76 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>27</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>46</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Illuminate\\Http\\Middleware\\ValidatePostSize</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>47</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"103 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>109</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>48</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"70 characters\">Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>49</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"70 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>48</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>50</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"37 characters\">Illuminate\\Http\\Middleware\\HandleCors</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>51</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>58</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>52</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Http\\Middleware\\TrustProxies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>53</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"94 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>22</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>54</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"61 characters\">Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>55</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"80 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePathEncoding.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>26</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>56</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"47 characters\">Illuminate\\Http\\Middleware\\ValidatePathEncoding</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>57</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>126</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>58</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>175</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>59</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>144</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"24 characters\">sendRequestThroughRouter</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>60</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Application.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>1219</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>61</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"16 characters\">public/index.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>20</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"13 characters\">handleRequest</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Application</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>62</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"71 characters\">vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>23</span>\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"58 characters\">C:\\laragon\\www\\ielts\\ielts-scoring-system\\public\\index.php</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"12 characters\">require_once</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "surrounding_lines": ["\n", "        $message .= '.';\n", "\n", "        return new static($message);\n", "    }\n", "}\n"], "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FRouting%2FExceptions%2FUrlGenerationException.php&line=35", "ajax": false, "filename": "UrlGenerationException.php", "line": "35"}}, {"type": "Illuminate\\Routing\\Exceptions\\UrlGenerationException", "message": "Missing required parameter for [Route: scoring.destroy] [URI: scoring/delete/{attempt}] [Missing parameter: attempt].", "code": 0, "file": "vendor/laravel/framework/src/Illuminate/Routing/Exceptions/UrlGenerationException.php", "line": 35, "stack_trace": null, "stack_trace_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:70</span> [<samp data-depth=1 class=sf-dump-expanded>\n  <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"69 characters\">vendor/laravel/framework/src/Illuminate/Routing/RouteUrlGenerator.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>94</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"20 characters\">forMissingParameters</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"52 characters\">Illuminate\\Routing\\Exceptions\\UrlGenerationException</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">::</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">attempt</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"64 characters\">vendor/laravel/framework/src/Illuminate/Routing/UrlGenerator.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>541</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"2 characters\">to</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Illuminate\\Routing\\RouteUrlGenerator</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>attempt</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-const>true</span>\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"64 characters\">vendor/laravel/framework/src/Illuminate/Routing/UrlGenerator.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>518</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"7 characters\">toRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"31 characters\">Illuminate\\Routing\\UrlGenerator</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"34 characters\">[object App\\Models\\ScoringAttempt]</span>\"\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-const>true</span>\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"62 characters\">vendor/laravel/framework/src/Illuminate/Foundation/helpers.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>883</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"5 characters\">route</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"31 characters\">Illuminate\\Routing\\UrlGenerator</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">scoring.destroy</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"34 characters\">[object App\\Models\\ScoringAttempt]</span>\"\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-const>true</span>\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"60 characters\">storage/framework/views/3d0e4b2b3d75180b3dbc322942eb1a4c.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>535</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"5 characters\">route</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">scoring.destroy</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"34 characters\">[object App\\Models\\ScoringAttempt]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"65 characters\">vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>123</span>\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"102 characters\">C:\\laragon\\www\\ielts\\ielts-scoring-system\\storage\\framework\\views\\3d0e4b2b3d75180b3dbc322942eb1a4c.php</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"7 characters\">require</span>\"\n  </samp>]\n  <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"65 characters\">vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>124</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"31 characters\">Illuminate\\Filesystem\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Illuminate\\Filesystem\\Filesystem</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">::</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>57</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"10 characters\">getRequire</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Illuminate\\Filesystem\\Filesystem</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"102 characters\">C:\\laragon\\www\\ielts\\ielts-scoring-system\\storage\\framework\\views/3d0e4b2b3d75180b3dbc322942eb1a4c.php</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>__env</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\View\\Factory\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\View</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Factory</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21305 title=\"3 occurrences\">#1305</a> &#8230;25}\n        \"<span class=sf-dump-key>app</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref24 title=\"3 occurrences\">#4</a> &#8230;44}\n        \"<span class=sf-dump-key>errors</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\ViewErrorBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ViewErrorBag</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21430 title=\"3 occurrences\">#1430</a><samp data-depth=5 id=sf-dump-**********-ref21430 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []\n        </samp>}\n        \"<span class=sf-dump-key>attempts</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Pagination\\LengthAwarePaginator\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Pagination</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">LengthAwarePaginator</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21484 title=\"3 occurrences\">#1484</a><samp data-depth=5 id=sf-dump-**********-ref21484 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#1456</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\ScoringAttempt\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ScoringAttempt</span></span> {<a class=sf-dump-ref>#1478</a><samp data-depth=8 class=sf-dump-compact>\n                #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"16 characters\">scoring_attempts</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:21</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>9</span>\n                  \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-num>1</span>\n                  \"<span class=sf-dump-key>essay_question_id</span>\" => <span class=sf-dump-const>null</span>\n                  \"<span class=sf-dump-key>essay_content</span>\" => \"\"\"\n                    <span class=sf-dump-str title=\"1184 characters\">International news is good for student but sometime it is not nessessary. Many people think student in secondary school need to study about international news, other think this waste their time. My essay will write about both of these thing.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">Firstly, International news is good because it make student smart. When student learn international news they can know many things. Example, student can know weather in other country, famous people life, or footbal match. They will very interesting about these topic and can relax in the classroom. Learning international news is also help student speaking english well.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">However, I thinking international news sometime is boring. Student have many important subject, example maths, literature, or chemistry. These subjects can help student become doctor or engineer in future. International news not really important, only waste of time.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">In my opinion, secondary student should play sport and learn math better than learn international news. Teacher should teaching student good subject not international news.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">I conclusion, international news have advantage but more disadvantage. Student no need study this subject, waste of their time.</span>\n                    \"\"\"\n                  \"<span class=sf-dump-key>task_type</span>\" => \"<span class=sf-dump-str title=\"5 characters\">task2</span>\"\n                  \"<span class=sf-dump-key>time_limit</span>\" => <span class=sf-dump-num>40</span>\n                  \"<span class=sf-dump-key>overall_band_score</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.0</span>\"\n                  \"<span class=sf-dump-key>task_achievement</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.0</span>\"\n                  \"<span class=sf-dump-key>coherence_cohesion</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.0</span>\"\n                  \"<span class=sf-dump-key>lexical_resource</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.0</span>\"\n                  \"<span class=sf-dump-key>grammar_accuracy</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.0</span>\"\n                  \"<span class=sf-dump-key>detailed_feedback</span>\" => \"<span class=sf-dump-str title=\"2086 characters\">{&quot;grammar_accuracy&quot;: {&quot;score&quot;: 4, &quot;issues&quot;: [&quot;S-V: \\&quot;news is &#8230; it make student\\&quot;, \\&quot;Learning international news is also help&#8230;\\&quot;&quot;, &quot;Thi&#7871;u m&#7841;o t&#7915;: \\&quot;in other country\\&quot;, \\&quot;famous people life\\&quot;&quot;], &quot;feedback&quot;: &quot;L&#7895;i S-V agreement, thi&#7871;u m&#7841;o t&#7915;, gi&#7899;i t&#7915;, th&#236;, c&#7845;u tr&#250;c c&#226;u h&#7847;u h&#7871;t &#7903; m&#7913;c &#273;&#417;n gi&#7843;n.&quot;, &quot;improvements&quot;: [&quot;&#212;n quy t&#7855;c S-V agreement, &#273;&#7863;c bi&#7879;t v&#7899;i danh t&#7915; s&#7889; nhi&#7873;u/kh&#244;ng &#273;&#7871;m &#273;&#432;&#7907;c.&quot;, &quot;K&#7871;t h&#7907;p c&#226;u ph&#7913;c (because, although, which), v&#224; d&#249;ng &#273;&#250;ng th&#236; hi&#7879;n t&#7841;i &#273;&#417;n/hi&#7879;n t&#7841;i ti&#7871;p di&#7877;n.&quot;]}, &quot;lexical_resource&quot;: {&quot;score&quot;: 4, &quot;issues&quot;: [&quot;Ch&#237;nh t&#7843;: nessessary, footbal, interesting (d&#249;ng sai d&#7841;ng), English kh&#244;ng vi&#7871;t hoa&#8230;&quot;, &quot;Collocation sai: \\&quot;very interesting about\\&quot;, \\&quot;good subject\\&quot;, \\&quot;help student speaking\\&quot;&#8230;&quot;], &quot;feedback&quot;: &quot;T&#7915; v&#7921;ng ngh&#232;o, l&#7863;p t&#7915; &#8220;student&#8221;, &#8220;international news&#8221;, sai ch&#237;nh t&#7843; nhi&#7873;u, collocation ch&#432;a ch&#237;nh x&#225;c.&quot;, &quot;improvements&quot;: [&quot;Ghi nh&#7899; ch&#237;nh t&#7843; chu&#7849;n, tra t&#7915; &#273;i&#7875;n tr&#432;&#7899;c khi vi&#7871;t.&quot;, &quot;H&#7885;c collocation: be interested in, improve speaking skills, beneficial subject&#8230; v&#224; thay th&#7871; t&#7915; l&#7863;p b&#7857;ng synonyms.&quot;]}, &quot;task_achievement&quot;: {&quot;score&quot;: 4, &quot;issues&quot;: [&quot;Thi&#7871;u t&#7889;i thi&#7875;u 250 t&#7915;.&quot;, &quot;L&#7853;p lu&#7853;n n&#244;ng, v&#237; d&#7909; chung chung, ch&#432;a ch&#7913;ng minh quan &#273;i&#7875;m.&quot;], &quot;feedback&quot;: &quot;B&#224;i vi&#7871;t ch&#432;a &#273;&#225;p &#7913;ng &#273;&#7847;y &#273;&#7911; y&#234;u c&#7847;u Task 2. B&#224;i ch&#7881; c&#243; 179 t&#7915; (&lt; 250 t&#7915; b&#7855;t bu&#7897;c), quan &#273;i&#7875;m c&#242;n m&#417; h&#7891; v&#224; ch&#432;a &#273;&#432;&#7907;c ph&#225;t tri&#7875;n v&#7899;i d&#7851;n ch&#7913;ng thuy&#7871;t ph&#7909;c.&quot;, &quot;improvements&quot;: [&quot;M&#7903; r&#7897;ng b&#224;i l&#234;n &#237;t nh&#7845;t 260-280 t&#7915;, ph&#225;t tri&#7875;n th&#234;m &#253; &#7903; m&#7895;i &#273;o&#7841;n v&#7899;i v&#237; d&#7909; c&#7909; th&#7875; (s&#7889; li&#7879;u, t&#236;nh hu&#7889;ng th&#7921;c t&#7871;).&quot;, &quot;N&#234;u r&#245; l&#7853;p tr&#432;&#7901;ng ngay t&#7915; m&#7903; b&#224;i v&#224; nh&#7855;c l&#7841;i nh&#7845;t qu&#225;n, s&#7917; d&#7909;ng l&#7853;p lu&#7853;n so s&#225;nh &#8211; ph&#226;n t&#237;ch l&#7907;i h&#7841;i thay v&#236; li&#7879;t k&#234;.&quot;]}, &quot;coherence_cohesion&quot;: {&quot;score&quot;: 4, &quot;issues&quot;: [&quot;D&#7845;u hi&#7879;u li&#234;n k&#7871;t h&#7841;n ch&#7871; (First, However&#8230;), thi&#7871;u therefore, moreover&#8230;&quot;, &quot;Thi&#7871;u ch&#7911; &#273;&#7873; c&#226;u (topic sentence) r&#245; r&#224;ng cho t&#7915;ng &#273;o&#7841;n th&#226;n.&quot;], &quot;feedback&quot;: &quot;B&#7889; c&#7909;c c&#243; m&#7903;-th&#226;n-k&#7871;t nh&#432;ng li&#234;n k&#7871;t l&#7887;ng l&#7867;o, t&#7915; n&#7889;i c&#242;n sai ho&#7863;c l&#7863;p. C&#226;u chuy&#7875;n &#273;o&#7841;n &#273;&#417;n gi&#7843;n n&#234;n &#253; t&#432;&#7903;ng r&#7901;i r&#7841;c.&quot;, &quot;improvements&quot;: [&quot;M&#7895;i &#273;o&#7841;n n&#234;n b&#7855;t &#273;&#7847;u b&#7857;ng c&#226;u ch&#7911; &#273;&#7873;, sau &#273;&#243; gi&#7843;i th&#237;ch v&#224; &#273;&#432;a v&#237; d&#7909;.&quot;, &quot;S&#7917; d&#7909;ng &#273;a d&#7841;ng cohesive devices: besides, as a result, in contrast&#8230; v&#224; d&#249;ng &#273;&#250;ng d&#7845;u ph&#7849;y.&quot;]}}</span>\"\n                  \"<span class=sf-dump-key>highlighted_corrections</span>\" => \"<span class=sf-dump-str title=\"3255 characters\">[{&quot;severity&quot;: &quot;high&quot;, &quot;error_type&quot;: &quot;grammar|spelling&quot;, &quot;explanation&quot;: &quot;thi&#7871;u &#039;s&#039; sau student, thi&#7871;u &#039;s&#039; sau sometimes, sai ch&#237;nh t&#7843; &#039;necessary&#039;&quot;, &quot;original_text&quot;: &quot;International news is good for student but sometime it is not nessessary.&quot;, &quot;suggested_correction&quot;: &quot;International news can be beneficial for students, but sometimes it is not necessary.&quot;}, {&quot;severity&quot;: &quot;high&quot;, &quot;error_type&quot;: &quot;grammar&quot;, &quot;explanation&quot;: &quot;thi&#7871;u s&#7889; nhi&#7873;u &#039;students&#039;, thi&#7871;u li&#234;n t&#7915; &#039;while&#039;, sai chia &#273;&#7897;ng t&#7915; &#039;think&#039;, thi&#7871;u &#273;&#7841;i t&#7915; &#039;it is&#039;.&quot;, &quot;original_text&quot;: &quot;Many people think student in secondary school need to study about international news, other think this waste their time.&quot;, &quot;suggested_correction&quot;: &quot;Many people think (that) secondary school students need to study international news, while others think it is a waste of time.&quot;}, {&quot;severity&quot;: &quot;medium&quot;, &quot;error_type&quot;: &quot;grammar&quot;, &quot;explanation&quot;: &quot;S-V agreement v&#224; t&#237;nh t&#7915; so s&#225;nh h&#417;n.&quot;, &quot;original_text&quot;: &quot;it make student smart.&quot;, &quot;suggested_correction&quot;: &quot;it makes students smarter.&quot;}, {&quot;severity&quot;: &quot;high&quot;, &quot;error_type&quot;: &quot;grammar|spelling|vocabulary&quot;, &quot;explanation&quot;: &quot;thi&#7871;u &#039;For&#039;, plural forms, s&#7903; h&#7919;u c&#225;ch, ch&#237;nh t&#7843; &#039;football&#039;.&quot;, &quot;original_text&quot;: &quot;Example, student can know weather in other country, famous people life, or footbal match.&quot;, &quot;suggested_correction&quot;: &quot;For example, students can learn about the weather in other countries, famous people&#039;s lives, or football matches.&quot;}, {&quot;severity&quot;: &quot;high&quot;, &quot;error_type&quot;: &quot;grammar|vocabulary&quot;, &quot;explanation&quot;: &quot;be interested in, plural &#039;topics&#039;.&quot;, &quot;original_text&quot;: &quot;They will very interesting about these topic&quot;, &quot;suggested_correction&quot;: &quot;They will be very interested in these topics&quot;}, {&quot;severity&quot;: &quot;high&quot;, &quot;error_type&quot;: &quot;grammar|spelling&quot;, &quot;explanation&quot;: &quot;thi&#7871;u &#039;s&#039; sau helps, c&#7847;n &#273;&#7897;ng t&#7915; &#039;improve&#039;, English vi&#7871;t hoa.&quot;, &quot;original_text&quot;: &quot;Learning international news is also help student speaking english well.&quot;, &quot;suggested_correction&quot;: &quot;Learning international news also helps students improve their English speaking skills.&quot;}, {&quot;severity&quot;: &quot;medium&quot;, &quot;error_type&quot;: &quot;grammar&quot;, &quot;explanation&quot;: &quot;sai th&#236; &#273;&#7897;ng t&#7915;, &#273;&#7843;o v&#7883; tr&#237; tr&#7841;ng t&#7915;.&quot;, &quot;original_text&quot;: &quot;However, I thinking international news sometime is boring.&quot;, &quot;suggested_correction&quot;: &quot;However, I think international news is sometimes boring.&quot;}, {&quot;severity&quot;: &quot;high&quot;, &quot;error_type&quot;: &quot;grammar&quot;, &quot;explanation&quot;: &quot;thi&#7871;u &#273;&#7897;ng t&#7915; &#039;is&#039;, thi&#7871;u m&#7841;o t&#7915; &#039;a&#039;.&quot;, &quot;original_text&quot;: &quot;International news not really important, only waste of time.&quot;, &quot;suggested_correction&quot;: &quot;International news is not really important; it is only a waste of time.&quot;}, {&quot;severity&quot;: &quot;medium&quot;, &quot;error_type&quot;: &quot;grammar|vocabulary&quot;, &quot;explanation&quot;: &quot;d&#249;ng &#273;&#7897;ng t&#7915; nguy&#234;n th&#7875; &#039;teach&#039;, plural teachers/students, d&#249;ng &#039;useful&#039;.&quot;, &quot;original_text&quot;: &quot;Teacher should teaching student good subject not international news.&quot;, &quot;suggested_correction&quot;: &quot;Teachers should teach students useful subjects rather than international news.&quot;}, {&quot;severity&quot;: &quot;high&quot;, &quot;error_type&quot;: &quot;grammar|vocabulary|spelling&quot;, &quot;explanation&quot;: &quot;In conclusion, S-V agreement &#039;news has&#039;, danh t&#7915; s&#7889; nhi&#7873;u &#039;disadvantages&#039;.&quot;, &quot;original_text&quot;: &quot;I conclusion, international news have advantage but more disadvantage.&quot;, &quot;suggested_correction&quot;: &quot;In conclusion, international news has some advantages but more disadvantages.&quot;}]</span>\"\n                  \"<span class=sf-dump-key>criteria_analysis</span>\" => \"<span class=sf-dump-str title=\"1665 characters\">[{&quot;criterion&quot;: &quot;Task Achievement&quot;, &quot;strengths&quot;: [&quot;C&#243; &#273;&#7873; c&#7853;p hai quan &#273;i&#7875;m nh&#432; y&#234;u c&#7847;u &#273;&#7873;.&quot;, &quot;N&#234;u &#273;&#432;&#7907;c quan &#273;i&#7875;m c&#225; nh&#226;n &#7903; &#273;o&#7841;n th&#226;n 2 v&#224; 3.&quot;], &quot;weaknesses&quot;: [&quot;B&#224;i d&#432;&#7899;i 250 t&#7915; n&#234;n b&#7883; tr&#7915; &#273;i&#7875;m n&#7863;ng.&quot;, &quot;L&#7853;p lu&#7853;n s&#417; s&#224;i, v&#237; d&#7909; ch&#432;a c&#7909; th&#7875;.&quot;, &quot;Thi&#7871;u ph&#7847;n gi&#7843;i th&#237;ch s&#226;u, kh&#244;ng c&#243; d&#7851;n ch&#7913;ng th&#7889;ng k&#234;.&quot;, &quot;Kh&#244;ng tr&#236;nh b&#224;y r&#245; r&#224;ng l&#253; do &#7911;ng h&#7897; ho&#7863;c ph&#7843;n &#273;&#7889;i.&quot;], &quot;band_justification&quot;: &quot;Theo m&#244; t&#7843; Band 4: tr&#7843; l&#7901;i m&#7897;t ph&#7847;n c&#226;u h&#7887;i, &#253; ch&#237;nh c&#242;n h&#7841;n ch&#7871; v&#224; thi&#7871;u h&#7895; tr&#7907;. B&#224;i n&#224;y ph&#249; h&#7907;p band 4.&quot;}, {&quot;criterion&quot;: &quot;Coherence &amp; Cohesion&quot;, &quot;strengths&quot;: [&quot;C&#243; m&#7903; b&#224;i, th&#226;n b&#224;i, k&#7871;t lu&#7853;n.&quot;, &quot;D&#249;ng &#273;&#432;&#7907;c &#039;Firstly&#039;, &#039;However&#039; &#273;&#7875; chuy&#7875;n &#253;.&quot;], &quot;weaknesses&quot;: [&quot;Li&#234;n k&#7871;t &#253; l&#7887;ng l&#7867;o, t&#7915; n&#7889;i l&#7863;p v&#224; thi&#7871;u.&quot;, &quot;C&#226;u ch&#7911; &#273;&#7873; ch&#432;a r&#245; r&#224;ng, s&#7855;p x&#7871;p &#253; thi&#7871;u logic.&quot;, &quot;Thi&#7871;u tham chi&#7871;u (this/that) v&#224; quan h&#7879; t&#7915;.&quot;, &quot;&#272;o&#7841;n 2 v&#224; 3 g&#7847;n nh&#432; tr&#249;ng n&#7897;i dung.&quot;], &quot;band_justification&quot;: &quot;Theo Band 4: t&#7893; ch&#7913;c th&#244;ng tin l&#7887;ng l&#7867;o, thi&#7871;t b&#7883; li&#234;n k&#7871;t c&#417; b&#7843;n v&#224; th&#432;&#7901;ng l&#7863;p; v&#236; v&#7853;y band 4.&quot;}, {&quot;criterion&quot;: &quot;Lexical Resource&quot;, &quot;strengths&quot;: [&quot;S&#7917; d&#7909;ng m&#7897;t s&#7889; t&#7915; v&#7921;ng li&#234;n quan ch&#7911; &#273;&#7873; nh&#432; &#039;weather&#039;, &#039;famous people&#039;.&quot;, &quot;C&#7889; g&#7855;ng d&#249;ng t&#237;nh t&#7915; (interesting, important).&quot;], &quot;weaknesses&quot;: [&quot;T&#7915; v&#7921;ng r&#7845;t h&#7841;n ch&#7871;, l&#7863;p nhi&#7873;u.&quot;, &quot;Nhi&#7873;u l&#7895;i ch&#237;nh t&#7843;, collocation sai.&quot;, &quot;&#205;t t&#7915; mang t&#237;nh h&#7885;c thu&#7853;t.&quot;], &quot;band_justification&quot;: &quot;Band 4: v&#7889;n t&#7915; h&#7841;n ch&#7871;, l&#7895;i th&#432;&#7901;ng xuy&#234;n g&#226;y tr&#7903; ng&#7841;i giao ti&#7871;p nh&#432;ng &#253; ch&#237;nh v&#7851;n hi&#7875;u.&quot;}, {&quot;criterion&quot;: &quot;Grammar &amp; Accuracy&quot;, &quot;strengths&quot;: [&quot;C&#243; d&#249;ng m&#7897;t s&#7889; th&#236; hi&#7879;n t&#7841;i &#273;&#417;n, c&#7845;u tr&#250;c &#273;&#417;n.&quot;], &quot;weaknesses&quot;: [&quot;L&#7895;i S-V agreement, m&#7841;o t&#7915;, gi&#7899;i t&#7915; ph&#7893; bi&#7871;n.&quot;, &quot;&#205;t c&#226;u ph&#7913;c; c&#7845;u tr&#250;c l&#7863;p.&quot;, &quot;Nhi&#7873;u l&#7895;i &#7843;nh h&#432;&#7903;ng s&#7921; hi&#7875;u.&quot;], &quot;band_justification&quot;: &quot;Ph&#249; h&#7907;p Band 4: L&#7895;i ng&#7919; ph&#225;p th&#432;&#7901;ng xuy&#234;n, &#237;t c&#7845;u tr&#250;c ph&#7913;c t&#7841;p.&quot;}]</span>\"\n                  \"<span class=sf-dump-key>statistics</span>\" => \"<span class=sf-dump-str title=\"144 characters\">{&quot;total_errors&quot;: 38, &quot;grammar_errors&quot;: 24, &quot;spelling_errors&quot;: 8, &quot;complexity_level&quot;: &quot;basic&quot;, &quot;sentence_variety&quot;: &quot;low&quot;, &quot;vocabulary_errors&quot;: 6}</span>\"\n                  \"<span class=sf-dump-key>word_count</span>\" => <span class=sf-dump-num>179</span>\n                  \"<span class=sf-dump-key>essay_length</span>\" => <span class=sf-dump-num>1184</span>\n                  \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"9 characters\">completed</span>\"\n                  \"<span class=sf-dump-key>error_message</span>\" => <span class=sf-dump-const>null</span>\n                  \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-18 15:06:07</span>\"\n                  \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-18 15:06:38</span>\"\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:21</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>9</span>\n                  \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-num>1</span>\n                  \"<span class=sf-dump-key>essay_question_id</span>\" => <span class=sf-dump-const>null</span>\n                  \"<span class=sf-dump-key>essay_content</span>\" => \"\"\"\n                    <span class=sf-dump-str title=\"1184 characters\">International news is good for student but sometime it is not nessessary. Many people think student in secondary school need to study about international news, other think this waste their time. My essay will write about both of these thing.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">Firstly, International news is good because it make student smart. When student learn international news they can know many things. Example, student can know weather in other country, famous people life, or footbal match. They will very interesting about these topic and can relax in the classroom. Learning international news is also help student speaking english well.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">However, I thinking international news sometime is boring. Student have many important subject, example maths, literature, or chemistry. These subjects can help student become doctor or engineer in future. International news not really important, only waste of time.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">In my opinion, secondary student should play sport and learn math better than learn international news. Teacher should teaching student good subject not international news.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">I conclusion, international news have advantage but more disadvantage. Student no need study this subject, waste of their time.</span>\n                    \"\"\"\n                  \"<span class=sf-dump-key>task_type</span>\" => \"<span class=sf-dump-str title=\"5 characters\">task2</span>\"\n                  \"<span class=sf-dump-key>time_limit</span>\" => <span class=sf-dump-num>40</span>\n                  \"<span class=sf-dump-key>overall_band_score</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.0</span>\"\n                  \"<span class=sf-dump-key>task_achievement</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.0</span>\"\n                  \"<span class=sf-dump-key>coherence_cohesion</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.0</span>\"\n                  \"<span class=sf-dump-key>lexical_resource</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.0</span>\"\n                  \"<span class=sf-dump-key>grammar_accuracy</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.0</span>\"\n                  \"<span class=sf-dump-key>detailed_feedback</span>\" => \"<span class=sf-dump-str title=\"2086 characters\">{&quot;grammar_accuracy&quot;: {&quot;score&quot;: 4, &quot;issues&quot;: [&quot;S-V: \\&quot;news is &#8230; it make student\\&quot;, \\&quot;Learning international news is also help&#8230;\\&quot;&quot;, &quot;Thi&#7871;u m&#7841;o t&#7915;: \\&quot;in other country\\&quot;, \\&quot;famous people life\\&quot;&quot;], &quot;feedback&quot;: &quot;L&#7895;i S-V agreement, thi&#7871;u m&#7841;o t&#7915;, gi&#7899;i t&#7915;, th&#236;, c&#7845;u tr&#250;c c&#226;u h&#7847;u h&#7871;t &#7903; m&#7913;c &#273;&#417;n gi&#7843;n.&quot;, &quot;improvements&quot;: [&quot;&#212;n quy t&#7855;c S-V agreement, &#273;&#7863;c bi&#7879;t v&#7899;i danh t&#7915; s&#7889; nhi&#7873;u/kh&#244;ng &#273;&#7871;m &#273;&#432;&#7907;c.&quot;, &quot;K&#7871;t h&#7907;p c&#226;u ph&#7913;c (because, although, which), v&#224; d&#249;ng &#273;&#250;ng th&#236; hi&#7879;n t&#7841;i &#273;&#417;n/hi&#7879;n t&#7841;i ti&#7871;p di&#7877;n.&quot;]}, &quot;lexical_resource&quot;: {&quot;score&quot;: 4, &quot;issues&quot;: [&quot;Ch&#237;nh t&#7843;: nessessary, footbal, interesting (d&#249;ng sai d&#7841;ng), English kh&#244;ng vi&#7871;t hoa&#8230;&quot;, &quot;Collocation sai: \\&quot;very interesting about\\&quot;, \\&quot;good subject\\&quot;, \\&quot;help student speaking\\&quot;&#8230;&quot;], &quot;feedback&quot;: &quot;T&#7915; v&#7921;ng ngh&#232;o, l&#7863;p t&#7915; &#8220;student&#8221;, &#8220;international news&#8221;, sai ch&#237;nh t&#7843; nhi&#7873;u, collocation ch&#432;a ch&#237;nh x&#225;c.&quot;, &quot;improvements&quot;: [&quot;Ghi nh&#7899; ch&#237;nh t&#7843; chu&#7849;n, tra t&#7915; &#273;i&#7875;n tr&#432;&#7899;c khi vi&#7871;t.&quot;, &quot;H&#7885;c collocation: be interested in, improve speaking skills, beneficial subject&#8230; v&#224; thay th&#7871; t&#7915; l&#7863;p b&#7857;ng synonyms.&quot;]}, &quot;task_achievement&quot;: {&quot;score&quot;: 4, &quot;issues&quot;: [&quot;Thi&#7871;u t&#7889;i thi&#7875;u 250 t&#7915;.&quot;, &quot;L&#7853;p lu&#7853;n n&#244;ng, v&#237; d&#7909; chung chung, ch&#432;a ch&#7913;ng minh quan &#273;i&#7875;m.&quot;], &quot;feedback&quot;: &quot;B&#224;i vi&#7871;t ch&#432;a &#273;&#225;p &#7913;ng &#273;&#7847;y &#273;&#7911; y&#234;u c&#7847;u Task 2. B&#224;i ch&#7881; c&#243; 179 t&#7915; (&lt; 250 t&#7915; b&#7855;t bu&#7897;c), quan &#273;i&#7875;m c&#242;n m&#417; h&#7891; v&#224; ch&#432;a &#273;&#432;&#7907;c ph&#225;t tri&#7875;n v&#7899;i d&#7851;n ch&#7913;ng thuy&#7871;t ph&#7909;c.&quot;, &quot;improvements&quot;: [&quot;M&#7903; r&#7897;ng b&#224;i l&#234;n &#237;t nh&#7845;t 260-280 t&#7915;, ph&#225;t tri&#7875;n th&#234;m &#253; &#7903; m&#7895;i &#273;o&#7841;n v&#7899;i v&#237; d&#7909; c&#7909; th&#7875; (s&#7889; li&#7879;u, t&#236;nh hu&#7889;ng th&#7921;c t&#7871;).&quot;, &quot;N&#234;u r&#245; l&#7853;p tr&#432;&#7901;ng ngay t&#7915; m&#7903; b&#224;i v&#224; nh&#7855;c l&#7841;i nh&#7845;t qu&#225;n, s&#7917; d&#7909;ng l&#7853;p lu&#7853;n so s&#225;nh &#8211; ph&#226;n t&#237;ch l&#7907;i h&#7841;i thay v&#236; li&#7879;t k&#234;.&quot;]}, &quot;coherence_cohesion&quot;: {&quot;score&quot;: 4, &quot;issues&quot;: [&quot;D&#7845;u hi&#7879;u li&#234;n k&#7871;t h&#7841;n ch&#7871; (First, However&#8230;), thi&#7871;u therefore, moreover&#8230;&quot;, &quot;Thi&#7871;u ch&#7911; &#273;&#7873; c&#226;u (topic sentence) r&#245; r&#224;ng cho t&#7915;ng &#273;o&#7841;n th&#226;n.&quot;], &quot;feedback&quot;: &quot;B&#7889; c&#7909;c c&#243; m&#7903;-th&#226;n-k&#7871;t nh&#432;ng li&#234;n k&#7871;t l&#7887;ng l&#7867;o, t&#7915; n&#7889;i c&#242;n sai ho&#7863;c l&#7863;p. C&#226;u chuy&#7875;n &#273;o&#7841;n &#273;&#417;n gi&#7843;n n&#234;n &#253; t&#432;&#7903;ng r&#7901;i r&#7841;c.&quot;, &quot;improvements&quot;: [&quot;M&#7895;i &#273;o&#7841;n n&#234;n b&#7855;t &#273;&#7847;u b&#7857;ng c&#226;u ch&#7911; &#273;&#7873;, sau &#273;&#243; gi&#7843;i th&#237;ch v&#224; &#273;&#432;a v&#237; d&#7909;.&quot;, &quot;S&#7917; d&#7909;ng &#273;a d&#7841;ng cohesive devices: besides, as a result, in contrast&#8230; v&#224; d&#249;ng &#273;&#250;ng d&#7845;u ph&#7849;y.&quot;]}}</span>\"\n                  \"<span class=sf-dump-key>highlighted_corrections</span>\" => \"<span class=sf-dump-str title=\"3255 characters\">[{&quot;severity&quot;: &quot;high&quot;, &quot;error_type&quot;: &quot;grammar|spelling&quot;, &quot;explanation&quot;: &quot;thi&#7871;u &#039;s&#039; sau student, thi&#7871;u &#039;s&#039; sau sometimes, sai ch&#237;nh t&#7843; &#039;necessary&#039;&quot;, &quot;original_text&quot;: &quot;International news is good for student but sometime it is not nessessary.&quot;, &quot;suggested_correction&quot;: &quot;International news can be beneficial for students, but sometimes it is not necessary.&quot;}, {&quot;severity&quot;: &quot;high&quot;, &quot;error_type&quot;: &quot;grammar&quot;, &quot;explanation&quot;: &quot;thi&#7871;u s&#7889; nhi&#7873;u &#039;students&#039;, thi&#7871;u li&#234;n t&#7915; &#039;while&#039;, sai chia &#273;&#7897;ng t&#7915; &#039;think&#039;, thi&#7871;u &#273;&#7841;i t&#7915; &#039;it is&#039;.&quot;, &quot;original_text&quot;: &quot;Many people think student in secondary school need to study about international news, other think this waste their time.&quot;, &quot;suggested_correction&quot;: &quot;Many people think (that) secondary school students need to study international news, while others think it is a waste of time.&quot;}, {&quot;severity&quot;: &quot;medium&quot;, &quot;error_type&quot;: &quot;grammar&quot;, &quot;explanation&quot;: &quot;S-V agreement v&#224; t&#237;nh t&#7915; so s&#225;nh h&#417;n.&quot;, &quot;original_text&quot;: &quot;it make student smart.&quot;, &quot;suggested_correction&quot;: &quot;it makes students smarter.&quot;}, {&quot;severity&quot;: &quot;high&quot;, &quot;error_type&quot;: &quot;grammar|spelling|vocabulary&quot;, &quot;explanation&quot;: &quot;thi&#7871;u &#039;For&#039;, plural forms, s&#7903; h&#7919;u c&#225;ch, ch&#237;nh t&#7843; &#039;football&#039;.&quot;, &quot;original_text&quot;: &quot;Example, student can know weather in other country, famous people life, or footbal match.&quot;, &quot;suggested_correction&quot;: &quot;For example, students can learn about the weather in other countries, famous people&#039;s lives, or football matches.&quot;}, {&quot;severity&quot;: &quot;high&quot;, &quot;error_type&quot;: &quot;grammar|vocabulary&quot;, &quot;explanation&quot;: &quot;be interested in, plural &#039;topics&#039;.&quot;, &quot;original_text&quot;: &quot;They will very interesting about these topic&quot;, &quot;suggested_correction&quot;: &quot;They will be very interested in these topics&quot;}, {&quot;severity&quot;: &quot;high&quot;, &quot;error_type&quot;: &quot;grammar|spelling&quot;, &quot;explanation&quot;: &quot;thi&#7871;u &#039;s&#039; sau helps, c&#7847;n &#273;&#7897;ng t&#7915; &#039;improve&#039;, English vi&#7871;t hoa.&quot;, &quot;original_text&quot;: &quot;Learning international news is also help student speaking english well.&quot;, &quot;suggested_correction&quot;: &quot;Learning international news also helps students improve their English speaking skills.&quot;}, {&quot;severity&quot;: &quot;medium&quot;, &quot;error_type&quot;: &quot;grammar&quot;, &quot;explanation&quot;: &quot;sai th&#236; &#273;&#7897;ng t&#7915;, &#273;&#7843;o v&#7883; tr&#237; tr&#7841;ng t&#7915;.&quot;, &quot;original_text&quot;: &quot;However, I thinking international news sometime is boring.&quot;, &quot;suggested_correction&quot;: &quot;However, I think international news is sometimes boring.&quot;}, {&quot;severity&quot;: &quot;high&quot;, &quot;error_type&quot;: &quot;grammar&quot;, &quot;explanation&quot;: &quot;thi&#7871;u &#273;&#7897;ng t&#7915; &#039;is&#039;, thi&#7871;u m&#7841;o t&#7915; &#039;a&#039;.&quot;, &quot;original_text&quot;: &quot;International news not really important, only waste of time.&quot;, &quot;suggested_correction&quot;: &quot;International news is not really important; it is only a waste of time.&quot;}, {&quot;severity&quot;: &quot;medium&quot;, &quot;error_type&quot;: &quot;grammar|vocabulary&quot;, &quot;explanation&quot;: &quot;d&#249;ng &#273;&#7897;ng t&#7915; nguy&#234;n th&#7875; &#039;teach&#039;, plural teachers/students, d&#249;ng &#039;useful&#039;.&quot;, &quot;original_text&quot;: &quot;Teacher should teaching student good subject not international news.&quot;, &quot;suggested_correction&quot;: &quot;Teachers should teach students useful subjects rather than international news.&quot;}, {&quot;severity&quot;: &quot;high&quot;, &quot;error_type&quot;: &quot;grammar|vocabulary|spelling&quot;, &quot;explanation&quot;: &quot;In conclusion, S-V agreement &#039;news has&#039;, danh t&#7915; s&#7889; nhi&#7873;u &#039;disadvantages&#039;.&quot;, &quot;original_text&quot;: &quot;I conclusion, international news have advantage but more disadvantage.&quot;, &quot;suggested_correction&quot;: &quot;In conclusion, international news has some advantages but more disadvantages.&quot;}]</span>\"\n                  \"<span class=sf-dump-key>criteria_analysis</span>\" => \"<span class=sf-dump-str title=\"1665 characters\">[{&quot;criterion&quot;: &quot;Task Achievement&quot;, &quot;strengths&quot;: [&quot;C&#243; &#273;&#7873; c&#7853;p hai quan &#273;i&#7875;m nh&#432; y&#234;u c&#7847;u &#273;&#7873;.&quot;, &quot;N&#234;u &#273;&#432;&#7907;c quan &#273;i&#7875;m c&#225; nh&#226;n &#7903; &#273;o&#7841;n th&#226;n 2 v&#224; 3.&quot;], &quot;weaknesses&quot;: [&quot;B&#224;i d&#432;&#7899;i 250 t&#7915; n&#234;n b&#7883; tr&#7915; &#273;i&#7875;m n&#7863;ng.&quot;, &quot;L&#7853;p lu&#7853;n s&#417; s&#224;i, v&#237; d&#7909; ch&#432;a c&#7909; th&#7875;.&quot;, &quot;Thi&#7871;u ph&#7847;n gi&#7843;i th&#237;ch s&#226;u, kh&#244;ng c&#243; d&#7851;n ch&#7913;ng th&#7889;ng k&#234;.&quot;, &quot;Kh&#244;ng tr&#236;nh b&#224;y r&#245; r&#224;ng l&#253; do &#7911;ng h&#7897; ho&#7863;c ph&#7843;n &#273;&#7889;i.&quot;], &quot;band_justification&quot;: &quot;Theo m&#244; t&#7843; Band 4: tr&#7843; l&#7901;i m&#7897;t ph&#7847;n c&#226;u h&#7887;i, &#253; ch&#237;nh c&#242;n h&#7841;n ch&#7871; v&#224; thi&#7871;u h&#7895; tr&#7907;. B&#224;i n&#224;y ph&#249; h&#7907;p band 4.&quot;}, {&quot;criterion&quot;: &quot;Coherence &amp; Cohesion&quot;, &quot;strengths&quot;: [&quot;C&#243; m&#7903; b&#224;i, th&#226;n b&#224;i, k&#7871;t lu&#7853;n.&quot;, &quot;D&#249;ng &#273;&#432;&#7907;c &#039;Firstly&#039;, &#039;However&#039; &#273;&#7875; chuy&#7875;n &#253;.&quot;], &quot;weaknesses&quot;: [&quot;Li&#234;n k&#7871;t &#253; l&#7887;ng l&#7867;o, t&#7915; n&#7889;i l&#7863;p v&#224; thi&#7871;u.&quot;, &quot;C&#226;u ch&#7911; &#273;&#7873; ch&#432;a r&#245; r&#224;ng, s&#7855;p x&#7871;p &#253; thi&#7871;u logic.&quot;, &quot;Thi&#7871;u tham chi&#7871;u (this/that) v&#224; quan h&#7879; t&#7915;.&quot;, &quot;&#272;o&#7841;n 2 v&#224; 3 g&#7847;n nh&#432; tr&#249;ng n&#7897;i dung.&quot;], &quot;band_justification&quot;: &quot;Theo Band 4: t&#7893; ch&#7913;c th&#244;ng tin l&#7887;ng l&#7867;o, thi&#7871;t b&#7883; li&#234;n k&#7871;t c&#417; b&#7843;n v&#224; th&#432;&#7901;ng l&#7863;p; v&#236; v&#7853;y band 4.&quot;}, {&quot;criterion&quot;: &quot;Lexical Resource&quot;, &quot;strengths&quot;: [&quot;S&#7917; d&#7909;ng m&#7897;t s&#7889; t&#7915; v&#7921;ng li&#234;n quan ch&#7911; &#273;&#7873; nh&#432; &#039;weather&#039;, &#039;famous people&#039;.&quot;, &quot;C&#7889; g&#7855;ng d&#249;ng t&#237;nh t&#7915; (interesting, important).&quot;], &quot;weaknesses&quot;: [&quot;T&#7915; v&#7921;ng r&#7845;t h&#7841;n ch&#7871;, l&#7863;p nhi&#7873;u.&quot;, &quot;Nhi&#7873;u l&#7895;i ch&#237;nh t&#7843;, collocation sai.&quot;, &quot;&#205;t t&#7915; mang t&#237;nh h&#7885;c thu&#7853;t.&quot;], &quot;band_justification&quot;: &quot;Band 4: v&#7889;n t&#7915; h&#7841;n ch&#7871;, l&#7895;i th&#432;&#7901;ng xuy&#234;n g&#226;y tr&#7903; ng&#7841;i giao ti&#7871;p nh&#432;ng &#253; ch&#237;nh v&#7851;n hi&#7875;u.&quot;}, {&quot;criterion&quot;: &quot;Grammar &amp; Accuracy&quot;, &quot;strengths&quot;: [&quot;C&#243; d&#249;ng m&#7897;t s&#7889; th&#236; hi&#7879;n t&#7841;i &#273;&#417;n, c&#7845;u tr&#250;c &#273;&#417;n.&quot;], &quot;weaknesses&quot;: [&quot;L&#7895;i S-V agreement, m&#7841;o t&#7915;, gi&#7899;i t&#7915; ph&#7893; bi&#7871;n.&quot;, &quot;&#205;t c&#226;u ph&#7913;c; c&#7845;u tr&#250;c l&#7863;p.&quot;, &quot;Nhi&#7873;u l&#7895;i &#7843;nh h&#432;&#7903;ng s&#7921; hi&#7875;u.&quot;], &quot;band_justification&quot;: &quot;Ph&#249; h&#7907;p Band 4: L&#7895;i ng&#7919; ph&#225;p th&#432;&#7901;ng xuy&#234;n, &#237;t c&#7845;u tr&#250;c ph&#7913;c t&#7841;p.&quot;}]</span>\"\n                  \"<span class=sf-dump-key>statistics</span>\" => \"<span class=sf-dump-str title=\"144 characters\">{&quot;total_errors&quot;: 38, &quot;grammar_errors&quot;: 24, &quot;spelling_errors&quot;: 8, &quot;complexity_level&quot;: &quot;basic&quot;, &quot;sentence_variety&quot;: &quot;low&quot;, &quot;vocabulary_errors&quot;: 6}</span>\"\n                  \"<span class=sf-dump-key>word_count</span>\" => <span class=sf-dump-num>179</span>\n                  \"<span class=sf-dump-key>essay_length</span>\" => <span class=sf-dump-num>1184</span>\n                  \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"9 characters\">completed</span>\"\n                  \"<span class=sf-dump-key>error_message</span>\" => <span class=sf-dump-const>null</span>\n                  \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-18 15:06:07</span>\"\n                  \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-18 15:06:38</span>\"\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">previous</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:9</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>detailed_feedback</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                  \"<span class=sf-dump-key>highlighted_corrections</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                  \"<span class=sf-dump-key>criteria_analysis</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                  \"<span class=sf-dump-key>statistics</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                  \"<span class=sf-dump-key>overall_band_score</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:1</span>\"\n                  \"<span class=sf-dump-key>task_achievement</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:1</span>\"\n                  \"<span class=sf-dump-key>coherence_cohesion</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:1</span>\"\n                  \"<span class=sf-dump-key>lexical_resource</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:1</span>\"\n                  \"<span class=sf-dump-key>grammar_accuracy</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:1</span>\"\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>essayQuestion</span>\" => <span class=sf-dump-const>null</span>\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadCallback</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadContext</span>: <span class=sf-dump-const>null</span>\n                +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:18</span> [<samp data-depth=9 class=sf-dump-compact>\n                  <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">user_id</span>\"\n                  <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"17 characters\">essay_question_id</span>\"\n                  <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"13 characters\">essay_content</span>\"\n                  <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"9 characters\">task_type</span>\"\n                  <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"10 characters\">time_limit</span>\"\n                  <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"18 characters\">overall_band_score</span>\"\n                  <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"16 characters\">task_achievement</span>\"\n                  <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"18 characters\">coherence_cohesion</span>\"\n                  <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"16 characters\">lexical_resource</span>\"\n                  <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"16 characters\">grammar_accuracy</span>\"\n                  <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"17 characters\">detailed_feedback</span>\"\n                  <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"23 characters\">highlighted_corrections</span>\"\n                  <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"17 characters\">criteria_analysis</span>\"\n                  <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"10 characters\">statistics</span>\"\n                  <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"10 characters\">word_count</span>\"\n                  <span class=sf-dump-index>15</span> => \"<span class=sf-dump-str title=\"12 characters\">essay_length</span>\"\n                  <span class=sf-dump-index>16</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n                  <span class=sf-dump-index>17</span> => \"<span class=sf-dump-str title=\"13 characters\">error_message</span>\"\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=9 class=sf-dump-compact>\n                  <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n                </samp>]\n              </samp>}\n              <span class=sf-dump-index>1</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\ScoringAttempt\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ScoringAttempt</span></span> {<a class=sf-dump-ref>#1488</a><samp data-depth=8 class=sf-dump-compact>\n                #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"16 characters\">scoring_attempts</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:21</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>5</span>\n                  \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-num>1</span>\n                  \"<span class=sf-dump-key>essay_question_id</span>\" => <span class=sf-dump-const>null</span>\n                  \"<span class=sf-dump-key>essay_content</span>\" => \"\"\"\n                    <span class=sf-dump-str title=\"1184 characters\">International news is good for student but sometime it is not nessessary. Many people think student in secondary school need to study about international news, other think this waste their time. My essay will write about both of these thing.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">Firstly, International news is good because it make student smart. When student learn international news they can know many things. Example, student can know weather in other country, famous people life, or footbal match. They will very interesting about these topic and can relax in the classroom. Learning international news is also help student speaking english well.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">However, I thinking international news sometime is boring. Student have many important subject, example maths, literature, or chemistry. These subjects can help student become doctor or engineer in future. International news not really important, only waste of time.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">In my opinion, secondary student should play sport and learn math better than learn international news. Teacher should teaching student good subject not international news.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">I conclusion, international news have advantage but more disadvantage. Student no need study this subject, waste of their time.</span>\n                    \"\"\"\n                  \"<span class=sf-dump-key>task_type</span>\" => \"<span class=sf-dump-str title=\"5 characters\">task2</span>\"\n                  \"<span class=sf-dump-key>time_limit</span>\" => <span class=sf-dump-num>40</span>\n                  \"<span class=sf-dump-key>overall_band_score</span>\" => \"<span class=sf-dump-str title=\"3 characters\">5.0</span>\"\n                  \"<span class=sf-dump-key>task_achievement</span>\" => \"<span class=sf-dump-str title=\"3 characters\">5.1</span>\"\n                  \"<span class=sf-dump-key>coherence_cohesion</span>\" => \"<span class=sf-dump-str title=\"3 characters\">5.2</span>\"\n                  \"<span class=sf-dump-key>lexical_resource</span>\" => \"<span class=sf-dump-str title=\"3 characters\">5.0</span>\"\n                  \"<span class=sf-dump-key>grammar_accuracy</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.8</span>\"\n                  \"<span class=sf-dump-key>detailed_feedback</span>\" => \"<span class=sf-dump-str title=\"208 characters\">{&quot;task_achievement&quot;: {&quot;score&quot;: 5, &quot;issues&quot;: [&quot;&#272;&#226;y l&#224; ph&#7843;n h&#7891;i demo&quot;], &quot;feedback&quot;: &quot;H&#7879; th&#7889;ng &#273;ang trong ch&#7871; &#273;&#7897; demo. &#272;&#226;y l&#224; k&#7871;t qu&#7843; m&#7851;u.&quot;, &quot;improvements&quot;: [&quot;Vui l&#242;ng th&#7917; l&#7841;i sau khi h&#7879; th&#7889;ng &#273;&#432;&#7907;c kh&#244;i ph&#7909;c&quot;]}}</span>\"\n                  \"<span class=sf-dump-key>highlighted_corrections</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n                  \"<span class=sf-dump-key>criteria_analysis</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n                  \"<span class=sf-dump-key>statistics</span>\" => \"<span class=sf-dump-str title=\"152 characters\">{&quot;total_errors&quot;: 7, &quot;grammar_errors&quot;: 6, &quot;spelling_errors&quot;: 2, &quot;complexity_level&quot;: &quot;intermediate&quot;, &quot;sentence_variety&quot;: &quot;medium&quot;, &quot;vocabulary_errors&quot;: 1}</span>\"\n                  \"<span class=sf-dump-key>word_count</span>\" => <span class=sf-dump-num>179</span>\n                  \"<span class=sf-dump-key>essay_length</span>\" => <span class=sf-dump-num>1184</span>\n                  \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"9 characters\">completed</span>\"\n                  \"<span class=sf-dump-key>error_message</span>\" => <span class=sf-dump-const>null</span>\n                  \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-18 14:56:40</span>\"\n                  \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-18 14:56:44</span>\"\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:21</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>5</span>\n                  \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-num>1</span>\n                  \"<span class=sf-dump-key>essay_question_id</span>\" => <span class=sf-dump-const>null</span>\n                  \"<span class=sf-dump-key>essay_content</span>\" => \"\"\"\n                    <span class=sf-dump-str title=\"1184 characters\">International news is good for student but sometime it is not nessessary. Many people think student in secondary school need to study about international news, other think this waste their time. My essay will write about both of these thing.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">Firstly, International news is good because it make student smart. When student learn international news they can know many things. Example, student can know weather in other country, famous people life, or footbal match. They will very interesting about these topic and can relax in the classroom. Learning international news is also help student speaking english well.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">However, I thinking international news sometime is boring. Student have many important subject, example maths, literature, or chemistry. These subjects can help student become doctor or engineer in future. International news not really important, only waste of time.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">In my opinion, secondary student should play sport and learn math better than learn international news. Teacher should teaching student good subject not international news.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">I conclusion, international news have advantage but more disadvantage. Student no need study this subject, waste of their time.</span>\n                    \"\"\"\n                  \"<span class=sf-dump-key>task_type</span>\" => \"<span class=sf-dump-str title=\"5 characters\">task2</span>\"\n                  \"<span class=sf-dump-key>time_limit</span>\" => <span class=sf-dump-num>40</span>\n                  \"<span class=sf-dump-key>overall_band_score</span>\" => \"<span class=sf-dump-str title=\"3 characters\">5.0</span>\"\n                  \"<span class=sf-dump-key>task_achievement</span>\" => \"<span class=sf-dump-str title=\"3 characters\">5.1</span>\"\n                  \"<span class=sf-dump-key>coherence_cohesion</span>\" => \"<span class=sf-dump-str title=\"3 characters\">5.2</span>\"\n                  \"<span class=sf-dump-key>lexical_resource</span>\" => \"<span class=sf-dump-str title=\"3 characters\">5.0</span>\"\n                  \"<span class=sf-dump-key>grammar_accuracy</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.8</span>\"\n                  \"<span class=sf-dump-key>detailed_feedback</span>\" => \"<span class=sf-dump-str title=\"208 characters\">{&quot;task_achievement&quot;: {&quot;score&quot;: 5, &quot;issues&quot;: [&quot;&#272;&#226;y l&#224; ph&#7843;n h&#7891;i demo&quot;], &quot;feedback&quot;: &quot;H&#7879; th&#7889;ng &#273;ang trong ch&#7871; &#273;&#7897; demo. &#272;&#226;y l&#224; k&#7871;t qu&#7843; m&#7851;u.&quot;, &quot;improvements&quot;: [&quot;Vui l&#242;ng th&#7917; l&#7841;i sau khi h&#7879; th&#7889;ng &#273;&#432;&#7907;c kh&#244;i ph&#7909;c&quot;]}}</span>\"\n                  \"<span class=sf-dump-key>highlighted_corrections</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n                  \"<span class=sf-dump-key>criteria_analysis</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n                  \"<span class=sf-dump-key>statistics</span>\" => \"<span class=sf-dump-str title=\"152 characters\">{&quot;total_errors&quot;: 7, &quot;grammar_errors&quot;: 6, &quot;spelling_errors&quot;: 2, &quot;complexity_level&quot;: &quot;intermediate&quot;, &quot;sentence_variety&quot;: &quot;medium&quot;, &quot;vocabulary_errors&quot;: 1}</span>\"\n                  \"<span class=sf-dump-key>word_count</span>\" => <span class=sf-dump-num>179</span>\n                  \"<span class=sf-dump-key>essay_length</span>\" => <span class=sf-dump-num>1184</span>\n                  \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"9 characters\">completed</span>\"\n                  \"<span class=sf-dump-key>error_message</span>\" => <span class=sf-dump-const>null</span>\n                  \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-18 14:56:40</span>\"\n                  \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-18 14:56:44</span>\"\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">previous</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:9</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>detailed_feedback</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                  \"<span class=sf-dump-key>highlighted_corrections</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                  \"<span class=sf-dump-key>criteria_analysis</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                  \"<span class=sf-dump-key>statistics</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                  \"<span class=sf-dump-key>overall_band_score</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:1</span>\"\n                  \"<span class=sf-dump-key>task_achievement</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:1</span>\"\n                  \"<span class=sf-dump-key>coherence_cohesion</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:1</span>\"\n                  \"<span class=sf-dump-key>lexical_resource</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:1</span>\"\n                  \"<span class=sf-dump-key>grammar_accuracy</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:1</span>\"\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>essayQuestion</span>\" => <span class=sf-dump-const>null</span>\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadCallback</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadContext</span>: <span class=sf-dump-const>null</span>\n                +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:18</span> [<samp data-depth=9 class=sf-dump-compact>\n                  <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">user_id</span>\"\n                  <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"17 characters\">essay_question_id</span>\"\n                  <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"13 characters\">essay_content</span>\"\n                  <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"9 characters\">task_type</span>\"\n                  <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"10 characters\">time_limit</span>\"\n                  <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"18 characters\">overall_band_score</span>\"\n                  <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"16 characters\">task_achievement</span>\"\n                  <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"18 characters\">coherence_cohesion</span>\"\n                  <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"16 characters\">lexical_resource</span>\"\n                  <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"16 characters\">grammar_accuracy</span>\"\n                  <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"17 characters\">detailed_feedback</span>\"\n                  <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"23 characters\">highlighted_corrections</span>\"\n                  <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"17 characters\">criteria_analysis</span>\"\n                  <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"10 characters\">statistics</span>\"\n                  <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"10 characters\">word_count</span>\"\n                  <span class=sf-dump-index>15</span> => \"<span class=sf-dump-str title=\"12 characters\">essay_length</span>\"\n                  <span class=sf-dump-index>16</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n                  <span class=sf-dump-index>17</span> => \"<span class=sf-dump-str title=\"13 characters\">error_message</span>\"\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=9 class=sf-dump-compact>\n                  <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n                </samp>]\n              </samp>}\n              <span class=sf-dump-index>2</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\ScoringAttempt\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ScoringAttempt</span></span> {<a class=sf-dump-ref>#1487</a><samp data-depth=8 class=sf-dump-compact>\n                #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"16 characters\">scoring_attempts</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:21</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>4</span>\n                  \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-num>1</span>\n                  \"<span class=sf-dump-key>essay_question_id</span>\" => <span class=sf-dump-const>null</span>\n                  \"<span class=sf-dump-key>essay_content</span>\" => \"\"\"\n                    <span class=sf-dump-str title=\"1184 characters\">International news is good for student but sometime it is not nessessary. Many people think student in secondary school need to study about international news, other think this waste their time. My essay will write about both of these thing.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">Firstly, International news is good because it make student smart. When student learn international news they can know many things. Example, student can know weather in other country, famous people life, or footbal match. They will very interesting about these topic and can relax in the classroom. Learning international news is also help student speaking english well.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">However, I thinking international news sometime is boring. Student have many important subject, example maths, literature, or chemistry. These subjects can help student become doctor or engineer in future. International news not really important, only waste of time.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">In my opinion, secondary student should play sport and learn math better than learn international news. Teacher should teaching student good subject not international news.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">I conclusion, international news have advantage but more disadvantage. Student no need study this subject, waste of their time.</span>\n                    \"\"\"\n                  \"<span class=sf-dump-key>task_type</span>\" => \"<span class=sf-dump-str title=\"5 characters\">task2</span>\"\n                  \"<span class=sf-dump-key>time_limit</span>\" => <span class=sf-dump-num>40</span>\n                  \"<span class=sf-dump-key>overall_band_score</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.4</span>\"\n                  \"<span class=sf-dump-key>task_achievement</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.1</span>\"\n                  \"<span class=sf-dump-key>coherence_cohesion</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.4</span>\"\n                  \"<span class=sf-dump-key>lexical_resource</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.6</span>\"\n                  \"<span class=sf-dump-key>grammar_accuracy</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.3</span>\"\n                  \"<span class=sf-dump-key>detailed_feedback</span>\" => \"<span class=sf-dump-str title=\"210 characters\">{&quot;task_achievement&quot;: {&quot;score&quot;: 4.4, &quot;issues&quot;: [&quot;&#272;&#226;y l&#224; ph&#7843;n h&#7891;i demo&quot;], &quot;feedback&quot;: &quot;H&#7879; th&#7889;ng &#273;ang trong ch&#7871; &#273;&#7897; demo. &#272;&#226;y l&#224; k&#7871;t qu&#7843; m&#7851;u.&quot;, &quot;improvements&quot;: [&quot;Vui l&#242;ng th&#7917; l&#7841;i sau khi h&#7879; th&#7889;ng &#273;&#432;&#7907;c kh&#244;i ph&#7909;c&quot;]}}</span>\"\n                  \"<span class=sf-dump-key>highlighted_corrections</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n                  \"<span class=sf-dump-key>criteria_analysis</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n                  \"<span class=sf-dump-key>statistics</span>\" => \"<span class=sf-dump-str title=\"153 characters\">{&quot;total_errors&quot;: 10, &quot;grammar_errors&quot;: 4, &quot;spelling_errors&quot;: 4, &quot;complexity_level&quot;: &quot;intermediate&quot;, &quot;sentence_variety&quot;: &quot;medium&quot;, &quot;vocabulary_errors&quot;: 2}</span>\"\n                  \"<span class=sf-dump-key>word_count</span>\" => <span class=sf-dump-num>179</span>\n                  \"<span class=sf-dump-key>essay_length</span>\" => <span class=sf-dump-num>1184</span>\n                  \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"9 characters\">completed</span>\"\n                  \"<span class=sf-dump-key>error_message</span>\" => <span class=sf-dump-const>null</span>\n                  \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-18 14:54:37</span>\"\n                  \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-18 14:54:41</span>\"\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:21</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>4</span>\n                  \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-num>1</span>\n                  \"<span class=sf-dump-key>essay_question_id</span>\" => <span class=sf-dump-const>null</span>\n                  \"<span class=sf-dump-key>essay_content</span>\" => \"\"\"\n                    <span class=sf-dump-str title=\"1184 characters\">International news is good for student but sometime it is not nessessary. Many people think student in secondary school need to study about international news, other think this waste their time. My essay will write about both of these thing.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">Firstly, International news is good because it make student smart. When student learn international news they can know many things. Example, student can know weather in other country, famous people life, or footbal match. They will very interesting about these topic and can relax in the classroom. Learning international news is also help student speaking english well.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">However, I thinking international news sometime is boring. Student have many important subject, example maths, literature, or chemistry. These subjects can help student become doctor or engineer in future. International news not really important, only waste of time.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">In my opinion, secondary student should play sport and learn math better than learn international news. Teacher should teaching student good subject not international news.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">I conclusion, international news have advantage but more disadvantage. Student no need study this subject, waste of their time.</span>\n                    \"\"\"\n                  \"<span class=sf-dump-key>task_type</span>\" => \"<span class=sf-dump-str title=\"5 characters\">task2</span>\"\n                  \"<span class=sf-dump-key>time_limit</span>\" => <span class=sf-dump-num>40</span>\n                  \"<span class=sf-dump-key>overall_band_score</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.4</span>\"\n                  \"<span class=sf-dump-key>task_achievement</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.1</span>\"\n                  \"<span class=sf-dump-key>coherence_cohesion</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.4</span>\"\n                  \"<span class=sf-dump-key>lexical_resource</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.6</span>\"\n                  \"<span class=sf-dump-key>grammar_accuracy</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.3</span>\"\n                  \"<span class=sf-dump-key>detailed_feedback</span>\" => \"<span class=sf-dump-str title=\"210 characters\">{&quot;task_achievement&quot;: {&quot;score&quot;: 4.4, &quot;issues&quot;: [&quot;&#272;&#226;y l&#224; ph&#7843;n h&#7891;i demo&quot;], &quot;feedback&quot;: &quot;H&#7879; th&#7889;ng &#273;ang trong ch&#7871; &#273;&#7897; demo. &#272;&#226;y l&#224; k&#7871;t qu&#7843; m&#7851;u.&quot;, &quot;improvements&quot;: [&quot;Vui l&#242;ng th&#7917; l&#7841;i sau khi h&#7879; th&#7889;ng &#273;&#432;&#7907;c kh&#244;i ph&#7909;c&quot;]}}</span>\"\n                  \"<span class=sf-dump-key>highlighted_corrections</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n                  \"<span class=sf-dump-key>criteria_analysis</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n                  \"<span class=sf-dump-key>statistics</span>\" => \"<span class=sf-dump-str title=\"153 characters\">{&quot;total_errors&quot;: 10, &quot;grammar_errors&quot;: 4, &quot;spelling_errors&quot;: 4, &quot;complexity_level&quot;: &quot;intermediate&quot;, &quot;sentence_variety&quot;: &quot;medium&quot;, &quot;vocabulary_errors&quot;: 2}</span>\"\n                  \"<span class=sf-dump-key>word_count</span>\" => <span class=sf-dump-num>179</span>\n                  \"<span class=sf-dump-key>essay_length</span>\" => <span class=sf-dump-num>1184</span>\n                  \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"9 characters\">completed</span>\"\n                  \"<span class=sf-dump-key>error_message</span>\" => <span class=sf-dump-const>null</span>\n                  \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-18 14:54:37</span>\"\n                  \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-18 14:54:41</span>\"\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">previous</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:9</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>detailed_feedback</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                  \"<span class=sf-dump-key>highlighted_corrections</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                  \"<span class=sf-dump-key>criteria_analysis</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                  \"<span class=sf-dump-key>statistics</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                  \"<span class=sf-dump-key>overall_band_score</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:1</span>\"\n                  \"<span class=sf-dump-key>task_achievement</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:1</span>\"\n                  \"<span class=sf-dump-key>coherence_cohesion</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:1</span>\"\n                  \"<span class=sf-dump-key>lexical_resource</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:1</span>\"\n                  \"<span class=sf-dump-key>grammar_accuracy</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:1</span>\"\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>essayQuestion</span>\" => <span class=sf-dump-const>null</span>\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadCallback</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadContext</span>: <span class=sf-dump-const>null</span>\n                +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:18</span> [<samp data-depth=9 class=sf-dump-compact>\n                  <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">user_id</span>\"\n                  <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"17 characters\">essay_question_id</span>\"\n                  <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"13 characters\">essay_content</span>\"\n                  <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"9 characters\">task_type</span>\"\n                  <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"10 characters\">time_limit</span>\"\n                  <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"18 characters\">overall_band_score</span>\"\n                  <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"16 characters\">task_achievement</span>\"\n                  <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"18 characters\">coherence_cohesion</span>\"\n                  <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"16 characters\">lexical_resource</span>\"\n                  <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"16 characters\">grammar_accuracy</span>\"\n                  <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"17 characters\">detailed_feedback</span>\"\n                  <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"23 characters\">highlighted_corrections</span>\"\n                  <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"17 characters\">criteria_analysis</span>\"\n                  <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"10 characters\">statistics</span>\"\n                  <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"10 characters\">word_count</span>\"\n                  <span class=sf-dump-index>15</span> => \"<span class=sf-dump-str title=\"12 characters\">essay_length</span>\"\n                  <span class=sf-dump-index>16</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n                  <span class=sf-dump-index>17</span> => \"<span class=sf-dump-str title=\"13 characters\">error_message</span>\"\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=9 class=sf-dump-compact>\n                  <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n                </samp>]\n              </samp>}\n              <span class=sf-dump-index>3</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\ScoringAttempt\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ScoringAttempt</span></span> {<a class=sf-dump-ref>#1486</a><samp data-depth=8 class=sf-dump-compact>\n                #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"16 characters\">scoring_attempts</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:21</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>2</span>\n                  \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-num>1</span>\n                  \"<span class=sf-dump-key>essay_question_id</span>\" => <span class=sf-dump-const>null</span>\n                  \"<span class=sf-dump-key>essay_content</span>\" => \"\"\"\n                    <span class=sf-dump-str title=\"1184 characters\">International news is good for student but sometime it is not nessessary. Many people think student in secondary school need to study about international news, other think this waste their time. My essay will write about both of these thing.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">Firstly, International news is good because it make student smart. When student learn international news they can know many things. Example, student can know weather in other country, famous people life, or footbal match. They will very interesting about these topic and can relax in the classroom. Learning international news is also help student speaking english well.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">However, I thinking international news sometime is boring. Student have many important subject, example maths, literature, or chemistry. These subjects can help student become doctor or engineer in future. International news not really important, only waste of time.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">In my opinion, secondary student should play sport and learn math better than learn international news. Teacher should teaching student good subject not international news.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">I conclusion, international news have advantage but more disadvantage. Student no need study this subject, waste of their time.</span>\n                    \"\"\"\n                  \"<span class=sf-dump-key>task_type</span>\" => \"<span class=sf-dump-str title=\"5 characters\">task2</span>\"\n                  \"<span class=sf-dump-key>time_limit</span>\" => <span class=sf-dump-num>40</span>\n                  \"<span class=sf-dump-key>overall_band_score</span>\" => \"<span class=sf-dump-str title=\"3 characters\">3.9</span>\"\n                  \"<span class=sf-dump-key>task_achievement</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.2</span>\"\n                  \"<span class=sf-dump-key>coherence_cohesion</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.0</span>\"\n                  \"<span class=sf-dump-key>lexical_resource</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.1</span>\"\n                  \"<span class=sf-dump-key>grammar_accuracy</span>\" => \"<span class=sf-dump-str title=\"3 characters\">3.8</span>\"\n                  \"<span class=sf-dump-key>detailed_feedback</span>\" => \"<span class=sf-dump-str title=\"210 characters\">{&quot;task_achievement&quot;: {&quot;score&quot;: 3.9, &quot;issues&quot;: [&quot;&#272;&#226;y l&#224; ph&#7843;n h&#7891;i demo&quot;], &quot;feedback&quot;: &quot;H&#7879; th&#7889;ng &#273;ang trong ch&#7871; &#273;&#7897; demo. &#272;&#226;y l&#224; k&#7871;t qu&#7843; m&#7851;u.&quot;, &quot;improvements&quot;: [&quot;Vui l&#242;ng th&#7917; l&#7841;i sau khi h&#7879; th&#7889;ng &#273;&#432;&#7907;c kh&#244;i ph&#7909;c&quot;]}}</span>\"\n                  \"<span class=sf-dump-key>highlighted_corrections</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n                  \"<span class=sf-dump-key>criteria_analysis</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n                  \"<span class=sf-dump-key>statistics</span>\" => \"<span class=sf-dump-str title=\"152 characters\">{&quot;total_errors&quot;: 8, &quot;grammar_errors&quot;: 3, &quot;spelling_errors&quot;: 5, &quot;complexity_level&quot;: &quot;intermediate&quot;, &quot;sentence_variety&quot;: &quot;medium&quot;, &quot;vocabulary_errors&quot;: 2}</span>\"\n                  \"<span class=sf-dump-key>word_count</span>\" => <span class=sf-dump-num>179</span>\n                  \"<span class=sf-dump-key>essay_length</span>\" => <span class=sf-dump-num>1184</span>\n                  \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"9 characters\">completed</span>\"\n                  \"<span class=sf-dump-key>error_message</span>\" => <span class=sf-dump-const>null</span>\n                  \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-18 14:46:07</span>\"\n                  \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-18 14:46:11</span>\"\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:21</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>2</span>\n                  \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-num>1</span>\n                  \"<span class=sf-dump-key>essay_question_id</span>\" => <span class=sf-dump-const>null</span>\n                  \"<span class=sf-dump-key>essay_content</span>\" => \"\"\"\n                    <span class=sf-dump-str title=\"1184 characters\">International news is good for student but sometime it is not nessessary. Many people think student in secondary school need to study about international news, other think this waste their time. My essay will write about both of these thing.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">Firstly, International news is good because it make student smart. When student learn international news they can know many things. Example, student can know weather in other country, famous people life, or footbal match. They will very interesting about these topic and can relax in the classroom. Learning international news is also help student speaking english well.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">However, I thinking international news sometime is boring. Student have many important subject, example maths, literature, or chemistry. These subjects can help student become doctor or engineer in future. International news not really important, only waste of time.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">In my opinion, secondary student should play sport and learn math better than learn international news. Teacher should teaching student good subject not international news.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">I conclusion, international news have advantage but more disadvantage. Student no need study this subject, waste of their time.</span>\n                    \"\"\"\n                  \"<span class=sf-dump-key>task_type</span>\" => \"<span class=sf-dump-str title=\"5 characters\">task2</span>\"\n                  \"<span class=sf-dump-key>time_limit</span>\" => <span class=sf-dump-num>40</span>\n                  \"<span class=sf-dump-key>overall_band_score</span>\" => \"<span class=sf-dump-str title=\"3 characters\">3.9</span>\"\n                  \"<span class=sf-dump-key>task_achievement</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.2</span>\"\n                  \"<span class=sf-dump-key>coherence_cohesion</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.0</span>\"\n                  \"<span class=sf-dump-key>lexical_resource</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.1</span>\"\n                  \"<span class=sf-dump-key>grammar_accuracy</span>\" => \"<span class=sf-dump-str title=\"3 characters\">3.8</span>\"\n                  \"<span class=sf-dump-key>detailed_feedback</span>\" => \"<span class=sf-dump-str title=\"210 characters\">{&quot;task_achievement&quot;: {&quot;score&quot;: 3.9, &quot;issues&quot;: [&quot;&#272;&#226;y l&#224; ph&#7843;n h&#7891;i demo&quot;], &quot;feedback&quot;: &quot;H&#7879; th&#7889;ng &#273;ang trong ch&#7871; &#273;&#7897; demo. &#272;&#226;y l&#224; k&#7871;t qu&#7843; m&#7851;u.&quot;, &quot;improvements&quot;: [&quot;Vui l&#242;ng th&#7917; l&#7841;i sau khi h&#7879; th&#7889;ng &#273;&#432;&#7907;c kh&#244;i ph&#7909;c&quot;]}}</span>\"\n                  \"<span class=sf-dump-key>highlighted_corrections</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n                  \"<span class=sf-dump-key>criteria_analysis</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n                  \"<span class=sf-dump-key>statistics</span>\" => \"<span class=sf-dump-str title=\"152 characters\">{&quot;total_errors&quot;: 8, &quot;grammar_errors&quot;: 3, &quot;spelling_errors&quot;: 5, &quot;complexity_level&quot;: &quot;intermediate&quot;, &quot;sentence_variety&quot;: &quot;medium&quot;, &quot;vocabulary_errors&quot;: 2}</span>\"\n                  \"<span class=sf-dump-key>word_count</span>\" => <span class=sf-dump-num>179</span>\n                  \"<span class=sf-dump-key>essay_length</span>\" => <span class=sf-dump-num>1184</span>\n                  \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"9 characters\">completed</span>\"\n                  \"<span class=sf-dump-key>error_message</span>\" => <span class=sf-dump-const>null</span>\n                  \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-18 14:46:07</span>\"\n                  \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-18 14:46:11</span>\"\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">previous</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:9</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>detailed_feedback</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                  \"<span class=sf-dump-key>highlighted_corrections</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                  \"<span class=sf-dump-key>criteria_analysis</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                  \"<span class=sf-dump-key>statistics</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                  \"<span class=sf-dump-key>overall_band_score</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:1</span>\"\n                  \"<span class=sf-dump-key>task_achievement</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:1</span>\"\n                  \"<span class=sf-dump-key>coherence_cohesion</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:1</span>\"\n                  \"<span class=sf-dump-key>lexical_resource</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:1</span>\"\n                  \"<span class=sf-dump-key>grammar_accuracy</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:1</span>\"\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>essayQuestion</span>\" => <span class=sf-dump-const>null</span>\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadCallback</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadContext</span>: <span class=sf-dump-const>null</span>\n                +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:18</span> [<samp data-depth=9 class=sf-dump-compact>\n                  <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">user_id</span>\"\n                  <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"17 characters\">essay_question_id</span>\"\n                  <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"13 characters\">essay_content</span>\"\n                  <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"9 characters\">task_type</span>\"\n                  <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"10 characters\">time_limit</span>\"\n                  <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"18 characters\">overall_band_score</span>\"\n                  <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"16 characters\">task_achievement</span>\"\n                  <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"18 characters\">coherence_cohesion</span>\"\n                  <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"16 characters\">lexical_resource</span>\"\n                  <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"16 characters\">grammar_accuracy</span>\"\n                  <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"17 characters\">detailed_feedback</span>\"\n                  <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"23 characters\">highlighted_corrections</span>\"\n                  <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"17 characters\">criteria_analysis</span>\"\n                  <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"10 characters\">statistics</span>\"\n                  <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"10 characters\">word_count</span>\"\n                  <span class=sf-dump-index>15</span> => \"<span class=sf-dump-str title=\"12 characters\">essay_length</span>\"\n                  <span class=sf-dump-index>16</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n                  <span class=sf-dump-index>17</span> => \"<span class=sf-dump-str title=\"13 characters\">error_message</span>\"\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=9 class=sf-dump-compact>\n                  <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n                </samp>]\n              </samp>}\n              <span class=sf-dump-index>4</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\ScoringAttempt\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ScoringAttempt</span></span> {<a class=sf-dump-ref>#1485</a><samp data-depth=8 class=sf-dump-compact>\n                #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"16 characters\">scoring_attempts</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n                +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n                #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n                +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n                +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n                +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:21</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n                  \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-num>1</span>\n                  \"<span class=sf-dump-key>essay_question_id</span>\" => <span class=sf-dump-const>null</span>\n                  \"<span class=sf-dump-key>essay_content</span>\" => \"\"\"\n                    <span class=sf-dump-str title=\"1184 characters\">International news is good for student but sometime it is not nessessary. Many people think student in secondary school need to study about international news, other think this waste their time. My essay will write about both of these thing.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">Firstly, International news is good because it make student smart. When student learn international news they can know many things. Example, student can know weather in other country, famous people life, or footbal match. They will very interesting about these topic and can relax in the classroom. Learning international news is also help student speaking english well.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">However, I thinking international news sometime is boring. Student have many important subject, example maths, literature, or chemistry. These subjects can help student become doctor or engineer in future. International news not really important, only waste of time.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">In my opinion, secondary student should play sport and learn math better than learn international news. Teacher should teaching student good subject not international news.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">I conclusion, international news have advantage but more disadvantage. Student no need study this subject, waste of their time.</span>\n                    \"\"\"\n                  \"<span class=sf-dump-key>task_type</span>\" => \"<span class=sf-dump-str title=\"5 characters\">task2</span>\"\n                  \"<span class=sf-dump-key>time_limit</span>\" => <span class=sf-dump-num>40</span>\n                  \"<span class=sf-dump-key>overall_band_score</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.7</span>\"\n                  \"<span class=sf-dump-key>task_achievement</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.4</span>\"\n                  \"<span class=sf-dump-key>coherence_cohesion</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.5</span>\"\n                  \"<span class=sf-dump-key>lexical_resource</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.8</span>\"\n                  \"<span class=sf-dump-key>grammar_accuracy</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.4</span>\"\n                  \"<span class=sf-dump-key>detailed_feedback</span>\" => \"<span class=sf-dump-str title=\"210 characters\">{&quot;task_achievement&quot;: {&quot;score&quot;: 4.7, &quot;issues&quot;: [&quot;&#272;&#226;y l&#224; ph&#7843;n h&#7891;i demo&quot;], &quot;feedback&quot;: &quot;H&#7879; th&#7889;ng &#273;ang trong ch&#7871; &#273;&#7897; demo. &#272;&#226;y l&#224; k&#7871;t qu&#7843; m&#7851;u.&quot;, &quot;improvements&quot;: [&quot;Vui l&#242;ng th&#7917; l&#7841;i sau khi h&#7879; th&#7889;ng &#273;&#432;&#7907;c kh&#244;i ph&#7909;c&quot;]}}</span>\"\n                  \"<span class=sf-dump-key>highlighted_corrections</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n                  \"<span class=sf-dump-key>criteria_analysis</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n                  \"<span class=sf-dump-key>statistics</span>\" => \"<span class=sf-dump-str title=\"153 characters\">{&quot;total_errors&quot;: 12, &quot;grammar_errors&quot;: 7, &quot;spelling_errors&quot;: 4, &quot;complexity_level&quot;: &quot;intermediate&quot;, &quot;sentence_variety&quot;: &quot;medium&quot;, &quot;vocabulary_errors&quot;: 2}</span>\"\n                  \"<span class=sf-dump-key>word_count</span>\" => <span class=sf-dump-num>179</span>\n                  \"<span class=sf-dump-key>essay_length</span>\" => <span class=sf-dump-num>1184</span>\n                  \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"9 characters\">completed</span>\"\n                  \"<span class=sf-dump-key>error_message</span>\" => <span class=sf-dump-const>null</span>\n                  \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-18 14:44:34</span>\"\n                  \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-18 14:44:38</span>\"\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:21</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n                  \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-num>1</span>\n                  \"<span class=sf-dump-key>essay_question_id</span>\" => <span class=sf-dump-const>null</span>\n                  \"<span class=sf-dump-key>essay_content</span>\" => \"\"\"\n                    <span class=sf-dump-str title=\"1184 characters\">International news is good for student but sometime it is not nessessary. Many people think student in secondary school need to study about international news, other think this waste their time. My essay will write about both of these thing.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">Firstly, International news is good because it make student smart. When student learn international news they can know many things. Example, student can know weather in other country, famous people life, or footbal match. They will very interesting about these topic and can relax in the classroom. Learning international news is also help student speaking english well.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">However, I thinking international news sometime is boring. Student have many important subject, example maths, literature, or chemistry. These subjects can help student become doctor or engineer in future. International news not really important, only waste of time.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">In my opinion, secondary student should play sport and learn math better than learn international news. Teacher should teaching student good subject not international news.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n                    <span class=sf-dump-str title=\"1184 characters\">I conclusion, international news have advantage but more disadvantage. Student no need study this subject, waste of their time.</span>\n                    \"\"\"\n                  \"<span class=sf-dump-key>task_type</span>\" => \"<span class=sf-dump-str title=\"5 characters\">task2</span>\"\n                  \"<span class=sf-dump-key>time_limit</span>\" => <span class=sf-dump-num>40</span>\n                  \"<span class=sf-dump-key>overall_band_score</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.7</span>\"\n                  \"<span class=sf-dump-key>task_achievement</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.4</span>\"\n                  \"<span class=sf-dump-key>coherence_cohesion</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.5</span>\"\n                  \"<span class=sf-dump-key>lexical_resource</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.8</span>\"\n                  \"<span class=sf-dump-key>grammar_accuracy</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.4</span>\"\n                  \"<span class=sf-dump-key>detailed_feedback</span>\" => \"<span class=sf-dump-str title=\"210 characters\">{&quot;task_achievement&quot;: {&quot;score&quot;: 4.7, &quot;issues&quot;: [&quot;&#272;&#226;y l&#224; ph&#7843;n h&#7891;i demo&quot;], &quot;feedback&quot;: &quot;H&#7879; th&#7889;ng &#273;ang trong ch&#7871; &#273;&#7897; demo. &#272;&#226;y l&#224; k&#7871;t qu&#7843; m&#7851;u.&quot;, &quot;improvements&quot;: [&quot;Vui l&#242;ng th&#7917; l&#7841;i sau khi h&#7879; th&#7889;ng &#273;&#432;&#7907;c kh&#244;i ph&#7909;c&quot;]}}</span>\"\n                  \"<span class=sf-dump-key>highlighted_corrections</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n                  \"<span class=sf-dump-key>criteria_analysis</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n                  \"<span class=sf-dump-key>statistics</span>\" => \"<span class=sf-dump-str title=\"153 characters\">{&quot;total_errors&quot;: 12, &quot;grammar_errors&quot;: 7, &quot;spelling_errors&quot;: 4, &quot;complexity_level&quot;: &quot;intermediate&quot;, &quot;sentence_variety&quot;: &quot;medium&quot;, &quot;vocabulary_errors&quot;: 2}</span>\"\n                  \"<span class=sf-dump-key>word_count</span>\" => <span class=sf-dump-num>179</span>\n                  \"<span class=sf-dump-key>essay_length</span>\" => <span class=sf-dump-num>1184</span>\n                  \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"9 characters\">completed</span>\"\n                  \"<span class=sf-dump-key>error_message</span>\" => <span class=sf-dump-const>null</span>\n                  \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-18 14:44:34</span>\"\n                  \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-18 14:44:38</span>\"\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">previous</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:9</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>detailed_feedback</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                  \"<span class=sf-dump-key>highlighted_corrections</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                  \"<span class=sf-dump-key>criteria_analysis</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                  \"<span class=sf-dump-key>statistics</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                  \"<span class=sf-dump-key>overall_band_score</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:1</span>\"\n                  \"<span class=sf-dump-key>task_achievement</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:1</span>\"\n                  \"<span class=sf-dump-key>coherence_cohesion</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:1</span>\"\n                  \"<span class=sf-dump-key>lexical_resource</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:1</span>\"\n                  \"<span class=sf-dump-key>grammar_accuracy</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:1</span>\"\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>essayQuestion</span>\" => <span class=sf-dump-const>null</span>\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadCallback</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadContext</span>: <span class=sf-dump-const>null</span>\n                +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n                +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n                #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n                #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:18</span> [<samp data-depth=9 class=sf-dump-compact>\n                  <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">user_id</span>\"\n                  <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"17 characters\">essay_question_id</span>\"\n                  <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"13 characters\">essay_content</span>\"\n                  <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"9 characters\">task_type</span>\"\n                  <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"10 characters\">time_limit</span>\"\n                  <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"18 characters\">overall_band_score</span>\"\n                  <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"16 characters\">task_achievement</span>\"\n                  <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"18 characters\">coherence_cohesion</span>\"\n                  <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"16 characters\">lexical_resource</span>\"\n                  <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"16 characters\">grammar_accuracy</span>\"\n                  <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"17 characters\">detailed_feedback</span>\"\n                  <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"23 characters\">highlighted_corrections</span>\"\n                  <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"17 characters\">criteria_analysis</span>\"\n                  <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"10 characters\">statistics</span>\"\n                  <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"10 characters\">word_count</span>\"\n                  <span class=sf-dump-index>15</span> => \"<span class=sf-dump-str title=\"12 characters\">essay_length</span>\"\n                  <span class=sf-dump-index>16</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n                  <span class=sf-dump-index>17</span> => \"<span class=sf-dump-str title=\"13 characters\">error_message</span>\"\n                </samp>]\n                #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=9 class=sf-dump-compact>\n                  <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n                </samp>]\n              </samp>}\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n          </samp>}\n          #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>10</span>\n          #<span class=sf-dump-protected title=\"Protected property\">currentPage</span>: <span class=sf-dump-num>1</span>\n          #<span class=sf-dump-protected title=\"Protected property\">path</span>: \"<span class=sf-dump-str title=\"37 characters\">http://127.0.0.1:8000/scoring/history</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">query</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">fragment</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">pageName</span>: \"<span class=sf-dump-str title=\"4 characters\">page</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n          +<span class=sf-dump-public title=\"Public property\">onEachSide</span>: <span class=sf-dump-num>3</span>\n          #<span class=sf-dump-protected title=\"Protected property\">options</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://127.0.0.1:8000/scoring/history</span>\"\n            \"<span class=sf-dump-key>pageName</span>\" => \"<span class=sf-dump-str title=\"4 characters\">page</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">total</span>: <span class=sf-dump-num>5</span>\n          #<span class=sf-dump-protected title=\"Protected property\">lastPage</span>: <span class=sf-dump-num>1</span>\n        </samp>}\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"71 characters\">vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>76</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"12 characters\">evaluatePath</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\View\\Engines\\PhpEngine</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"102 characters\">C:\\laragon\\www\\ielts\\ielts-scoring-system\\storage\\framework\\views/3d0e4b2b3d75180b3dbc322942eb1a4c.php</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>__env</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\View\\Factory\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\View</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Factory</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21305 title=\"3 occurrences\">#1305</a> &#8230;25}\n        \"<span class=sf-dump-key>app</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref24 title=\"3 occurrences\">#4</a> &#8230;44}\n        \"<span class=sf-dump-key>errors</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\ViewErrorBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ViewErrorBag</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21430 title=\"3 occurrences\">#1430</a>}\n        \"<span class=sf-dump-key>attempts</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Pagination\\LengthAwarePaginator\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Pagination</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">LengthAwarePaginator</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21484 title=\"3 occurrences\">#1484</a>}\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"53 characters\">vendor/laravel/framework/src/Illuminate/View/View.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"3 characters\">get</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"38 characters\">Illuminate\\View\\Engines\\CompilerEngine</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"83 characters\">C:\\laragon\\www\\ielts\\ielts-scoring-system\\resources\\views/scoring/history.blade.php</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>__env</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\View\\Factory\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\View</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Factory</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21305 title=\"3 occurrences\">#1305</a> &#8230;25}\n        \"<span class=sf-dump-key>app</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref24 title=\"3 occurrences\">#4</a> &#8230;44}\n        \"<span class=sf-dump-key>errors</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\ViewErrorBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ViewErrorBag</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21430 title=\"3 occurrences\">#1430</a>}\n        \"<span class=sf-dump-key>attempts</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Pagination\\LengthAwarePaginator\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Pagination</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">LengthAwarePaginator</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21484 title=\"3 occurrences\">#1484</a>}\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"53 characters\">vendor/laravel/framework/src/Illuminate/View/View.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>191</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"11 characters\">getContents</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Illuminate\\View\\View</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>11</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"53 characters\">vendor/laravel/framework/src/Illuminate/View/View.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>160</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"14 characters\">renderContents</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Illuminate\\View\\View</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>12</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"57 characters\">vendor/laravel/framework/src/Illuminate/Http/Response.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>78</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">render</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Illuminate\\View\\View</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>13</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"57 characters\">vendor/laravel/framework/src/Illuminate/Http/Response.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>34</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"10 characters\">setContent</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Illuminate\\Http\\Response</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">[object Illuminate\\View\\View]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>14</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>924</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"11 characters\">__construct</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Illuminate\\Http\\Response</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">[object Illuminate\\View\\View]</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-num>200</span>\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>Content-Type</span>\" => \"<span class=sf-dump-str title=\"9 characters\">text/html</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>15</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>891</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"10 characters\">toResponse</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">::</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"29 characters\">[object Illuminate\\View\\View]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>16</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>807</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"15 characters\">prepareResponse</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"29 characters\">[object Illuminate\\View\\View]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>17</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>169</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Routing\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>18</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>66</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>19</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Barryvdh\\Debugbar\\Middleware\\InjectDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>20</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"43 characters\">app/Http/Middleware/EnsureUserHasCredit.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>32</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>21</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">App\\Http\\Middleware\\EnsureUserHasCredit</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>22</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"81 characters\">vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>50</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>23</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"48 characters\">Illuminate\\Routing\\Middleware\\SubstituteBindings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>24</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>63</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>25</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Auth\\Middleware\\Authenticate</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>26</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"86 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>87</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>27</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"53 characters\">Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>28</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>48</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>29</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\View\\Middleware\\ShareErrorsFromSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>30</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"75 characters\">vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>120</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>31</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"75 characters\">vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>63</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"21 characters\">handleStatefulRequest</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Illuminate\\Session\\Middleware\\StartSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Session\\Store]</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>32</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Illuminate\\Session\\Middleware\\StartSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>33</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>36</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>34</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>35</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"76 characters\">vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>74</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>36</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Illuminate\\Cookie\\Middleware\\EncryptCookies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>37</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>126</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>38</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>807</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>39</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>786</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"19 characters\">runRouteWithinStack</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>40</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>750</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">runRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>41</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>739</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"15 characters\">dispatchToRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>42</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>200</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">dispatch</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>43</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>169</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Illuminate\\Foundation\\Http\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>44</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>66</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>45</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Barryvdh\\Debugbar\\Middleware\\InjectDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>46</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>47</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"96 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>31</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>48</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"63 characters\">Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>49</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>50</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>51</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>51</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\Foundation\\Http\\Middleware\\TrimStrings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>52</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"76 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>27</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>53</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Illuminate\\Http\\Middleware\\ValidatePostSize</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>54</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"103 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>109</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>55</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"70 characters\">Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>56</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"70 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>48</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>57</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"37 characters\">Illuminate\\Http\\Middleware\\HandleCors</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>58</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>58</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>59</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Http\\Middleware\\TrustProxies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>60</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"94 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>22</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>61</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"61 characters\">Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>62</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"80 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePathEncoding.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>26</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>63</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"47 characters\">Illuminate\\Http\\Middleware\\ValidatePathEncoding</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>64</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>126</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>65</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>175</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>66</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>144</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"24 characters\">sendRequestThroughRouter</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>67</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Application.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>1219</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>68</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"16 characters\">public/index.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>20</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"13 characters\">handleRequest</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Application</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>69</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"71 characters\">vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>23</span>\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"58 characters\">C:\\laragon\\www\\ielts\\ielts-scoring-system\\public\\index.php</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"12 characters\">require_once</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "surrounding_lines": ["\n", "        $message .= '.';\n", "\n", "        return new static($message);\n", "    }\n", "}\n"], "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FRouting%2FExceptions%2FUrlGenerationException.php&line=35", "ajax": false, "filename": "UrlGenerationException.php", "line": "35"}}]}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.20.0", "PHP Version": "8.2.27", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost", "Timezone": "Asia/Ho_Chi_Minh", "Locale": "en"}}, "views": {"count": 23, "nb_templates": 23, "templates": [{"name": "scoring.history", "param_count": null, "params": [], "start": **********.083663, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\resources\\views/scoring/history.blade.phpscoring.history", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fresources%2Fviews%2Fscoring%2Fhistory.blade.php&line=1", "ajax": false, "filename": "history.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::show", "param_count": null, "params": [], "start": **********.329271, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/show.blade.phplaravel-exceptions-renderer::show", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fshow.blade.php&line=1", "ajax": false, "filename": "show.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.navigation", "param_count": null, "params": [], "start": **********.330992, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/navigation.blade.phplaravel-exceptions-renderer::components.navigation", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Fnavigation.blade.php&line=1", "ajax": false, "filename": "navigation.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.theme-switcher", "param_count": null, "params": [], "start": **********.331362, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/theme-switcher.blade.phplaravel-exceptions-renderer::components.theme-switcher", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ftheme-switcher.blade.php&line=1", "ajax": false, "filename": "theme-switcher.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.icons.sun", "param_count": null, "params": [], "start": **********.331794, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/icons/sun.blade.phplaravel-exceptions-renderer::components.icons.sun", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ficons%2Fsun.blade.php&line=1", "ajax": false, "filename": "sun.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.icons.moon", "param_count": null, "params": [], "start": **********.332092, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/icons/moon.blade.phplaravel-exceptions-renderer::components.icons.moon", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ficons%2Fmoon.blade.php&line=1", "ajax": false, "filename": "moon.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.icons.sun", "param_count": null, "params": [], "start": **********.332345, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/icons/sun.blade.phplaravel-exceptions-renderer::components.icons.sun", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ficons%2Fsun.blade.php&line=1", "ajax": false, "filename": "sun.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.icons.moon", "param_count": null, "params": [], "start": **********.332542, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/icons/moon.blade.phplaravel-exceptions-renderer::components.icons.moon", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ficons%2Fmoon.blade.php&line=1", "ajax": false, "filename": "moon.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.icons.computer-desktop", "param_count": null, "params": [], "start": **********.332774, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/icons/computer-desktop.blade.phplaravel-exceptions-renderer::components.icons.computer-desktop", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ficons%2Fcomputer-desktop.blade.php&line=1", "ajax": false, "filename": "computer-desktop.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.header", "param_count": null, "params": [], "start": **********.333068, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/header.blade.phplaravel-exceptions-renderer::components.header", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.card", "param_count": null, "params": [], "start": **********.333442, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/card.blade.phplaravel-exceptions-renderer::components.card", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Fcard.blade.php&line=1", "ajax": false, "filename": "card.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.trace-and-editor", "param_count": null, "params": [], "start": **********.333768, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/trace-and-editor.blade.phplaravel-exceptions-renderer::components.trace-and-editor", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ftrace-and-editor.blade.php&line=1", "ajax": false, "filename": "trace-and-editor.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.trace", "param_count": null, "params": [], "start": **********.343399, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/trace.blade.phplaravel-exceptions-renderer::components.trace", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ftrace.blade.php&line=1", "ajax": false, "filename": "trace.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.icons.chevron-down", "param_count": null, "params": [], "start": **********.344052, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/icons/chevron-down.blade.phplaravel-exceptions-renderer::components.icons.chevron-down", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ficons%2Fchevron-down.blade.php&line=1", "ajax": false, "filename": "chevron-down.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.icons.chevron-up", "param_count": null, "params": [], "start": **********.344486, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/icons/chevron-up.blade.phplaravel-exceptions-renderer::components.icons.chevron-up", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ficons%2Fchevron-up.blade.php&line=1", "ajax": false, "filename": "chevron-up.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.icons.chevron-up", "param_count": null, "params": [], "start": **********.344827, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/icons/chevron-up.blade.phplaravel-exceptions-renderer::components.icons.chevron-up", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ficons%2Fchevron-up.blade.php&line=1", "ajax": false, "filename": "chevron-up.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.icons.chevron-down", "param_count": null, "params": [], "start": **********.345025, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/icons/chevron-down.blade.phplaravel-exceptions-renderer::components.icons.chevron-down", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ficons%2Fchevron-down.blade.php&line=1", "ajax": false, "filename": "chevron-down.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.editor", "param_count": null, "params": [], "start": **********.365553, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/editor.blade.phplaravel-exceptions-renderer::components.editor", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Feditor.blade.php&line=1", "ajax": false, "filename": "editor.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.card", "param_count": null, "params": [], "start": **********.408085, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/card.blade.phplaravel-exceptions-renderer::components.card", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Fcard.blade.php&line=1", "ajax": false, "filename": "card.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.context", "param_count": null, "params": [], "start": **********.408469, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/context.blade.phplaravel-exceptions-renderer::components.context", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Fcontext.blade.php&line=1", "ajax": false, "filename": "context.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.card", "param_count": null, "params": [], "start": **********.409013, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/card.blade.phplaravel-exceptions-renderer::components.card", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Fcard.blade.php&line=1", "ajax": false, "filename": "card.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.card", "param_count": null, "params": [], "start": **********.409326, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/card.blade.phplaravel-exceptions-renderer::components.card", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Fcard.blade.php&line=1", "ajax": false, "filename": "card.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.layout", "param_count": null, "params": [], "start": **********.409556, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/layout.blade.phplaravel-exceptions-renderer::components.layout", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Flayout.blade.php&line=1", "ajax": false, "filename": "layout.blade.php", "line": "?"}}]}, "queries": {"count": 6, "nb_statements": 5, "nb_visible_statements": 6, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00411, "accumulated_duration_str": "4.11ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 158}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 56}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}], "start": **********.050843, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "ietls", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `sessions` where `id` = 'TGbnOTYIBZUzNJj0xv9nDBkjG9tZftJvLifPfv1e' limit 1", "type": "query", "params": [], "bindings": ["TGbnOTYIBZUzNJj0xv9nDBkjG9tZftJvLifPfv1e"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.054899, "duration": 0.00199, "duration_str": "1.99ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "ietls", "explain": null, "start_percent": 0, "width_percent": 48.418}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 82}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.065691, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "ietls", "explain": null, "start_percent": 48.418, "width_percent": 12.165}, {"sql": "select * from `user_credits` where `user_credits`.`user_id` = 1 and `user_credits`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "middleware", "name": "ensure.credit", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\app\\Http\\Middleware\\EnsureUserHasCredit.php", "line": 22}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}], "start": **********.0708349, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "ensure.credit:22", "source": {"index": 21, "namespace": "middleware", "name": "ensure.credit", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\app\\Http\\Middleware\\EnsureUserHasCredit.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fapp%2FHttp%2FMiddleware%2FEnsureUserHasCredit.php&line=22", "ajax": false, "filename": "EnsureUserHasCredit.php", "line": "22"}, "connection": "ietls", "explain": null, "start_percent": 60.584, "width_percent": 13.625}, {"sql": "select count(*) as aggregate from `scoring_attempts` where `user_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ScoringController.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\app\\Http\\Controllers\\ScoringController.php", "line": 236}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.0730681, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "ScoringController.php:236", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/ScoringController.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\app\\Http\\Controllers\\ScoringController.php", "line": 236}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fapp%2FHttp%2FControllers%2FScoringController.php&line=236", "ajax": false, "filename": "ScoringController.php", "line": "236"}, "connection": "ietls", "explain": null, "start_percent": 74.209, "width_percent": 12.165}, {"sql": "select * from `scoring_attempts` where `user_id` = 1 order by `created_at` desc limit 10 offset 0", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ScoringController.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\app\\Http\\Controllers\\ScoringController.php", "line": 236}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.074627, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "ScoringController.php:236", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/ScoringController.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\app\\Http\\Controllers\\ScoringController.php", "line": 236}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fapp%2FHttp%2FControllers%2FScoringController.php&line=236", "ajax": false, "filename": "ScoringController.php", "line": "236"}, "connection": "ietls", "explain": null, "start_percent": 86.375, "width_percent": 13.625}]}, "models": {"data": {"App\\Models\\ScoringAttempt": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fapp%2FModels%2FScoringAttempt.php&line=1", "ajax": false, "filename": "ScoringAttempt.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\UserCredit": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fapp%2FModels%2FUserCredit.php&line=1", "ajax": false, "filename": "UserCredit.php", "line": "?"}}}, "count": 7, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "500 Internal Server Error", "full_url": "http://127.0.0.1:8000/scoring/history", "action_name": "scoring.history", "controller_action": "App\\Http\\Controllers\\ScoringController@history", "uri": "GET scoring/history", "controller": "App\\Http\\Controllers\\ScoringController@history<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fapp%2FHttp%2FControllers%2FScoringController.php&line=231\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "/scoring", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fapp%2FHttp%2FControllers%2FScoringController.php&line=231\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/ScoringController.php:231-239</a>", "middleware": "web, auth", "duration": "585ms", "peak_memory": "30MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-821921653 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-821921653\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-914559655 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-914559655\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2141003131 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://127.0.0.1:8000/scoring/history</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,vi;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1636 characters\">laravel_session=eyJpdiI6ImJ0YWE2SlNlVGxuMjBoeFlMc29JOXc9PSIsInZhbHVlIjoiVDNHR0hmKzlzZGZOZUFJYmJGYnBXRHJIdnpGYXdBMXZwL1dQbk1uLzNnU2w0Z2NqdVp1RGlPbjBheDd5dXlQUzNZbkdnMHYzL0lsajF5d2FldllyL2hzUXJ3Qjdjc0xOUElDOGVTcWZKOGJZUjdZSW5sNjFlek5GSGNveU4xRFIiLCJtYWMiOiI5MTI2NDA0NzFlYmQ3NGVjNmY0NzljZTVmMDAxOTVmZGVhNDhjMTJiNWJjNWZiNzczNmE2YjU4Y2QzNDUzMDQ4IiwidGFnIjoiIn0%3D; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Imw5QVM5cFVDbmZqVFBYdXlBK1RMdmc9PSIsInZhbHVlIjoiVzhoc2pFTmlSUm9iNUtmQk82eVR4UDJRRVI5QWt6T3oxQTM2ZFgrZ2cyZ1FXOEVPSlhQYTE1NXF5eVNHaFdJNDF5ZlBvL0wvS053L081YjdxTnBaV0RGSnBPQTJ1Q0JybVR3aUV4dmlkaGFiR3RyWlFNajhXeGtpSVdCSEowd0lSNjBGQlR0UVpoaG84M2pTekJNQlBhWmhJODIxRVFGb1E1VFlUTjZYLy92aDJLTG96ODlRSWhSODJ5WmJCZHJJWTB3VWVydXAybWEyQW45OWF3MzI1dEdDU1c5TEpxaG5NVnR2dXhiRU1UVT0iLCJtYWMiOiI3OWFjMDU0MDBkNDdhNjc2ODNjYmQ5ODRiZmRiZWIwNTdmZjE4NTNhYjM5YzljYWEyY2M2MzkwZGU2ZmYyMWMxIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImsyOE9IUzVOZWlvZDRHY1Y1bXh3SFE9PSIsInZhbHVlIjoia0ZwdmhkS3VQdVdPZDQxK3FXNlBnZ3g2SUxVZ0NXUGRTdThjN2hTOWJqVzlEbktwckFWRm51cllrbFRUVGluOWswaG00MEF5TXlEN2lHbENwaVQ0SWFKN2JJWTZwMDFQWmY4Z3FvSWc3azMySTBLTzgzSjVoZmJZbURLU3JYL3ciLCJtYWMiOiJkNWNlYjgwODUwNjVmN2IzODY3NzgwOWZhZTk5YjAzODBjYzM4MTBjYzNmNmNlZjI3YjkwNDYyNzI2ODdjY2E0IiwidGFnIjoiIn0%3D; i_e_l_t_s_a_i_scoring_system_session=eyJpdiI6IkJVdCs0aHZRVVZHaFR1Z0hrQWo1Tnc9PSIsInZhbHVlIjoiSUhkR3N1WHZRSDZvb0lsTlN0SFNGbFpLcHI3Yi95dFFhV3dqeDUwVjFJQ3ZiUUtKSEt2c3dQZXV6STgyZGpKOEJFRWpCcFFrLzR2MmRBNEkyT20yWnh1Z0dhMzcvSitQMDZQTzNJMWMySE5ud3RSM243dTNyaDRWMk1Nb2VsWTAiLCJtYWMiOiI2YmE5ODQ5NDI3NjJkODkzZTFkOTZlNzA5MzA3Mzk3ZWI4MzBkY2RhZjIzOWZkM2MyNDAwNjNjYzk1MGE5ZDhiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2141003131\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1575908502 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>laravel_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|3wccpylwRhXpahJKUyCq5OB1acJKoA1uVT8wR590398beZmetuIix9Oz31TE|$2y$12$w9Xe5C9oBSghC7wjYhaL8e4NH7n0qFNzXvZwq4eFPiPRNhYyYy9zq</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PcVgzRJ2u4aLQUh3Wf0zWNjBL5JJ2KyXi8PQjBGb</span>\"\n  \"<span class=sf-dump-key>i_e_l_t_s_a_i_scoring_system_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TGbnOTYIBZUzNJj0xv9nDBkjG9tZftJvLifPfv1e</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1575908502\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1140525169 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 18 Jul 2025 17:26:59 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1140525169\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1477279212 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PcVgzRJ2u4aLQUh3Wf0zWNjBL5JJ2KyXi8PQjBGb</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://127.0.0.1:8000/scoring/history</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1752849859</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1477279212\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "500 Internal Server Error", "full_url": "http://127.0.0.1:8000/scoring/history", "action_name": "scoring.history", "controller_action": "App\\Http\\Controllers\\ScoringController@history"}, "badge": "500 Internal Server Error"}}