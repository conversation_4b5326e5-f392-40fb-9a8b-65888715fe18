{"__meta": {"id": "01K0F8D8DBH62VDD02DBK5B71S", "datetime": "2025-07-18 17:13:16", "utime": **********.45993, "method": "GET", "uri": "/dashboard", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.244191, "end": **********.459943, "duration": 0.21575212478637695, "duration_str": "216ms", "measures": [{"label": "Booting", "start": **********.244191, "relative_start": 0, "end": **********.380313, "relative_end": **********.380313, "duration": 0.****************, "duration_str": "136ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.380403, "relative_start": 0.*****************, "end": **********.459944, "relative_end": 9.5367431640625e-07, "duration": 0.*****************, "duration_str": "79.54ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.401119, "relative_start": 0.*****************, "end": **********.403274, "relative_end": **********.403274, "duration": 0.0021550655364990234, "duration_str": "2.16ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.450809, "relative_start": 0.***************, "end": **********.458481, "relative_end": **********.458481, "duration": 0.*****************, "duration_str": "7.67ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: home.dashboard", "start": **********.452451, "relative_start": 0.*****************, "end": **********.452451, "relative_end": **********.452451, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: layouts.app", "start": **********.456266, "relative_start": 0.*****************, "end": **********.456266, "relative_end": **********.456266, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 24268248, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.20.0", "PHP Version": "8.2.27", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 2, "nb_templates": 2, "templates": [{"name": "home.dashboard", "param_count": null, "params": [], "start": **********.452417, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\resources\\views/home/<USER>", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fresources%2Fviews%2Fhome%2Fdashboard.blade.php&line=1", "ajax": false, "filename": "dashboard.blade.php", "line": "?"}}, {"name": "layouts.app", "param_count": null, "params": [], "start": **********.456233, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\resources\\views/layouts/app.blade.phplayouts.app", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fresources%2Fviews%2Flayouts%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}}]}, "queries": {"count": 8, "nb_statements": 7, "nb_visible_statements": 8, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00506, "accumulated_duration_str": "5.06ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 158}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 56}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}], "start": **********.413606, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "ietls", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `sessions` where `id` = 'TGbnOTYIBZUzNJj0xv9nDBkjG9tZftJvLifPfv1e' limit 1", "type": "query", "params": [], "bindings": ["TGbnOTYIBZUzNJj0xv9nDBkjG9tZftJvLifPfv1e"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.418045, "duration": 0.00204, "duration_str": "2.04ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "ietls", "explain": null, "start_percent": 0, "width_percent": 40.316}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 82}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.430192, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "ietls", "explain": null, "start_percent": 40.316, "width_percent": 11.858}, {"sql": "select * from `user_credits` where `user_credits`.`user_id` = 1 and `user_credits`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "middleware", "name": "ensure.credit", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\app\\Http\\Middleware\\EnsureUserHasCredit.php", "line": 22}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}], "start": **********.435812, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "ensure.credit:22", "source": {"index": 21, "namespace": "middleware", "name": "ensure.credit", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\app\\Http\\Middleware\\EnsureUserHasCredit.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fapp%2FHttp%2FMiddleware%2FEnsureUserHasCredit.php&line=22", "ajax": false, "filename": "EnsureUserHasCredit.php", "line": "22"}, "connection": "ietls", "explain": null, "start_percent": 52.174, "width_percent": 10.87}, {"sql": "select count(*) as aggregate from `scoring_attempts` where `user_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\app\\Http\\Controllers\\HomeController.php", "line": 49}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.437944, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:49", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\app\\Http\\Controllers\\HomeController.php", "line": 49}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fapp%2FHttp%2FControllers%2FHomeController.php&line=49", "ajax": false, "filename": "HomeController.php", "line": "49"}, "connection": "ietls", "explain": null, "start_percent": 63.043, "width_percent": 7.708}, {"sql": "select count(*) as aggregate from `scoring_attempts` where `user_id` = 1 and `status` = 'completed'", "type": "query", "params": [], "bindings": [1, "completed"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\app\\Http\\Controllers\\HomeController.php", "line": 52}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.439826, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:52", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\app\\Http\\Controllers\\HomeController.php", "line": 52}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fapp%2FHttp%2FControllers%2FHomeController.php&line=52", "ajax": false, "filename": "HomeController.php", "line": "52"}, "connection": "ietls", "explain": null, "start_percent": 70.751, "width_percent": 11.265}, {"sql": "select avg(`overall_band_score`) as aggregate from `scoring_attempts` where `user_id` = 1 and `status` = 'completed'", "type": "query", "params": [], "bindings": [1, "completed"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\app\\Http\\Controllers\\HomeController.php", "line": 56}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.441506, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:56", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\app\\Http\\Controllers\\HomeController.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fapp%2FHttp%2FControllers%2FHomeController.php&line=56", "ajax": false, "filename": "HomeController.php", "line": "56"}, "connection": "ietls", "explain": null, "start_percent": 82.016, "width_percent": 7.708}, {"sql": "select * from `scoring_attempts` where `user_id` = 1 order by `created_at` desc limit 10", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\app\\Http\\Controllers\\HomeController.php", "line": 62}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.442956, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:62", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\app\\Http\\Controllers\\HomeController.php", "line": 62}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fapp%2FHttp%2FControllers%2FHomeController.php&line=62", "ajax": false, "filename": "HomeController.php", "line": "62"}, "connection": "ietls", "explain": null, "start_percent": 89.723, "width_percent": 10.277}]}, "models": {"data": {"App\\Models\\ScoringAttempt": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fapp%2FModels%2FScoringAttempt.php&line=1", "ajax": false, "filename": "ScoringAttempt.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\UserCredit": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fapp%2FModels%2FUserCredit.php&line=1", "ajax": false, "filename": "UserCredit.php", "line": "?"}}}, "count": 7, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/dashboard", "action_name": "dashboard", "controller_action": "App\\Http\\Controllers\\HomeController@index", "uri": "GET dashboard", "controller": "App\\Http\\Controllers\\HomeController@index<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fapp%2FHttp%2FControllers%2FHomeController.php&line=46\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fapp%2FHttp%2FControllers%2FHomeController.php&line=46\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/HomeController.php:46-73</a>", "middleware": "web, auth", "duration": "218ms", "peak_memory": "24MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1821037404 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1821037404\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-928079433 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-928079433\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1645228047 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">http://127.0.0.1:8000/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,vi;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1636 characters\">laravel_session=eyJpdiI6ImJ0YWE2SlNlVGxuMjBoeFlMc29JOXc9PSIsInZhbHVlIjoiVDNHR0hmKzlzZGZOZUFJYmJGYnBXRHJIdnpGYXdBMXZwL1dQbk1uLzNnU2w0Z2NqdVp1RGlPbjBheDd5dXlQUzNZbkdnMHYzL0lsajF5d2FldllyL2hzUXJ3Qjdjc0xOUElDOGVTcWZKOGJZUjdZSW5sNjFlek5GSGNveU4xRFIiLCJtYWMiOiI5MTI2NDA0NzFlYmQ3NGVjNmY0NzljZTVmMDAxOTVmZGVhNDhjMTJiNWJjNWZiNzczNmE2YjU4Y2QzNDUzMDQ4IiwidGFnIjoiIn0%3D; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Imw5QVM5cFVDbmZqVFBYdXlBK1RMdmc9PSIsInZhbHVlIjoiVzhoc2pFTmlSUm9iNUtmQk82eVR4UDJRRVI5QWt6T3oxQTM2ZFgrZ2cyZ1FXOEVPSlhQYTE1NXF5eVNHaFdJNDF5ZlBvL0wvS053L081YjdxTnBaV0RGSnBPQTJ1Q0JybVR3aUV4dmlkaGFiR3RyWlFNajhXeGtpSVdCSEowd0lSNjBGQlR0UVpoaG84M2pTekJNQlBhWmhJODIxRVFGb1E1VFlUTjZYLy92aDJLTG96ODlRSWhSODJ5WmJCZHJJWTB3VWVydXAybWEyQW45OWF3MzI1dEdDU1c5TEpxaG5NVnR2dXhiRU1UVT0iLCJtYWMiOiI3OWFjMDU0MDBkNDdhNjc2ODNjYmQ5ODRiZmRiZWIwNTdmZjE4NTNhYjM5YzljYWEyY2M2MzkwZGU2ZmYyMWMxIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjZyZU54MDh0cUduS1htWVowWnV2N0E9PSIsInZhbHVlIjoiQmplSjAwZjdhUlVsSHRxUWxUYU5FMG5jM05GT0JwYWlzV21LazNVcUd0aWNmNUVyQklPUTBKcyt3TWpycUJ6cWhjTjVBN0prWmM5TmZSZ2ViOC9zaVpxZkZ6OUZsaFNJZm5UT2hlQTJneEtHcUd5aUJMUzg5TVZEMzJoWnE4RDEiLCJtYWMiOiIxZDI3Mjc3YjA2MThiNTgyMzgyMDJjMzUyOTgwMDc1NWFiNmQ2ZjM4MjhmNzBjZTIyY2IzNDVjMmMzMDA2MmU5IiwidGFnIjoiIn0%3D; i_e_l_t_s_a_i_scoring_system_session=eyJpdiI6IklFckUzVExNUm1oOVArcXFpVnhNWWc9PSIsInZhbHVlIjoiU2V5eVNqakJMZlYreFl5T0JOWTFhb0ZUZm42a3cyc25XeXNwU2RCNW1QZUE0bm14TTlSSlozNmZJblNieHhWaWhWV0tmK3crbFgybmRVUnh6M1UySnNZOWhUZGhPN0d1eDM3VytqYWphR1pmblEvWFB3eXM0WlNzWUo5NlZqS3oiLCJtYWMiOiIzZmFkZTM3YzNlMDVmNTAxNDZhMDk3Y2NiMTJjODYzZWM0MDMyMGY4NmQyOWU3MGExN2E5ZTM2YmZjMDY5ZmIzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1645228047\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>laravel_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|3wccpylwRhXpahJKUyCq5OB1acJKoA1uVT8wR590398beZmetuIix9Oz31TE|$2y$12$w9Xe5C9oBSghC7wjYhaL8e4NH7n0qFNzXvZwq4eFPiPRNhYyYy9zq</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PcVgzRJ2u4aLQUh3Wf0zWNjBL5JJ2KyXi8PQjBGb</span>\"\n  \"<span class=sf-dump-key>i_e_l_t_s_a_i_scoring_system_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TGbnOTYIBZUzNJj0xv9nDBkjG9tZftJvLifPfv1e</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1544273317 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 18 Jul 2025 17:13:16 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1544273317\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2031381924 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PcVgzRJ2u4aLQUh3Wf0zWNjBL5JJ2KyXi8PQjBGb</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"31 characters\">http://127.0.0.1:8000/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1752849859</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2031381924\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/dashboard", "action_name": "dashboard", "controller_action": "App\\Http\\Controllers\\HomeController@index"}, "badge": null}}