{"__meta": {"id": "01K0F87KXB1GP9CYTZSQDSGXDV", "datetime": "2025-07-18 17:10:11", "utime": **********.628249, "method": "GET", "uri": "/share/ZXlKcGRpSTZJbmM0UzIxQk5rdDZkVmxKV20wclVGUjZNRkY0Wm5jOVBTSXNJblpoYkhWbElqb2lOSEI1ZWtkV1owbFFUVkpGTld4NFRGSjRVRW80Y1VWMWN6UldaMFJzUjBzMGF6TTViVmt3V2pCNWVEbDRWWGRKTTA5ck9GRnVlbFpzZVdwcU4yRmhRbWR5WmxGWmRFMTJNMDFDYUhKa2RsTnpLMVF3YkdGYVNEZzFSMUZQTDFFdmNsVkplaTk1ZDBzMFVGUlVkbTlOWmtRME9EUXJlRlJ1SzFoaVlsSTBUbVVpTENKdFlXTWlPaUpsWlRCbE5qRXdZMk00WkRKbE0yRmtNamMyTW1JeE5XSTROekJsTW1VNE16ZGpNVEUxTVRrM09UZzBaRFE1WVRkbU1tVm1ZV1EzTjJRM05URmhNV1EwSWl3aWRHRm5Jam9pSW4wPQ==", "ip": "127.0.0.1"}, "messages": {"count": 2, "messages": [{"message": "[17:10:11] LOG.info: IELTSScorer initialized {\n    \"api_url\": \"https:\\/\\/api.v3.cm\\/v1\\/chat\\/completions\",\n    \"model\": \"o3\",\n    \"api_key_length\": 51\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.348212, "xdebug_link": null, "collector": "log"}, {"message": "[17:10:11] LOG.error: View [scoring.public] not found. {\n    \"userId\": 3,\n    \"exception\": {}\n}", "message_html": null, "is_string": false, "label": "error", "time": **********.428642, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.214304, "end": **********.628265, "duration": 0.41396093368530273, "duration_str": "414ms", "measures": [{"label": "Booting", "start": **********.214304, "relative_start": 0, "end": **********.32798, "relative_end": **********.32798, "duration": 0.*****************, "duration_str": "114ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.327989, "relative_start": 0.*****************, "end": **********.628267, "relative_end": 2.1457672119140625e-06, "duration": 0.****************, "duration_str": "300ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.343108, "relative_start": 0.*****************, "end": **********.345177, "relative_end": **********.345177, "duration": 0.0020689964294433594, "duration_str": "2.07ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: laravel-exceptions-renderer::show", "start": **********.539318, "relative_start": 0.****************, "end": **********.539318, "relative_end": **********.539318, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.navigation", "start": **********.540892, "relative_start": 0.****************, "end": **********.540892, "relative_end": **********.540892, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.theme-switcher", "start": **********.541259, "relative_start": 0.32695508003234863, "end": **********.541259, "relative_end": **********.541259, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.icons.sun", "start": **********.541699, "relative_start": 0.327394962310791, "end": **********.541699, "relative_end": **********.541699, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.icons.moon", "start": **********.542015, "relative_start": 0.3277111053466797, "end": **********.542015, "relative_end": **********.542015, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.icons.sun", "start": **********.542276, "relative_start": 0.3279719352722168, "end": **********.542276, "relative_end": **********.542276, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.icons.moon", "start": **********.542477, "relative_start": 0.3281729221343994, "end": **********.542477, "relative_end": **********.542477, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.icons.computer-desktop", "start": **********.542727, "relative_start": 0.32842302322387695, "end": **********.542727, "relative_end": **********.542727, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.header", "start": **********.543034, "relative_start": 0.32873010635375977, "end": **********.543034, "relative_end": **********.543034, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.card", "start": **********.543419, "relative_start": 0.3291149139404297, "end": **********.543419, "relative_end": **********.543419, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.trace-and-editor", "start": **********.543762, "relative_start": 0.32945799827575684, "end": **********.543762, "relative_end": **********.543762, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.trace", "start": **********.55456, "relative_start": 0.3402559757232666, "end": **********.55456, "relative_end": **********.55456, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.icons.chevron-down", "start": **********.555121, "relative_start": 0.3408169746398926, "end": **********.555121, "relative_end": **********.555121, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.icons.chevron-up", "start": **********.555426, "relative_start": 0.3411219120025635, "end": **********.555426, "relative_end": **********.555426, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.icons.chevron-up", "start": **********.555678, "relative_start": 0.34137392044067383, "end": **********.555678, "relative_end": **********.555678, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.icons.chevron-down", "start": **********.55584, "relative_start": 0.3415360450744629, "end": **********.55584, "relative_end": **********.55584, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.editor", "start": **********.573762, "relative_start": 0.35945796966552734, "end": **********.573762, "relative_end": **********.573762, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.card", "start": **********.613186, "relative_start": 0.3988819122314453, "end": **********.613186, "relative_end": **********.613186, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.context", "start": **********.613641, "relative_start": 0.3993370532989502, "end": **********.613641, "relative_end": **********.613641, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.card", "start": **********.61436, "relative_start": 0.****************, "end": **********.61436, "relative_end": **********.61436, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.card", "start": **********.614923, "relative_start": 0.****************, "end": **********.614923, "relative_end": **********.614923, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.layout", "start": **********.615897, "relative_start": 0.****************, "end": **********.615897, "relative_end": **********.615897, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 28009648, "peak_usage_str": "27MB"}, "exceptions": {"count": 1, "exceptions": [{"type": "InvalidArgumentException", "message": "View [scoring.public] not found.", "code": 0, "file": "vendor/laravel/framework/src/Illuminate/View/FileViewFinder.php", "line": 138, "stack_trace": null, "stack_trace_html": "<pre class=sf-dump id=sf-dump-488470655 data-indent-pad=\"  \"><span class=sf-dump-note>array:62</span> [<samp data-depth=1 class=sf-dump-expanded>\n  <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"63 characters\">vendor/laravel/framework/src/Illuminate/View/FileViewFinder.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>78</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"11 characters\">findInPaths</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Illuminate\\View\\FileViewFinder</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">scoring.public</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"57 characters\">C:\\laragon\\www\\ielts\\ielts-scoring-system\\resources\\views</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"56 characters\">vendor/laravel/framework/src/Illuminate/View/Factory.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>150</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">find</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Illuminate\\View\\FileViewFinder</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">scoring.public</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"62 characters\">vendor/laravel/framework/src/Illuminate/Foundation/helpers.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>1101</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">make</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"23 characters\">Illuminate\\View\\Factory</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">scoring.public</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>attempt</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\ScoringAttempt\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ScoringAttempt</span></span> {<a class=sf-dump-ref href=#sf-dump-488470655-ref21405 title=\"2 occurrences\">#1405</a><samp data-depth=5 id=sf-dump-488470655-ref21405 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"16 characters\">scoring_attempts</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n          +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n          #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n          +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n          +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n          +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:21</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>10</span>\n            \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-num>3</span>\n            \"<span class=sf-dump-key>essay_question_id</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>essay_content</span>\" => \"\"\"\n              <span class=sf-dump-str title=\"1184 characters\">International news is good for student but sometime it is not nessessary. Many people think student in secondary school need to study about international news, other think this waste their time. My essay will write about both of these thing.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n              <span class=sf-dump-str title=\"1184 characters\">Firstly, International news is good because it make student smart. When student learn international news they can know many things. Example, student can know weather in other country, famous people life, or footbal match. They will very interesting about these topic and can relax in the classroom. Learning international news is also help student speaking english well.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n              <span class=sf-dump-str title=\"1184 characters\">However, I thinking international news sometime is boring. Student have many important subject, example maths, literature, or chemistry. These subjects can help student become doctor or engineer in future. International news not really important, only waste of time.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n              <span class=sf-dump-str title=\"1184 characters\">In my opinion, secondary student should play sport and learn math better than learn international news. Teacher should teaching student good subject not international news.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n              <span class=sf-dump-str title=\"1184 characters\">I conclusion, international news have advantage but more disadvantage. Student no need study this subject, waste of their time.</span>\n              \"\"\"\n            \"<span class=sf-dump-key>task_type</span>\" => \"<span class=sf-dump-str title=\"5 characters\">task2</span>\"\n            \"<span class=sf-dump-key>time_limit</span>\" => <span class=sf-dump-num>40</span>\n            \"<span class=sf-dump-key>overall_band_score</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.0</span>\"\n            \"<span class=sf-dump-key>task_achievement</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.0</span>\"\n            \"<span class=sf-dump-key>coherence_cohesion</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.5</span>\"\n            \"<span class=sf-dump-key>lexical_resource</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.0</span>\"\n            \"<span class=sf-dump-key>grammar_accuracy</span>\" => \"<span class=sf-dump-str title=\"3 characters\">3.5</span>\"\n            \"<span class=sf-dump-key>detailed_feedback</span>\" => \"<span class=sf-dump-str title=\"2203 characters\">{&quot;grammar_accuracy&quot;: {&quot;score&quot;: 3.5, &quot;issues&quot;: [&quot;Sai ch&#7911; ng&#7919;-&#273;&#7897;ng t&#7915;: &#8216;news is&#8230; it make student&#8230;&#8217;;&quot;, &quot;Thi&#7871;u m&#7841;o t&#7915;: &#8216;in other country&#8217;; sai th&#236;: &#8216;I thinking&#8217;;&quot;, &quot;C&#226;u thi&#7871;u th&#224;nh ph&#7847;n: &#8216;International news not really important&#8217;.&quot;, &quot;Vi&#7871;t hoa v&#224; ch&#7845;m c&#226;u ch&#432;a chu&#7849;n.&quot;], &quot;feedback&quot;: &quot;L&#7895;i ng&#7919; ph&#225;p c&#417; b&#7843;n xu&#7845;t hi&#7879;n th&#432;&#7901;ng xuy&#234;n; ch&#7911;-v&#7883;, th&#236; v&#224; m&#7841;o t&#7915; ch&#432;a chu&#7849;n.&quot;, &quot;improvements&quot;: [&quot;&#212;n l&#7841;i S-V agreement, m&#7841;o t&#7915;, danh t&#7915; s&#7889; nhi&#7873;u.&quot;, &quot;D&#249;ng c&#7845;u tr&#250;c ph&#7913;c t&#7841;p h&#417;n: Although&#8230;, in spite of&#8230;, would lead to&#8230;&quot;, &quot;T&#7921; &#273;&#7885;c l&#7841;i v&#224; s&#7917;a l&#7895;i ng&#7919; ph&#225;p tr&#432;&#7899;c khi n&#7897;p b&#224;i.&quot;]}, &quot;lexical_resource&quot;: {&quot;score&quot;: 4, &quot;issues&quot;: [&quot;L&#7863;p &#273;i l&#7863;p l&#7841;i &#8216;student&#8217;, &#8216;international news&#8217;.&quot;, &quot;Sai ch&#237;nh t&#7843;: nessessary, footbal, interesting, advantage v.v.&quot;, &quot;D&#249;ng t&#7915; ch&#432;a chu&#7849;n: &#8216;smart&#8217; (n&#234;n thay b&#7857;ng &#8216;knowledgeable&#8217;), &#8216;good subject&#8217;.&quot;], &quot;feedback&quot;: &quot;T&#7915; v&#7921;ng &#7903; m&#7913;c c&#417; b&#7843;n, l&#7863;p nhi&#7873;u, c&#243; sai ch&#237;nh t&#7843; v&#224; d&#249;ng t&#7915; kh&#244;ng ch&#237;nh x&#225;c.&quot;, &quot;improvements&quot;: [&quot;H&#7885;c th&#234;m collocations: keep abreast of international affairs, broaden horizons&#8230;&quot;, &quot;Ki&#7875;m tra ch&#237;nh t&#7843; sau khi vi&#7871;t.&quot;, &quot;D&#249;ng t&#7915; &#273;a d&#7841;ng h&#417;n, tr&#225;nh l&#7863;p; s&#7917; d&#7909;ng t&#7915; &#273;&#7891;ng ngh&#297;a, paraphrase.&quot;]}, &quot;task_achievement&quot;: {&quot;score&quot;: 4, &quot;issues&quot;: [&quot;Thi&#7871;u t&#7915; (179/250 words) &#8594; b&#7883; tr&#7915; &#273;i&#7875;m.&quot;, &quot;L&#7853;p lu&#7853;n &#273;&#417;n gi&#7843;n, ch&#432;a c&#243; v&#237; d&#7909; c&#7909; th&#7875;, s&#7889; li&#7879;u ho&#7863;c ph&#226;n t&#237;ch s&#226;u.&quot;, &quot;Ch&#432;a tr&#7843; l&#7901;i &#273;&#7847;y &#273;&#7911; c&#7843; hai quan &#273;i&#7875;m m&#7897;t c&#225;ch c&#226;n b&#7857;ng.&quot;], &quot;feedback&quot;: &quot;B&#224;i vi&#7871;t ch&#432;a &#273;&#225;p &#7913;ng &#273;&#7911; y&#234;u c&#7847;u t&#7889;i thi&#7875;u 250 t&#7915;, &#253; c&#242;n s&#417; s&#224;i v&#224; minh ho&#7841; ch&#432;a thuy&#7871;t ph&#7909;c.&quot;, &quot;improvements&quot;: [&quot;Vi&#7871;t t&#7889;i thi&#7875;u 260 t&#7915; &#273;&#7875; tr&#225;nh tr&#7915; &#273;i&#7875;m.&quot;, &quot;Tri&#7875;n khai m&#7895;i l&#7853;p lu&#7853;n v&#7899;i v&#237; d&#7909; th&#7921;c t&#7871;, s&#7889; li&#7879;u, ho&#7863;c d&#7851;n ch&#7913;ng c&#225; nh&#226;n.&quot;, &quot;N&#234;u r&#245; l&#7853;p tr&#432;&#7901;ng ngay ph&#7847;n m&#7903; b&#224;i v&#224; nh&#7855;c l&#7841;i trong k&#7871;t lu&#7853;n.&quot;]}, &quot;coherence_cohesion&quot;: {&quot;score&quot;: 4.5, &quot;issues&quot;: [&quot;T&#7915; n&#7889;i ngh&#232;o n&#224;n, h&#7847;u nh&#432; ch&#7881; d&#249;ng &#8216;Firstly&#8217;, &#8216;However&#8217;.&quot;, &quot;C&#225;c &#273;o&#7841;n qu&#225; ng&#7855;n, &#253; chuy&#7875;n &#273;&#7897;t ng&#7897;t, ch&#432;a c&#243; c&#226;u ch&#7911; &#273;&#7873; r&#245; r&#224;ng.&quot;, &quot;Thi&#7871;u m&#7841;ch logic gi&#7919;a c&#225;c c&#226;u.&quot;], &quot;feedback&quot;: &quot;B&#7889; c&#7909;c c&#243; m&#7903; th&#226;n k&#7871;t nh&#432;ng li&#234;n k&#7871;t &#253; c&#242;n r&#7901;i r&#7841;c, t&#7915; n&#7889;i h&#7841;n ch&#7871; v&#224; l&#7863;p l&#7841;i.&quot;, &quot;improvements&quot;: [&quot;S&#7917; d&#7909;ng &#273;a d&#7841;ng linking words: In addition, Moreover, On the other hand, Consequently&#8230;&quot;, &quot;M&#7895;i &#273;o&#7841;n th&#226;n b&#224;i: 1 c&#226;u ch&#7911; &#273;&#7873; &#8211; 2-3 c&#226;u gi&#7843;i th&#237;ch &#8211; 1 v&#237; d&#7909; &#8211; 1 c&#226;u k&#7871;t.&quot;, &quot;Tr&#225;nh l&#7863;p t&#7915; &#8216;student&#8217;, &#8216;international news&#8217; qu&#225; nhi&#7873;u; d&#249;ng &#273;&#7841;i t&#7915; thay th&#7871;.&quot;]}}</span>\"\n            \"<span class=sf-dump-key>highlighted_corrections</span>\" => \"<span class=sf-dump-str title=\"2895 characters\">[{&quot;severity&quot;: &quot;high&quot;, &quot;error_type&quot;: &quot;spelling&quot;, &quot;explanation&quot;: &quot;Sai ch&#237;nh t&#7843; &#8216;sometime&#8217; (thi&#7871;u &#8216;s&#8217;) v&#224; &#8216;nessessary&#8217;.&quot;, &quot;original_text&quot;: &quot;sometime it is not nessessary&quot;, &quot;suggested_correction&quot;: &quot;sometimes it is not necessary&quot;}, {&quot;severity&quot;: &quot;medium&quot;, &quot;error_type&quot;: &quot;grammar&quot;, &quot;explanation&quot;: &quot;Thi&#7871;u &#8216;s&#8217; &#7903; danh t&#7915; s&#7889; nhi&#7873;u v&#224; m&#7841;o t&#7915; &#8216;schools&#8217;.&quot;, &quot;original_text&quot;: &quot;student in secondary school&quot;, &quot;suggested_correction&quot;: &quot;students in secondary schools&quot;}, {&quot;severity&quot;: &quot;medium&quot;, &quot;error_type&quot;: &quot;grammar&quot;, &quot;explanation&quot;: &quot;&#272;&#7897;ng t&#7915; ph&#7843;i chia s&#7889; &#237;t &#8216;wastes&#8217; khi ch&#7911; ng&#7919; l&#224; &#8216;this&#8217;.&quot;, &quot;original_text&quot;: &quot;this waste their time&quot;, &quot;suggested_correction&quot;: &quot;this wastes their time&quot;}, {&quot;severity&quot;: &quot;high&quot;, &quot;error_type&quot;: &quot;grammar|vocabulary&quot;, &quot;explanation&quot;: &quot;Chia &#273;&#7897;ng t&#7915; &#8216;makes&#8217;; d&#249;ng t&#7915; ph&#249; h&#7907;p &#8216;beneficial&#8217;, &#8216;knowledgeable&#8217;.&quot;, &quot;original_text&quot;: &quot;International news is good because it make student smart&quot;, &quot;suggested_correction&quot;: &quot;International news is beneficial because it makes students more knowledgeable&quot;}, {&quot;severity&quot;: &quot;medium&quot;, &quot;error_type&quot;: &quot;grammar|vocabulary&quot;, &quot;explanation&quot;: &quot;Thi&#7871;u c&#7909;m &#8216;For example&#8217;; &#8216;know weather&#8217; &#8594; &#8216;learn about the weather&#8217;; &#8216;country&#8217; &#8594; &#8216;countries&#8217;.&quot;, &quot;original_text&quot;: &quot;Example, student can know weather in other country&quot;, &quot;suggested_correction&quot;: &quot;For example, students can learn about the weather in other countries&quot;}, {&quot;severity&quot;: &quot;high&quot;, &quot;error_type&quot;: &quot;grammar|vocabulary&quot;, &quot;explanation&quot;: &quot;Sai c&#7845;u tr&#250;c &#8216;is also help&#8217;; c&#7847;n &#8216;helps&#8217;; vi&#7871;t hoa &#8216;English&#8217;.&quot;, &quot;original_text&quot;: &quot;Learning international news is also help student speaking english well.&quot;, &quot;suggested_correction&quot;: &quot;Learning international news also helps students improve their English speaking skills.&quot;}, {&quot;severity&quot;: &quot;medium&quot;, &quot;error_type&quot;: &quot;grammar&quot;, &quot;explanation&quot;: &quot;Sai th&#236; ti&#7871;p di&#7877;n, v&#7883; tr&#237; &#8216;sometimes&#8217;.&quot;, &quot;original_text&quot;: &quot;I thinking international news sometime is boring.&quot;, &quot;suggested_correction&quot;: &quot;I think international news is sometimes boring.&quot;}, {&quot;severity&quot;: &quot;medium&quot;, &quot;error_type&quot;: &quot;grammar|punctuation&quot;, &quot;explanation&quot;: &quot;Danh t&#7915; s&#7889; nhi&#7873;u; thi&#7871;u d&#7845;u ph&#7849;y; m&#7841;o t&#7915;.&quot;, &quot;original_text&quot;: &quot;Student have many important subject, example maths, literature, or chemistry.&quot;, &quot;suggested_correction&quot;: &quot;Students have many important subjects, for example, maths, literature or chemistry.&quot;}, {&quot;severity&quot;: &quot;high&quot;, &quot;error_type&quot;: &quot;grammar&quot;, &quot;explanation&quot;: &quot;Thi&#7871;u &#273;&#7897;ng t&#7915; &#8216;is&#8217;; c&#7847;n m&#7841;o t&#7915; &#8216;a&#8217;.&quot;, &quot;original_text&quot;: &quot;International news not really important, only waste of time.&quot;, &quot;suggested_correction&quot;: &quot;International news is not really important; it is only a waste of time.&quot;}, {&quot;severity&quot;: &quot;high&quot;, &quot;error_type&quot;: &quot;grammar|spelling&quot;, &quot;explanation&quot;: &quot;C&#7847;n &#8216;In conclusion&#8217;; &#8216;news&#8217; coi nh&#432; s&#7889; &#237;t; th&#234;m &#8216;some&#8217;; danh t&#7915; s&#7889; nhi&#7873;u &#8216;disadvantages&#8217;.&quot;, &quot;original_text&quot;: &quot;I conclusion, international news have advantage but more disadvantage.&quot;, &quot;suggested_correction&quot;: &quot;In conclusion, international news has some advantages but more disadvantages.&quot;}]</span>\"\n            \"<span class=sf-dump-key>criteria_analysis</span>\" => \"<span class=sf-dump-str title=\"1493 characters\">[{&quot;criterion&quot;: &quot;Task Achievement&quot;, &quot;strengths&quot;: [&quot;&#272;&#227; n&#234;u &#273;&#432;&#7907;c hai quan &#273;i&#7875;m&quot;, &quot;C&#243; l&#7853;p tr&#432;&#7901;ng c&#225; nh&#226;n trong &#273;o&#7841;n k&#7871;t&quot;], &quot;weaknesses&quot;: [&quot;D&#432;&#7899;i 250 t&#7915; n&#234;n b&#7883; tr&#7915; &#273;i&#7875;m&quot;, &quot;L&#7853;p lu&#7853;n h&#7901;i h&#7907;t, v&#237; d&#7909; chung chung&quot;, &quot;Kh&#244;ng gi&#7843;i th&#237;ch v&#236; sao tin qu&#7889;c t&#7871; gi&#250;p n&#243;i ti&#7871;ng Anh t&#7889;t h&#417;n&quot;, &quot;Kh&#244;ng ph&#7843;n bi&#7879;n l&#7841;i quan &#273;i&#7875;m &#273;&#7889;i l&#7853;p&quot;], &quot;band_justification&quot;: &quot;&#272;&#225;p &#7913;ng ph&#7847;n n&#224;o y&#234;u c&#7847;u &#273;&#7873; nh&#432;ng ph&#225;t tri&#7875;n ch&#432;a &#273;&#7911;, thi&#7871;u chi&#7873;u s&#226;u v&#224; thi&#7871;u t&#7915; &#8594; band 4.0.&quot;}, {&quot;criterion&quot;: &quot;Coherence &amp; Cohesion&quot;, &quot;strengths&quot;: [&quot;C&#243; b&#7889; c&#7909;c c&#417; b&#7843;n: m&#7903;&#8211;th&#226;n&#8211;k&#7871;t&quot;, &quot;M&#7897;t s&#7889; t&#7915; n&#7889;i &#273;&#432;&#7907;c d&#249;ng (Firstly, However)&quot;], &quot;weaknesses&quot;: [&quot;Li&#234;n k&#7871;t &#253; k&#233;m, chuy&#7875;n &#273;o&#7841;n &#273;&#7897;t ng&#7897;t&quot;, &quot;C&#226;u ch&#7911; &#273;&#7873; m&#7901; nh&#7841;t&quot;, &quot;D&#249;ng l&#7863;p t&#7915; n&#7889;i, ch&#432;a &#273;a d&#7841;ng&quot;], &quot;band_justification&quot;: &quot;Li&#234;n k&#7871;t &#7903; m&#7913;c h&#7841;n ch&#7871;, &#253; &#273;&#244;i khi r&#7901;i r&#7841;c nh&#432;ng v&#7851;n theo tr&#236;nh t&#7921; chung &#8594; 4.5.&quot;}, {&quot;criterion&quot;: &quot;Lexical Resource&quot;, &quot;strengths&quot;: [&quot;T&#7915; v&#7921;ng quen thu&#7897;c, d&#7877; hi&#7875;u&quot;, &quot;M&#7897;t v&#224;i t&#7915; h&#7885;c thu&#7853;t &#273;&#417;n gi&#7843;n (advantage, disadvantage)&quot;], &quot;weaknesses&quot;: [&quot;L&#7863;p t&#7915; th&#432;&#7901;ng xuy&#234;n&quot;, &quot;Sai ch&#237;nh t&#7843; nhi&#7873;u&quot;, &quot;D&#249;ng t&#7915; ch&#432;a ch&#237;nh x&#225;c ho&#7863;c kh&#244;ng ph&#249; h&#7907;p v&#259;n phong h&#7885;c thu&#7853;t&quot;], &quot;band_justification&quot;: &quot;V&#7889;n t&#7915; h&#7841;n ch&#7871;, l&#7895;i ch&#237;nh t&#7843; &amp; ch&#7885;n t&#7915; &#7843;nh h&#432;&#7903;ng ng&#432;&#7901;i &#273;&#7885;c &#8594; 4.0.&quot;}, {&quot;criterion&quot;: &quot;Grammatical Range &amp; Accuracy&quot;, &quot;strengths&quot;: [&quot;Bi&#7871;t k&#7871;t th&#250;c c&#226;u b&#7857;ng d&#7845;u ch&#7845;m&quot;, &quot;M&#7897;t s&#7889; m&#7879;nh &#273;&#7873; &#273;&#417;n ch&#237;nh x&#225;c&quot;], &quot;weaknesses&quot;: [&quot;L&#7895;i S-V agreement li&#234;n t&#7909;c&quot;, &quot;Thi&#7871;u m&#7841;o t&#7915;, gi&#7899;i t&#7915;&quot;, &quot;C&#7845;u tr&#250;c c&#226;u &#273;&#417;n gi&#7843;n, hi&#7871;m c&#226;u ph&#7913;c&quot;, &quot;Sai th&#236; v&#224; thi&#7871;u &#273;&#7897;ng t&#7915; to be&quot;], &quot;band_justification&quot;: &quot;L&#7895;i th&#432;&#7901;ng xuy&#234;n khi&#7871;n kh&#243; hi&#7875;u, c&#7845;u tr&#250;c &#273;&#417;n gi&#7843;n &#8594; 3.5.&quot;}]</span>\"\n            \"<span class=sf-dump-key>statistics</span>\" => \"<span class=sf-dump-str title=\"145 characters\">{&quot;total_errors&quot;: 45, &quot;grammar_errors&quot;: 28, &quot;spelling_errors&quot;: 10, &quot;complexity_level&quot;: &quot;basic&quot;, &quot;sentence_variety&quot;: &quot;low&quot;, &quot;vocabulary_errors&quot;: 7}</span>\"\n            \"<span class=sf-dump-key>word_count</span>\" => <span class=sf-dump-num>179</span>\n            \"<span class=sf-dump-key>essay_length</span>\" => <span class=sf-dump-num>1184</span>\n            \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"9 characters\">completed</span>\"\n            \"<span class=sf-dump-key>error_message</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-18 17:02:55</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-18 17:03:22</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:21</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>10</span>\n            \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-num>3</span>\n            \"<span class=sf-dump-key>essay_question_id</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>essay_content</span>\" => \"\"\"\n              <span class=sf-dump-str title=\"1184 characters\">International news is good for student but sometime it is not nessessary. Many people think student in secondary school need to study about international news, other think this waste their time. My essay will write about both of these thing.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n              <span class=sf-dump-str title=\"1184 characters\">Firstly, International news is good because it make student smart. When student learn international news they can know many things. Example, student can know weather in other country, famous people life, or footbal match. They will very interesting about these topic and can relax in the classroom. Learning international news is also help student speaking english well.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n              <span class=sf-dump-str title=\"1184 characters\">However, I thinking international news sometime is boring. Student have many important subject, example maths, literature, or chemistry. These subjects can help student become doctor or engineer in future. International news not really important, only waste of time.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n              <span class=sf-dump-str title=\"1184 characters\">In my opinion, secondary student should play sport and learn math better than learn international news. Teacher should teaching student good subject not international news.<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n              <span class=sf-dump-str title=\"1184 characters\">I conclusion, international news have advantage but more disadvantage. Student no need study this subject, waste of their time.</span>\n              \"\"\"\n            \"<span class=sf-dump-key>task_type</span>\" => \"<span class=sf-dump-str title=\"5 characters\">task2</span>\"\n            \"<span class=sf-dump-key>time_limit</span>\" => <span class=sf-dump-num>40</span>\n            \"<span class=sf-dump-key>overall_band_score</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.0</span>\"\n            \"<span class=sf-dump-key>task_achievement</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.0</span>\"\n            \"<span class=sf-dump-key>coherence_cohesion</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.5</span>\"\n            \"<span class=sf-dump-key>lexical_resource</span>\" => \"<span class=sf-dump-str title=\"3 characters\">4.0</span>\"\n            \"<span class=sf-dump-key>grammar_accuracy</span>\" => \"<span class=sf-dump-str title=\"3 characters\">3.5</span>\"\n            \"<span class=sf-dump-key>detailed_feedback</span>\" => \"<span class=sf-dump-str title=\"2203 characters\">{&quot;grammar_accuracy&quot;: {&quot;score&quot;: 3.5, &quot;issues&quot;: [&quot;Sai ch&#7911; ng&#7919;-&#273;&#7897;ng t&#7915;: &#8216;news is&#8230; it make student&#8230;&#8217;;&quot;, &quot;Thi&#7871;u m&#7841;o t&#7915;: &#8216;in other country&#8217;; sai th&#236;: &#8216;I thinking&#8217;;&quot;, &quot;C&#226;u thi&#7871;u th&#224;nh ph&#7847;n: &#8216;International news not really important&#8217;.&quot;, &quot;Vi&#7871;t hoa v&#224; ch&#7845;m c&#226;u ch&#432;a chu&#7849;n.&quot;], &quot;feedback&quot;: &quot;L&#7895;i ng&#7919; ph&#225;p c&#417; b&#7843;n xu&#7845;t hi&#7879;n th&#432;&#7901;ng xuy&#234;n; ch&#7911;-v&#7883;, th&#236; v&#224; m&#7841;o t&#7915; ch&#432;a chu&#7849;n.&quot;, &quot;improvements&quot;: [&quot;&#212;n l&#7841;i S-V agreement, m&#7841;o t&#7915;, danh t&#7915; s&#7889; nhi&#7873;u.&quot;, &quot;D&#249;ng c&#7845;u tr&#250;c ph&#7913;c t&#7841;p h&#417;n: Although&#8230;, in spite of&#8230;, would lead to&#8230;&quot;, &quot;T&#7921; &#273;&#7885;c l&#7841;i v&#224; s&#7917;a l&#7895;i ng&#7919; ph&#225;p tr&#432;&#7899;c khi n&#7897;p b&#224;i.&quot;]}, &quot;lexical_resource&quot;: {&quot;score&quot;: 4, &quot;issues&quot;: [&quot;L&#7863;p &#273;i l&#7863;p l&#7841;i &#8216;student&#8217;, &#8216;international news&#8217;.&quot;, &quot;Sai ch&#237;nh t&#7843;: nessessary, footbal, interesting, advantage v.v.&quot;, &quot;D&#249;ng t&#7915; ch&#432;a chu&#7849;n: &#8216;smart&#8217; (n&#234;n thay b&#7857;ng &#8216;knowledgeable&#8217;), &#8216;good subject&#8217;.&quot;], &quot;feedback&quot;: &quot;T&#7915; v&#7921;ng &#7903; m&#7913;c c&#417; b&#7843;n, l&#7863;p nhi&#7873;u, c&#243; sai ch&#237;nh t&#7843; v&#224; d&#249;ng t&#7915; kh&#244;ng ch&#237;nh x&#225;c.&quot;, &quot;improvements&quot;: [&quot;H&#7885;c th&#234;m collocations: keep abreast of international affairs, broaden horizons&#8230;&quot;, &quot;Ki&#7875;m tra ch&#237;nh t&#7843; sau khi vi&#7871;t.&quot;, &quot;D&#249;ng t&#7915; &#273;a d&#7841;ng h&#417;n, tr&#225;nh l&#7863;p; s&#7917; d&#7909;ng t&#7915; &#273;&#7891;ng ngh&#297;a, paraphrase.&quot;]}, &quot;task_achievement&quot;: {&quot;score&quot;: 4, &quot;issues&quot;: [&quot;Thi&#7871;u t&#7915; (179/250 words) &#8594; b&#7883; tr&#7915; &#273;i&#7875;m.&quot;, &quot;L&#7853;p lu&#7853;n &#273;&#417;n gi&#7843;n, ch&#432;a c&#243; v&#237; d&#7909; c&#7909; th&#7875;, s&#7889; li&#7879;u ho&#7863;c ph&#226;n t&#237;ch s&#226;u.&quot;, &quot;Ch&#432;a tr&#7843; l&#7901;i &#273;&#7847;y &#273;&#7911; c&#7843; hai quan &#273;i&#7875;m m&#7897;t c&#225;ch c&#226;n b&#7857;ng.&quot;], &quot;feedback&quot;: &quot;B&#224;i vi&#7871;t ch&#432;a &#273;&#225;p &#7913;ng &#273;&#7911; y&#234;u c&#7847;u t&#7889;i thi&#7875;u 250 t&#7915;, &#253; c&#242;n s&#417; s&#224;i v&#224; minh ho&#7841; ch&#432;a thuy&#7871;t ph&#7909;c.&quot;, &quot;improvements&quot;: [&quot;Vi&#7871;t t&#7889;i thi&#7875;u 260 t&#7915; &#273;&#7875; tr&#225;nh tr&#7915; &#273;i&#7875;m.&quot;, &quot;Tri&#7875;n khai m&#7895;i l&#7853;p lu&#7853;n v&#7899;i v&#237; d&#7909; th&#7921;c t&#7871;, s&#7889; li&#7879;u, ho&#7863;c d&#7851;n ch&#7913;ng c&#225; nh&#226;n.&quot;, &quot;N&#234;u r&#245; l&#7853;p tr&#432;&#7901;ng ngay ph&#7847;n m&#7903; b&#224;i v&#224; nh&#7855;c l&#7841;i trong k&#7871;t lu&#7853;n.&quot;]}, &quot;coherence_cohesion&quot;: {&quot;score&quot;: 4.5, &quot;issues&quot;: [&quot;T&#7915; n&#7889;i ngh&#232;o n&#224;n, h&#7847;u nh&#432; ch&#7881; d&#249;ng &#8216;Firstly&#8217;, &#8216;However&#8217;.&quot;, &quot;C&#225;c &#273;o&#7841;n qu&#225; ng&#7855;n, &#253; chuy&#7875;n &#273;&#7897;t ng&#7897;t, ch&#432;a c&#243; c&#226;u ch&#7911; &#273;&#7873; r&#245; r&#224;ng.&quot;, &quot;Thi&#7871;u m&#7841;ch logic gi&#7919;a c&#225;c c&#226;u.&quot;], &quot;feedback&quot;: &quot;B&#7889; c&#7909;c c&#243; m&#7903; th&#226;n k&#7871;t nh&#432;ng li&#234;n k&#7871;t &#253; c&#242;n r&#7901;i r&#7841;c, t&#7915; n&#7889;i h&#7841;n ch&#7871; v&#224; l&#7863;p l&#7841;i.&quot;, &quot;improvements&quot;: [&quot;S&#7917; d&#7909;ng &#273;a d&#7841;ng linking words: In addition, Moreover, On the other hand, Consequently&#8230;&quot;, &quot;M&#7895;i &#273;o&#7841;n th&#226;n b&#224;i: 1 c&#226;u ch&#7911; &#273;&#7873; &#8211; 2-3 c&#226;u gi&#7843;i th&#237;ch &#8211; 1 v&#237; d&#7909; &#8211; 1 c&#226;u k&#7871;t.&quot;, &quot;Tr&#225;nh l&#7863;p t&#7915; &#8216;student&#8217;, &#8216;international news&#8217; qu&#225; nhi&#7873;u; d&#249;ng &#273;&#7841;i t&#7915; thay th&#7871;.&quot;]}}</span>\"\n            \"<span class=sf-dump-key>highlighted_corrections</span>\" => \"<span class=sf-dump-str title=\"2895 characters\">[{&quot;severity&quot;: &quot;high&quot;, &quot;error_type&quot;: &quot;spelling&quot;, &quot;explanation&quot;: &quot;Sai ch&#237;nh t&#7843; &#8216;sometime&#8217; (thi&#7871;u &#8216;s&#8217;) v&#224; &#8216;nessessary&#8217;.&quot;, &quot;original_text&quot;: &quot;sometime it is not nessessary&quot;, &quot;suggested_correction&quot;: &quot;sometimes it is not necessary&quot;}, {&quot;severity&quot;: &quot;medium&quot;, &quot;error_type&quot;: &quot;grammar&quot;, &quot;explanation&quot;: &quot;Thi&#7871;u &#8216;s&#8217; &#7903; danh t&#7915; s&#7889; nhi&#7873;u v&#224; m&#7841;o t&#7915; &#8216;schools&#8217;.&quot;, &quot;original_text&quot;: &quot;student in secondary school&quot;, &quot;suggested_correction&quot;: &quot;students in secondary schools&quot;}, {&quot;severity&quot;: &quot;medium&quot;, &quot;error_type&quot;: &quot;grammar&quot;, &quot;explanation&quot;: &quot;&#272;&#7897;ng t&#7915; ph&#7843;i chia s&#7889; &#237;t &#8216;wastes&#8217; khi ch&#7911; ng&#7919; l&#224; &#8216;this&#8217;.&quot;, &quot;original_text&quot;: &quot;this waste their time&quot;, &quot;suggested_correction&quot;: &quot;this wastes their time&quot;}, {&quot;severity&quot;: &quot;high&quot;, &quot;error_type&quot;: &quot;grammar|vocabulary&quot;, &quot;explanation&quot;: &quot;Chia &#273;&#7897;ng t&#7915; &#8216;makes&#8217;; d&#249;ng t&#7915; ph&#249; h&#7907;p &#8216;beneficial&#8217;, &#8216;knowledgeable&#8217;.&quot;, &quot;original_text&quot;: &quot;International news is good because it make student smart&quot;, &quot;suggested_correction&quot;: &quot;International news is beneficial because it makes students more knowledgeable&quot;}, {&quot;severity&quot;: &quot;medium&quot;, &quot;error_type&quot;: &quot;grammar|vocabulary&quot;, &quot;explanation&quot;: &quot;Thi&#7871;u c&#7909;m &#8216;For example&#8217;; &#8216;know weather&#8217; &#8594; &#8216;learn about the weather&#8217;; &#8216;country&#8217; &#8594; &#8216;countries&#8217;.&quot;, &quot;original_text&quot;: &quot;Example, student can know weather in other country&quot;, &quot;suggested_correction&quot;: &quot;For example, students can learn about the weather in other countries&quot;}, {&quot;severity&quot;: &quot;high&quot;, &quot;error_type&quot;: &quot;grammar|vocabulary&quot;, &quot;explanation&quot;: &quot;Sai c&#7845;u tr&#250;c &#8216;is also help&#8217;; c&#7847;n &#8216;helps&#8217;; vi&#7871;t hoa &#8216;English&#8217;.&quot;, &quot;original_text&quot;: &quot;Learning international news is also help student speaking english well.&quot;, &quot;suggested_correction&quot;: &quot;Learning international news also helps students improve their English speaking skills.&quot;}, {&quot;severity&quot;: &quot;medium&quot;, &quot;error_type&quot;: &quot;grammar&quot;, &quot;explanation&quot;: &quot;Sai th&#236; ti&#7871;p di&#7877;n, v&#7883; tr&#237; &#8216;sometimes&#8217;.&quot;, &quot;original_text&quot;: &quot;I thinking international news sometime is boring.&quot;, &quot;suggested_correction&quot;: &quot;I think international news is sometimes boring.&quot;}, {&quot;severity&quot;: &quot;medium&quot;, &quot;error_type&quot;: &quot;grammar|punctuation&quot;, &quot;explanation&quot;: &quot;Danh t&#7915; s&#7889; nhi&#7873;u; thi&#7871;u d&#7845;u ph&#7849;y; m&#7841;o t&#7915;.&quot;, &quot;original_text&quot;: &quot;Student have many important subject, example maths, literature, or chemistry.&quot;, &quot;suggested_correction&quot;: &quot;Students have many important subjects, for example, maths, literature or chemistry.&quot;}, {&quot;severity&quot;: &quot;high&quot;, &quot;error_type&quot;: &quot;grammar&quot;, &quot;explanation&quot;: &quot;Thi&#7871;u &#273;&#7897;ng t&#7915; &#8216;is&#8217;; c&#7847;n m&#7841;o t&#7915; &#8216;a&#8217;.&quot;, &quot;original_text&quot;: &quot;International news not really important, only waste of time.&quot;, &quot;suggested_correction&quot;: &quot;International news is not really important; it is only a waste of time.&quot;}, {&quot;severity&quot;: &quot;high&quot;, &quot;error_type&quot;: &quot;grammar|spelling&quot;, &quot;explanation&quot;: &quot;C&#7847;n &#8216;In conclusion&#8217;; &#8216;news&#8217; coi nh&#432; s&#7889; &#237;t; th&#234;m &#8216;some&#8217;; danh t&#7915; s&#7889; nhi&#7873;u &#8216;disadvantages&#8217;.&quot;, &quot;original_text&quot;: &quot;I conclusion, international news have advantage but more disadvantage.&quot;, &quot;suggested_correction&quot;: &quot;In conclusion, international news has some advantages but more disadvantages.&quot;}]</span>\"\n            \"<span class=sf-dump-key>criteria_analysis</span>\" => \"<span class=sf-dump-str title=\"1493 characters\">[{&quot;criterion&quot;: &quot;Task Achievement&quot;, &quot;strengths&quot;: [&quot;&#272;&#227; n&#234;u &#273;&#432;&#7907;c hai quan &#273;i&#7875;m&quot;, &quot;C&#243; l&#7853;p tr&#432;&#7901;ng c&#225; nh&#226;n trong &#273;o&#7841;n k&#7871;t&quot;], &quot;weaknesses&quot;: [&quot;D&#432;&#7899;i 250 t&#7915; n&#234;n b&#7883; tr&#7915; &#273;i&#7875;m&quot;, &quot;L&#7853;p lu&#7853;n h&#7901;i h&#7907;t, v&#237; d&#7909; chung chung&quot;, &quot;Kh&#244;ng gi&#7843;i th&#237;ch v&#236; sao tin qu&#7889;c t&#7871; gi&#250;p n&#243;i ti&#7871;ng Anh t&#7889;t h&#417;n&quot;, &quot;Kh&#244;ng ph&#7843;n bi&#7879;n l&#7841;i quan &#273;i&#7875;m &#273;&#7889;i l&#7853;p&quot;], &quot;band_justification&quot;: &quot;&#272;&#225;p &#7913;ng ph&#7847;n n&#224;o y&#234;u c&#7847;u &#273;&#7873; nh&#432;ng ph&#225;t tri&#7875;n ch&#432;a &#273;&#7911;, thi&#7871;u chi&#7873;u s&#226;u v&#224; thi&#7871;u t&#7915; &#8594; band 4.0.&quot;}, {&quot;criterion&quot;: &quot;Coherence &amp; Cohesion&quot;, &quot;strengths&quot;: [&quot;C&#243; b&#7889; c&#7909;c c&#417; b&#7843;n: m&#7903;&#8211;th&#226;n&#8211;k&#7871;t&quot;, &quot;M&#7897;t s&#7889; t&#7915; n&#7889;i &#273;&#432;&#7907;c d&#249;ng (Firstly, However)&quot;], &quot;weaknesses&quot;: [&quot;Li&#234;n k&#7871;t &#253; k&#233;m, chuy&#7875;n &#273;o&#7841;n &#273;&#7897;t ng&#7897;t&quot;, &quot;C&#226;u ch&#7911; &#273;&#7873; m&#7901; nh&#7841;t&quot;, &quot;D&#249;ng l&#7863;p t&#7915; n&#7889;i, ch&#432;a &#273;a d&#7841;ng&quot;], &quot;band_justification&quot;: &quot;Li&#234;n k&#7871;t &#7903; m&#7913;c h&#7841;n ch&#7871;, &#253; &#273;&#244;i khi r&#7901;i r&#7841;c nh&#432;ng v&#7851;n theo tr&#236;nh t&#7921; chung &#8594; 4.5.&quot;}, {&quot;criterion&quot;: &quot;Lexical Resource&quot;, &quot;strengths&quot;: [&quot;T&#7915; v&#7921;ng quen thu&#7897;c, d&#7877; hi&#7875;u&quot;, &quot;M&#7897;t v&#224;i t&#7915; h&#7885;c thu&#7853;t &#273;&#417;n gi&#7843;n (advantage, disadvantage)&quot;], &quot;weaknesses&quot;: [&quot;L&#7863;p t&#7915; th&#432;&#7901;ng xuy&#234;n&quot;, &quot;Sai ch&#237;nh t&#7843; nhi&#7873;u&quot;, &quot;D&#249;ng t&#7915; ch&#432;a ch&#237;nh x&#225;c ho&#7863;c kh&#244;ng ph&#249; h&#7907;p v&#259;n phong h&#7885;c thu&#7853;t&quot;], &quot;band_justification&quot;: &quot;V&#7889;n t&#7915; h&#7841;n ch&#7871;, l&#7895;i ch&#237;nh t&#7843; &amp; ch&#7885;n t&#7915; &#7843;nh h&#432;&#7903;ng ng&#432;&#7901;i &#273;&#7885;c &#8594; 4.0.&quot;}, {&quot;criterion&quot;: &quot;Grammatical Range &amp; Accuracy&quot;, &quot;strengths&quot;: [&quot;Bi&#7871;t k&#7871;t th&#250;c c&#226;u b&#7857;ng d&#7845;u ch&#7845;m&quot;, &quot;M&#7897;t s&#7889; m&#7879;nh &#273;&#7873; &#273;&#417;n ch&#237;nh x&#225;c&quot;], &quot;weaknesses&quot;: [&quot;L&#7895;i S-V agreement li&#234;n t&#7909;c&quot;, &quot;Thi&#7871;u m&#7841;o t&#7915;, gi&#7899;i t&#7915;&quot;, &quot;C&#7845;u tr&#250;c c&#226;u &#273;&#417;n gi&#7843;n, hi&#7871;m c&#226;u ph&#7913;c&quot;, &quot;Sai th&#236; v&#224; thi&#7871;u &#273;&#7897;ng t&#7915; to be&quot;], &quot;band_justification&quot;: &quot;L&#7895;i th&#432;&#7901;ng xuy&#234;n khi&#7871;n kh&#243; hi&#7875;u, c&#7845;u tr&#250;c &#273;&#417;n gi&#7843;n &#8594; 3.5.&quot;}]</span>\"\n            \"<span class=sf-dump-key>statistics</span>\" => \"<span class=sf-dump-str title=\"145 characters\">{&quot;total_errors&quot;: 45, &quot;grammar_errors&quot;: 28, &quot;spelling_errors&quot;: 10, &quot;complexity_level&quot;: &quot;basic&quot;, &quot;sentence_variety&quot;: &quot;low&quot;, &quot;vocabulary_errors&quot;: 7}</span>\"\n            \"<span class=sf-dump-key>word_count</span>\" => <span class=sf-dump-num>179</span>\n            \"<span class=sf-dump-key>essay_length</span>\" => <span class=sf-dump-num>1184</span>\n            \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"9 characters\">completed</span>\"\n            \"<span class=sf-dump-key>error_message</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-18 17:02:55</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-18 17:03:22</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">previous</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:9</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>detailed_feedback</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n            \"<span class=sf-dump-key>highlighted_corrections</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n            \"<span class=sf-dump-key>criteria_analysis</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n            \"<span class=sf-dump-key>statistics</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n            \"<span class=sf-dump-key>overall_band_score</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:1</span>\"\n            \"<span class=sf-dump-key>task_achievement</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:1</span>\"\n            \"<span class=sf-dump-key>coherence_cohesion</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:1</span>\"\n            \"<span class=sf-dump-key>lexical_resource</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:1</span>\"\n            \"<span class=sf-dump-key>grammar_accuracy</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:1</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>user</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\User\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">User</span></span> {<a class=sf-dump-ref>#1470</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"5 characters\">users</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n              +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n              +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n              +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>3</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">anh vo</span>\"\n                \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"\n                \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"10 characters\">0327239691</span>\"\n                \"<span class=sf-dump-key>email_verified_at</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$kHdkDSFSoZnvKLzjz2BNNO.eTtDMX8mMlt3RY4/iSp3q.LGENyf.i</span>\"\n                \"<span class=sf-dump-key>role</span>\" => \"<span class=sf-dump-str title=\"4 characters\">user</span>\"\n                \"<span class=sf-dump-key>is_active</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>remember_token</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-18 16:16:51</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-18 16:16:51</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>3</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">anh vo</span>\"\n                \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"\n                \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"10 characters\">0327239691</span>\"\n                \"<span class=sf-dump-key>email_verified_at</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$kHdkDSFSoZnvKLzjz2BNNO.eTtDMX8mMlt3RY4/iSp3q.LGENyf.i</span>\"\n                \"<span class=sf-dump-key>role</span>\" => \"<span class=sf-dump-str title=\"4 characters\">user</span>\"\n                \"<span class=sf-dump-key>is_active</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>remember_token</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-18 16:16:51</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-18 16:16:51</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">previous</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>email_verified_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n                \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"6 characters\">hashed</span>\"\n                \"<span class=sf-dump-key>is_active</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadCallback</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadContext</span>: <span class=sf-dump-const>null</span>\n              +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">password</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"14 characters\">remember_token</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:6</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"5 characters\">email</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"5 characters\">phone</span>\"\n                <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"8 characters\">password</span>\"\n                <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"4 characters\">role</span>\"\n                <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"9 characters\">is_active</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">authPasswordName</span>: \"<span class=sf-dump-str title=\"8 characters\">password</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">rememberTokenName</span>: \"<span class=sf-dump-str title=\"14 characters\">remember_token</span>\"\n            </samp>}\n            \"<span class=sf-dump-key>essayQuestion</span>\" => <span class=sf-dump-const>null</span>\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadCallback</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">relationAutoloadContext</span>: <span class=sf-dump-const>null</span>\n          +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n          +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:18</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">user_id</span>\"\n            <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"17 characters\">essay_question_id</span>\"\n            <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"13 characters\">essay_content</span>\"\n            <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"9 characters\">task_type</span>\"\n            <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"10 characters\">time_limit</span>\"\n            <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"18 characters\">overall_band_score</span>\"\n            <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"16 characters\">task_achievement</span>\"\n            <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"18 characters\">coherence_cohesion</span>\"\n            <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"16 characters\">lexical_resource</span>\"\n            <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"16 characters\">grammar_accuracy</span>\"\n            <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"17 characters\">detailed_feedback</span>\"\n            <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"23 characters\">highlighted_corrections</span>\"\n            <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"17 characters\">criteria_analysis</span>\"\n            <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"10 characters\">statistics</span>\"\n            <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"10 characters\">word_count</span>\"\n            <span class=sf-dump-index>15</span> => \"<span class=sf-dump-str title=\"12 characters\">essay_length</span>\"\n            <span class=sf-dump-index>16</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n            <span class=sf-dump-index>17</span> => \"<span class=sf-dump-str title=\"13 characters\">error_message</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n          </samp>]\n        </samp>}\n        \"<span class=sf-dump-key>token</span>\" => \"<span class=sf-dump-str title=\"456 characters\">ZXlKcGRpSTZJbmM0UzIxQk5rdDZkVmxKV20wclVGUjZNRkY0Wm5jOVBTSXNJblpoYkhWbElqb2lOSEI1ZWtkV1owbFFUVkpGTld4NFRGSjRVRW80Y1VWMWN6UldaMFJzUjBzMGF6TTViVmt3V2pCNWVEbDRWWGRKTTA5ck9GRnVlbFpzZVdwcU4yRmhRbWR5WmxGWmRFMTJNMDFDYUhKa2RsTnpLMVF3YkdGYVNEZzFSMUZQTDFFdmNsVkplaTk1ZDBzMFVGUlVkbTlOWmtRME9EUXJlRlJ1SzFoaVlsSTBUbVVpTENKdFlXTWlPaUpsWlRCbE5qRXdZMk00WkRKbE0yRmtNamMyTW1JeE5XSTROekJsTW1VNE16ZGpNVEUxTVRrM09UZzBaRFE1WVRkbU1tVm1ZV1EzTjJRM05URmhNV1EwSWl3aWRHRm5Jam9pSW4wPQ==</span>\"\n      </samp>]\n      <span class=sf-dump-index>2</span> => []\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"42 characters\">app/Http/Controllers/ScoringController.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>222</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">view</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">scoring.public</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>attempt</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\ScoringAttempt\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ScoringAttempt</span></span> {<a class=sf-dump-ref href=#sf-dump-488470655-ref21405 title=\"2 occurrences\">#1405</a>}\n        \"<span class=sf-dump-key>token</span>\" => \"<span class=sf-dump-str title=\"456 characters\">ZXlKcGRpSTZJbmM0UzIxQk5rdDZkVmxKV20wclVGUjZNRkY0Wm5jOVBTSXNJblpoYkhWbElqb2lOSEI1ZWtkV1owbFFUVkpGTld4NFRGSjRVRW80Y1VWMWN6UldaMFJzUjBzMGF6TTViVmt3V2pCNWVEbDRWWGRKTTA5ck9GRnVlbFpzZVdwcU4yRmhRbWR5WmxGWmRFMTJNMDFDYUhKa2RsTnpLMVF3YkdGYVNEZzFSMUZQTDFFdmNsVkplaTk1ZDBzMFVGUlVkbTlOWmtRME9EUXJlRlJ1SzFoaVlsSTBUbVVpTENKdFlXTWlPaUpsWlRCbE5qRXdZMk00WkRKbE0yRmtNamMyTW1JeE5XSTROekJsTW1VNE16ZGpNVEUxTVRrM09UZzBaRFE1WVRkbU1tVm1ZV1EzTjJRM05URmhNV1EwSWl3aWRHRm5Jam9pSW4wPQ==</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"62 characters\">vendor/laravel/framework/src/Illuminate/Routing/Controller.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>54</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"10 characters\">publicShow</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"38 characters\">App\\Http\\Controllers\\ScoringController</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"456 characters\">ZXlKcGRpSTZJbmM0UzIxQk5rdDZkVmxKV20wclVGUjZNRkY0Wm5jOVBTSXNJblpoYkhWbElqb2lOSEI1ZWtkV1owbFFUVkpGTld4NFRGSjRVRW80Y1VWMWN6UldaMFJzUjBzMGF6TTViVmt3V2pCNWVEbDRWWGRKTTA5ck9GRnVlbFpzZVdwcU4yRmhRbWR5WmxGWmRFMTJNMDFDYUhKa2RsTnpLMVF3YkdGYVNEZzFSMUZQTDFFdmNsVkplaTk1ZDBzMFVGUlVkbTlOWmtRME9EUXJlRlJ1SzFoaVlsSTBUbVVpTENKdFlXTWlPaUpsWlRCbE5qRXdZMk00WkRKbE0yRmtNamMyTW1JeE5XSTROekJsTW1VNE16ZGpNVEUxTVRrM09UZzBaRFE1WVRkbU1tVm1ZV1EzTjJRM05URmhNV1EwSWl3aWRHRm5Jam9pSW4wPQ==</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>43</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callAction</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Routing\\Controller</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">publicShow</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>token</span>\" => \"<span class=sf-dump-str title=\"456 characters\">ZXlKcGRpSTZJbmM0UzIxQk5rdDZkVmxKV20wclVGUjZNRkY0Wm5jOVBTSXNJblpoYkhWbElqb2lOSEI1ZWtkV1owbFFUVkpGTld4NFRGSjRVRW80Y1VWMWN6UldaMFJzUjBzMGF6TTViVmt3V2pCNWVEbDRWWGRKTTA5ck9GRnVlbFpzZVdwcU4yRmhRbWR5WmxGWmRFMTJNMDFDYUhKa2RsTnpLMVF3YkdGYVNEZzFSMUZQTDFFdmNsVkplaTk1ZDBzMFVGUlVkbTlOWmtRME9EUXJlRlJ1SzFoaVlsSTBUbVVpTENKdFlXTWlPaUpsWlRCbE5qRXdZMk00WkRKbE0yRmtNamMyTW1JeE5XSTROekJsTW1VNE16ZGpNVEUxTVRrM09UZzBaRFE1WVRkbU1tVm1ZV1EzTjJRM05URmhNV1EwSWl3aWRHRm5Jam9pSW4wPQ==</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"57 characters\">vendor/laravel/framework/src/Illuminate/Routing/Route.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>265</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">dispatch</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Routing\\ControllerDispatcher</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"47 characters\">[object App\\Http\\Controllers\\ScoringController]</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"10 characters\">publicShow</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"57 characters\">vendor/laravel/framework/src/Illuminate/Routing/Route.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>211</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"13 characters\">runController</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Illuminate\\Routing\\Route</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>808</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"3 characters\">run</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Illuminate\\Routing\\Route</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>169</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Routing\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>66</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>11</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Barryvdh\\Debugbar\\Middleware\\InjectDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>12</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"43 characters\">app/Http/Middleware/EnsureUserHasCredit.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>32</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>13</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">App\\Http\\Middleware\\EnsureUserHasCredit</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>14</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"81 characters\">vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>50</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>15</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"48 characters\">Illuminate\\Routing\\Middleware\\SubstituteBindings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>16</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>63</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>17</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Auth\\Middleware\\Authenticate</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>18</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"86 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>87</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>19</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"53 characters\">Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>20</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>48</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>21</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\View\\Middleware\\ShareErrorsFromSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>22</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"75 characters\">vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>120</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>23</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"75 characters\">vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>63</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"21 characters\">handleStatefulRequest</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Illuminate\\Session\\Middleware\\StartSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Session\\Store]</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>24</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Illuminate\\Session\\Middleware\\StartSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>25</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>36</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>26</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>27</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"76 characters\">vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>74</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>28</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Illuminate\\Cookie\\Middleware\\EncryptCookies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>29</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>126</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>30</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>807</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>31</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>786</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"19 characters\">runRouteWithinStack</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>32</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>750</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">runRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>33</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>739</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"15 characters\">dispatchToRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>34</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>200</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">dispatch</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>35</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>169</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Illuminate\\Foundation\\Http\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>36</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>66</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>37</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Barryvdh\\Debugbar\\Middleware\\InjectDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>38</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>39</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"96 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>31</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>40</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"63 characters\">Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>41</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>42</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>51</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>43</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\Foundation\\Http\\Middleware\\TrimStrings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>44</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"76 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>27</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>45</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Illuminate\\Http\\Middleware\\ValidatePostSize</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>46</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"103 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>109</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>47</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"70 characters\">Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>48</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"70 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>48</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>49</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"37 characters\">Illuminate\\Http\\Middleware\\HandleCors</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>50</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>58</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>51</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Http\\Middleware\\TrustProxies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>52</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"94 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>22</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>53</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"61 characters\">Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>54</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"80 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePathEncoding.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>26</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>55</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"47 characters\">Illuminate\\Http\\Middleware\\ValidatePathEncoding</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>56</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>126</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>57</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>175</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>58</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>144</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"24 characters\">sendRequestThroughRouter</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>59</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Application.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>1219</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>60</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"16 characters\">public/index.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>20</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"13 characters\">handleRequest</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Application</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>61</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"71 characters\">vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>23</span>\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"58 characters\">C:\\laragon\\www\\ielts\\ielts-scoring-system\\public\\index.php</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"12 characters\">require_once</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-488470655\", {\"maxDepth\":0})</script>\n", "surrounding_lines": ["            }\n", "        }\n", "\n", "        throw new InvalidArgumentException(\"View [{$name}] not found.\");\n", "    }\n", "\n", "    /**\n"], "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FView%2FFileViewFinder.php&line=138", "ajax": false, "filename": "FileViewFinder.php", "line": "138"}}]}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.20.0", "PHP Version": "8.2.27", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 22, "nb_templates": 22, "templates": [{"name": "laravel-exceptions-renderer::show", "param_count": null, "params": [], "start": **********.539283, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/show.blade.phplaravel-exceptions-renderer::show", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fshow.blade.php&line=1", "ajax": false, "filename": "show.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.navigation", "param_count": null, "params": [], "start": **********.540863, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/navigation.blade.phplaravel-exceptions-renderer::components.navigation", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Fnavigation.blade.php&line=1", "ajax": false, "filename": "navigation.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.theme-switcher", "param_count": null, "params": [], "start": **********.541232, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/theme-switcher.blade.phplaravel-exceptions-renderer::components.theme-switcher", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ftheme-switcher.blade.php&line=1", "ajax": false, "filename": "theme-switcher.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.icons.sun", "param_count": null, "params": [], "start": **********.541672, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/icons/sun.blade.phplaravel-exceptions-renderer::components.icons.sun", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ficons%2Fsun.blade.php&line=1", "ajax": false, "filename": "sun.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.icons.moon", "param_count": null, "params": [], "start": **********.541986, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/icons/moon.blade.phplaravel-exceptions-renderer::components.icons.moon", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ficons%2Fmoon.blade.php&line=1", "ajax": false, "filename": "moon.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.icons.sun", "param_count": null, "params": [], "start": **********.54225, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/icons/sun.blade.phplaravel-exceptions-renderer::components.icons.sun", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ficons%2Fsun.blade.php&line=1", "ajax": false, "filename": "sun.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.icons.moon", "param_count": null, "params": [], "start": **********.542451, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/icons/moon.blade.phplaravel-exceptions-renderer::components.icons.moon", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ficons%2Fmoon.blade.php&line=1", "ajax": false, "filename": "moon.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.icons.computer-desktop", "param_count": null, "params": [], "start": **********.542696, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/icons/computer-desktop.blade.phplaravel-exceptions-renderer::components.icons.computer-desktop", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ficons%2Fcomputer-desktop.blade.php&line=1", "ajax": false, "filename": "computer-desktop.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.header", "param_count": null, "params": [], "start": **********.543006, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/header.blade.phplaravel-exceptions-renderer::components.header", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.card", "param_count": null, "params": [], "start": **********.54339, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/card.blade.phplaravel-exceptions-renderer::components.card", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Fcard.blade.php&line=1", "ajax": false, "filename": "card.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.trace-and-editor", "param_count": null, "params": [], "start": **********.543734, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/trace-and-editor.blade.phplaravel-exceptions-renderer::components.trace-and-editor", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ftrace-and-editor.blade.php&line=1", "ajax": false, "filename": "trace-and-editor.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.trace", "param_count": null, "params": [], "start": **********.55453, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/trace.blade.phplaravel-exceptions-renderer::components.trace", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ftrace.blade.php&line=1", "ajax": false, "filename": "trace.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.icons.chevron-down", "param_count": null, "params": [], "start": **********.555091, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/icons/chevron-down.blade.phplaravel-exceptions-renderer::components.icons.chevron-down", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ficons%2Fchevron-down.blade.php&line=1", "ajax": false, "filename": "chevron-down.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.icons.chevron-up", "param_count": null, "params": [], "start": **********.555399, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/icons/chevron-up.blade.phplaravel-exceptions-renderer::components.icons.chevron-up", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ficons%2Fchevron-up.blade.php&line=1", "ajax": false, "filename": "chevron-up.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.icons.chevron-up", "param_count": null, "params": [], "start": **********.555652, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/icons/chevron-up.blade.phplaravel-exceptions-renderer::components.icons.chevron-up", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ficons%2Fchevron-up.blade.php&line=1", "ajax": false, "filename": "chevron-up.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.icons.chevron-down", "param_count": null, "params": [], "start": **********.555813, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/icons/chevron-down.blade.phplaravel-exceptions-renderer::components.icons.chevron-down", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ficons%2Fchevron-down.blade.php&line=1", "ajax": false, "filename": "chevron-down.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.editor", "param_count": null, "params": [], "start": **********.57373, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/editor.blade.phplaravel-exceptions-renderer::components.editor", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Feditor.blade.php&line=1", "ajax": false, "filename": "editor.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.card", "param_count": null, "params": [], "start": **********.613127, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/card.blade.phplaravel-exceptions-renderer::components.card", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Fcard.blade.php&line=1", "ajax": false, "filename": "card.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.context", "param_count": null, "params": [], "start": **********.613611, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/context.blade.phplaravel-exceptions-renderer::components.context", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Fcontext.blade.php&line=1", "ajax": false, "filename": "context.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.card", "param_count": null, "params": [], "start": **********.614329, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/card.blade.phplaravel-exceptions-renderer::components.card", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Fcard.blade.php&line=1", "ajax": false, "filename": "card.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.card", "param_count": null, "params": [], "start": **********.614888, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/card.blade.phplaravel-exceptions-renderer::components.card", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Fcard.blade.php&line=1", "ajax": false, "filename": "card.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.layout", "param_count": null, "params": [], "start": **********.615854, "type": "blade", "hash": "bladeC:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/layout.blade.phplaravel-exceptions-renderer::components.layout", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Flayout.blade.php&line=1", "ajax": false, "filename": "layout.blade.php", "line": "?"}}]}, "queries": {"count": 6, "nb_statements": 5, "nb_visible_statements": 6, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.023420000000000003, "accumulated_duration_str": "23.42ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 158}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 56}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}], "start": **********.356982, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "ietls", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `sessions` where `id` = 'pCASZAoZFeOJeGpNZ7yRLWlVdF889fM8IsLGw0i1' limit 1", "type": "query", "params": [], "bindings": ["pCASZAoZFeOJeGpNZ7yRLWlVdF889fM8IsLGw0i1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.361907, "duration": 0.019690000000000003, "duration_str": "19.69ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "ietls", "explain": null, "start_percent": 0, "width_percent": 84.073}, {"sql": "select * from `users` where `id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 82}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.405039, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "ietls", "explain": null, "start_percent": 84.073, "width_percent": 3.8}, {"sql": "select * from `user_credits` where `user_credits`.`user_id` = 3 and `user_credits`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "middleware", "name": "ensure.credit", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\app\\Http\\Middleware\\EnsureUserHasCredit.php", "line": 22}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}], "start": **********.4151242, "duration": 0.0015, "duration_str": "1.5ms", "memory": 0, "memory_str": null, "filename": "ensure.credit:22", "source": {"index": 21, "namespace": "middleware", "name": "ensure.credit", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\app\\Http\\Middleware\\EnsureUserHasCredit.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fapp%2FHttp%2FMiddleware%2FEnsureUserHasCredit.php&line=22", "ajax": false, "filename": "EnsureUserHasCredit.php", "line": "22"}, "connection": "ietls", "explain": null, "start_percent": 87.874, "width_percent": 6.405}, {"sql": "select * from `scoring_attempts` where `scoring_attempts`.`id` = 10 limit 1", "type": "query", "params": [], "bindings": [10], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Http/Controllers/ScoringController.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\app\\Http\\Controllers\\ScoringController.php", "line": 210}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.419641, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "ScoringController.php:210", "source": {"index": 17, "namespace": null, "name": "app/Http/Controllers/ScoringController.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\app\\Http\\Controllers\\ScoringController.php", "line": 210}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fapp%2FHttp%2FControllers%2FScoringController.php&line=210", "ajax": false, "filename": "ScoringController.php", "line": "210"}, "connection": "ietls", "explain": null, "start_percent": 94.278, "width_percent": 3.715}, {"sql": "select * from `users` where `users`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/ScoringController.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\app\\Http\\Controllers\\ScoringController.php", "line": 210}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.422621, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "ScoringController.php:210", "source": {"index": 22, "namespace": null, "name": "app/Http/Controllers/ScoringController.php", "file": "C:\\laragon\\www\\ielts\\ielts-scoring-system\\app\\Http\\Controllers\\ScoringController.php", "line": 210}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fapp%2FHttp%2FControllers%2FScoringController.php&line=210", "ajax": false, "filename": "ScoringController.php", "line": "210"}, "connection": "ietls", "explain": null, "start_percent": 97.993, "width_percent": 2.007}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\UserCredit": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fapp%2FModels%2FUserCredit.php&line=1", "ajax": false, "filename": "UserCredit.php", "line": "?"}}, "App\\Models\\ScoringAttempt": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fapp%2FModels%2FScoringAttempt.php&line=1", "ajax": false, "filename": "ScoringAttempt.php", "line": "?"}}}, "count": 4, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "500 Internal Server Error", "full_url": "http://127.0.0.1:8000/share/ZXlKcGRpSTZJbmM0UzIxQk5rdDZkVmxKV20wclVGUjZNRkY0Wm5jOVBTSXNJblpoYkhWbElq...", "action_name": "scoring.public", "controller_action": "App\\Http\\Controllers\\ScoringController@publicShow", "uri": "GET share/{token}", "controller": "App\\Http\\Controllers\\ScoringController@publicShow<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fapp%2FHttp%2FControllers%2FScoringController.php&line=200\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fielts%2Fielts-scoring-system%2Fapp%2FHttp%2FControllers%2FScoringController.php&line=200\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/ScoringController.php:200-223</a>", "middleware": "web", "duration": "426ms", "peak_memory": "28MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-40399043 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-40399043\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-359547735 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-359547735\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-367563925 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"734 characters\">XSRF-TOKEN=eyJpdiI6IkNEUmR4MFJUZTNTcVZ2VXc5WnhRWkE9PSIsInZhbHVlIjoiL01HeHgrYk42OVI0Sy9QdTlodHB3QVppanRkd1ZDNVBjMUVSamVRNHZNZFhMTlpDd1NnTlNXV2UremczL1hBSXRXZXNvc1ZtRktMTVVHQlhvc3pLL21YUy9iQ1YvV3dJN2dSUThBNFRWc3MwN0ZrYm9qaG5MSVJQN3IzS1dyMk4iLCJtYWMiOiJhYmY5OWRmY2ZmMDExZWVmYmYzZmIzODk3N2E1MTdmZmFkMWE0Mzk5Yzk5ZDkyOGZlNjE3MTI4ZmQxM2QyMWEwIiwidGFnIjoiIn0%3D; i_e_l_t_s_a_i_scoring_system_session=eyJpdiI6InRCWFcyaXh0K2pEcmRTdldpK3JpSFE9PSIsInZhbHVlIjoiRlR2K1hhWEpuNC95SmhFZjhMUU5YUkN4WmhiMml5T0h4Tkc3aUl6dHUxVnU3N1pwSXdHQmhWc09kUC9JdmRDQUVqUnZZMVJPZlR2M3J2cHYwVk1ROHVIK1hRWkNYNVI4b2t4N05FeFRVNitvMHgwYjdEZVFOb3ZxZDJLNFpNdGoiLCJtYWMiOiI3MTJjYjQyNjk0Nzc3YmU5YWI3OGZkYzkzZGVkYmMwNWZjYWM4NmM1ODUyOGM4YjNjZDk1NzNiMDY1YzNmMGNjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-367563925\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-501822388 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cUmWl6GniRBNGmoQJUVf8GM28NkKFkyfd5CEwpDM</span>\"\n  \"<span class=sf-dump-key>i_e_l_t_s_a_i_scoring_system_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pCASZAoZFeOJeGpNZ7yRLWlVdF889fM8IsLGw0i1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-501822388\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-275369093 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 18 Jul 2025 17:10:11 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-275369093\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-820982720 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cUmWl6GniRBNGmoQJUVf8GM28NkKFkyfd5CEwpDM</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/scoring/show/9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"484 characters\">http://127.0.0.1:8000/share/ZXlKcGRpSTZJbmM0UzIxQk5rdDZkVmxKV20wclVGUjZNRkY0Wm5jOVBTSXNJblpoYkhWbElqb2lOSEI1ZWtkV1owbFFUVkpGTld4NFRGSjRVRW80Y1VWMWN6UldaMFJzUjBzMGF6TTViVmt3V2pCNWVEbDRWWGRKTTA5ck9GRnVlbFpzZVdwcU4yRmhRbWR5WmxGWmRFMTJNMDFDYUhKa2RsTnpLMVF3YkdGYVNEZzFSMUZQTDFFdmNsVkplaTk1ZDBzMFVGUlVkbTlOWmtRME9EUXJlRlJ1SzFoaVlsSTBUbVVpTENKdFlXTWlPaUpsWlRCbE5qRXdZMk00WkRKbE0yRmtNamMyTW1JeE5XSTROekJsTW1VNE16ZGpNVEUxTVRrM09UZzBaRFE1WVRkbU1tVm1ZV1EzTjJRM05URmhNV1EwSWl3aWRHRm5Jam9pSW4wPQ==</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>3</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-820982720\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "500 Internal Server Error", "full_url": "http://127.0.0.1:8000/share/ZXlKcGRpSTZJbmM0UzIxQk5rdDZkVmxKV20wclVGUjZNRkY0Wm5jOVBTSXNJblpoYkhWbElq...", "action_name": "scoring.public", "controller_action": "App\\Http\\Controllers\\ScoringController@publicShow"}, "badge": "500 Internal Server Error"}}