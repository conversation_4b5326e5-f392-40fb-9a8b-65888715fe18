{"name": "hashids/hashids", "description": "Generate short, unique, non-sequential ids (like YouTube and Bitly) from numbers", "license": "MIT", "keywords": ["bitly", "decode", "encode", "hash", "hashid", "hashids", "ids", "obfuscate", "youtube"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "homepage": "https://hashids.org/php", "require": {"php": "^8.1", "ext-mbstring": "*"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "suggest": {"ext-bcmath": "Required to use BC Math arbitrary precision mathematics (*).", "ext-gmp": "Required to use GNU multiple precision mathematics (*)."}, "minimum-stability": "dev", "prefer-stable": true, "autoload": {"psr-4": {"Hashids\\": "src/"}}, "autoload-dev": {"psr-4": {"Hashids\\Tests\\": "tests/"}}, "config": {"preferred-install": "dist"}, "extra": {"branch-alias": {"dev-master": "5.0-dev"}}}