<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\EssayQuestion;
use App\Models\ScoringAttempt;
use Illuminate\Support\Facades\Auth;

class HomeController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth')->except('welcome');
    }

    /**
     * Show the welcome page
     */
    public function welcome()
    {
        $questions = EssayQuestion::active()->get();
        $recentAttempts = collect();

        if (Auth::check()) {
            $recentAttempts = ScoringAttempt::where('user_id', Auth::id())
                ->with('essayQuestion')
                ->orderBy('created_at', 'desc')
                ->limit(5)
                ->get();
        }

        return view('home.index', compact('questions', 'recentAttempts'));
    }

    /**
     * Show the application dashboard.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function index()
    {
        $user = Auth::user();
        $totalAttempts = ScoringAttempt::where('user_id', $user->id)->count();
        $completedAttempts = ScoringAttempt::where('user_id', $user->id)
            ->where('status', 'completed')
            ->count();

        $averageScore = ScoringAttempt::where('user_id', $user->id)
            ->where('status', 'completed')
            ->avg('overall_band_score');

        $recentAttempts = ScoringAttempt::where('user_id', $user->id)
            ->with('essayQuestion')
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        $availableCredits = $user->getAvailableCredits();

        return view('home.dashboard', compact(
            'totalAttempts',
            'completedAttempts',
            'averageScore',
            'recentAttempts',
            'availableCredits'
        ));
    }
}
