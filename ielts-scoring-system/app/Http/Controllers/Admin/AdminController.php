<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\User;
use App\Models\ScoringAttempt;
use App\Models\UserCredit;

class AdminController extends Controller
{
    public function index()
    {
        $totalUsers = User::count();
        $totalAttempts = ScoringAttempt::count();
        $completedAttempts = ScoringAttempt::where('status', 'completed')->count();
        $totalCreditsUsed = UserCredit::sum('used_credits');

        $recentUsers = User::latest()->limit(5)->get();
        $recentAttempts = ScoringAttempt::with(['user', 'essayQuestion'])
            ->latest()
            ->limit(10)
            ->get();

        return view('admin.index', compact(
            'totalUsers',
            'totalAttempts',
            'completedAttempts',
            'totalCreditsUsed',
            'recentUsers',
            'recentAttempts'
        ));
    }
}
