<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;
use App\Models\IeltsAttempt;

class LandingController extends Controller
{
    public function index()
    {
        // Statistics for landing page
        $stats = [
            'total_users' => User::count() ?: 100, // Default values for demo
            'total_attempts' => IeltsAttempt::count() ?: 1250,
            'completed_attempts' => IeltsAttempt::whereNotNull('overall_score')->count() ?: 1180,
            'average_score' => IeltsAttempt::whereNotNull('overall_score')->avg('overall_score') ?: 7.2,
            'success_rate' => $this->calculateSuccessRate(),
        ];

        // Recent testimonials or success stories
        $recentScores = IeltsAttempt::with('user')
            ->whereNotNull('overall_score')
            ->where('overall_score', '>=', 6.5)
            ->latest()
            ->take(6)
            ->get();

        return view('landing.index', compact('stats', 'recentScores'));
    }

    private function calculateSuccessRate()
    {
        $totalAttempts = IeltsAttempt::whereNotNull('overall_score')->count();
        if ($totalAttempts === 0) return 95; // Default demo value

        $successfulAttempts = IeltsAttempt::whereNotNull('overall_score')
            ->where('overall_score', '>=', 6.5)
            ->count();

        return round(($successfulAttempts / $totalAttempts) * 100, 1);
    }
}
