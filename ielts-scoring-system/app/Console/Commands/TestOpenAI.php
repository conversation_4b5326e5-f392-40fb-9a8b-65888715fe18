<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\IELTSScorer;

class TestOpenAI extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:openai';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test OpenAI API connection';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Testing OpenAI API connection...');

        $scorer = new IELTSScorer();

        $this->info('Configuration:');
        $this->line('API URL: ' . config('services.openai.api_url'));
        $this->line('Model: ' . config('services.openai.model'));
        $this->line('API Key: ' . (config('services.openai.api_key') ? 'Set (' . strlen(config('services.openai.api_key')) . ' chars)' : 'Not set'));

        $this->newLine();
        $this->info('Testing connection...');

        $result = $scorer->testConnection();

        if ($result['success']) {
            $this->info('✅ API connection successful!');
            $this->line('Response: ' . $result['response']);
        } else {
            $this->error('❌ API connection failed!');
            $this->line('Error: ' . $result['error']);
        }

        return $result['success'] ? 0 : 1;
    }
}
