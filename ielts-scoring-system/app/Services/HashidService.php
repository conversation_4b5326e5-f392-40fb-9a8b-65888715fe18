<?php

namespace App\Services;

use Hashids\Hashids;

class HashidService
{
    private static $hashids;
    
    /**
     * Get Hashids instance
     */
    private static function getHashids(): Hashids
    {
        if (!self::$hashids) {
            // Use app key as salt for security
            $salt = config('app.key') . 'ielts_scoring';
            
            // Minimum length 8 characters, custom alphabet
            self::$hashids = new Hashids($salt, 8, 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890');
        }
        
        return self::$hashids;
    }
    
    /**
     * Encode an ID to hashid
     */
    public static function encode(int $id): string
    {
        return self::getHashids()->encode($id);
    }
    
    /**
     * Decode a hashid to ID
     */
    public static function decode(string $hashid): ?int
    {
        $decoded = self::getHashids()->decode($hashid);
        
        return !empty($decoded) ? $decoded[0] : null;
    }
    
    /**
     * Encode multiple IDs
     */
    public static function encodeMultiple(array $ids): string
    {
        return self::getHashids()->encode(...$ids);
    }
    
    /**
     * Decode to multiple IDs
     */
    public static function decodeMultiple(string $hashid): array
    {
        return self::getHashids()->decode($hashid);
    }
    
    /**
     * Check if a string is a valid hashid
     */
    public static function isValid(string $hashid): bool
    {
        $decoded = self::getHashids()->decode($hashid);
        return !empty($decoded);
    }
    
    /**
     * Generate hashid for scoring attempt
     */
    public static function encodeAttempt(int $attemptId): string
    {
        // Add timestamp to make it more unique and secure
        $timestamp = time();
        return self::getHashids()->encode($attemptId, $timestamp % 1000);
    }
    
    /**
     * Decode hashid for scoring attempt
     */
    public static function decodeAttempt(string $hashid): ?int
    {
        $decoded = self::getHashids()->decode($hashid);
        
        // Return the first number which should be the attempt ID
        return !empty($decoded) ? $decoded[0] : null;
    }
}
