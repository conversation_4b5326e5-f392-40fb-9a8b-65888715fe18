<!doctype html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <!-- CSRF <PERSON> -->
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>@yield('title', 'Admin Dashboard - IELTS AI Scoring System')</title>

    <!-- Fonts -->
    <link rel="dns-prefetch" href="//fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=Nunito:300,400,600,700,800,900" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- AOS Animation -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">

    <!-- SweetAlert2 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">

    @stack('styles')
</head>
<body>
    <div id="app">
        <!-- Admin Navigation -->
        <nav class="admin-navbar">
            <div class="container-fluid">
                <div class="navbar-content">
                    <a class="navbar-brand" href="{{ route('admin.index') }}">
                        <div class="brand-icon">
                            <i class="fas fa-crown"></i>
                        </div>
                        <div class="brand-text">
                            <span class="brand-name">IELTS AI Admin</span>
                            <span class="brand-subtitle">Management System</span>
                        </div>
                    </a>

                    <div class="navbar-menu">
                        <div class="nav-links">
                            <a class="nav-link {{ request()->routeIs('admin.index') ? 'active' : '' }}" href="{{ route('admin.index') }}">
                                <i class="fas fa-chart-line"></i>
                                <span>Dashboard</span>
                            </a>
                            <a class="nav-link {{ request()->routeIs('admin.users.*') ? 'active' : '' }}" href="{{ route('admin.users.index') }}">
                                <i class="fas fa-users"></i>
                                <span>Users</span>
                            </a>
                            <a class="nav-link" href="{{ route('dashboard') }}">
                                <i class="fas fa-arrow-left"></i>
                                <span>Back to Site</span>
                            </a>
                        </div>
                    </div>

                    <!-- User Menu -->
                    <div class="navbar-user">
                        <div class="dropdown">
                            <button class="user-button" type="button" data-bs-toggle="dropdown">
                                <div class="user-avatar">
                                    <i class="fas fa-user-shield"></i>
                                </div>
                                <div class="user-info">
                                    <div class="user-name">{{ Auth::user()->name }}</div>
                                    <div class="user-role">Administrator</div>
                                </div>
                                <i class="fas fa-chevron-down"></i>
                            </button>
                            <ul class="dropdown-menu user-dropdown">
                                <li>
                                    <a class="dropdown-item" href="{{ route('dashboard') }}">
                                        <i class="fas fa-home"></i>
                                        <span>User Dashboard</span>
                                    </a>
                                </li>
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <a class="dropdown-item" href="{{ route('logout') }}"
                                       onclick="event.preventDefault(); document.getElementById('logout-form').submit();">
                                        <i class="fas fa-sign-out-alt"></i>
                                        <span>Đăng xuất</span>
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="admin-main">
            @yield('content')
        </main>

        <!-- Logout Form -->
        <form id="logout-form" action="{{ route('logout') }}" method="POST" class="d-none">
            @csrf
        </form>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <script>
        // Initialize AOS
        AOS.init({
            duration: 800,
            easing: 'ease-in-out',
            once: true,
            mirror: false
        });
    </script>

    @stack('scripts')

    <style>
    /* Admin Layout Styles */
    :root {
        --admin-primary: #667eea;
        --admin-secondary: #764ba2;
        --admin-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        --admin-shadow: 0 10px 40px rgba(0,0,0,0.1);
        --admin-border-radius: 16px;
        --admin-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }

    body {
        font-family: 'Inter', sans-serif;
        background: #f8fafc;
        color: #2d3748;
        line-height: 1.6;
    }

    /* Admin Navbar */
    .admin-navbar {
        background: var(--admin-gradient);
        backdrop-filter: blur(20px);
        border-bottom: 1px solid rgba(255,255,255,0.1);
        padding: 1rem 0;
        position: sticky;
        top: 0;
        z-index: 1000;
        box-shadow: var(--admin-shadow);
    }

    .navbar-content {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
    }

    .navbar-brand {
        display: flex;
        align-items: center;
        gap: 1rem;
        text-decoration: none;
        color: white;
    }

    .brand-icon {
        width: 50px;
        height: 50px;
        background: rgba(255,255,255,0.2);
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        color: #ffd700;
    }

    .brand-text {
        display: flex;
        flex-direction: column;
    }

    .brand-name {
        font-size: 1.25rem;
        font-weight: 800;
        line-height: 1;
    }

    .brand-subtitle {
        font-size: 0.8rem;
        opacity: 0.8;
        font-weight: 500;
    }

    .navbar-menu {
        display: flex;
        justify-content: center;
        flex: 1;
    }

    .nav-links {
        display: flex;
        gap: 2rem;
        align-items: center;
    }

    .nav-link {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.75rem 1.5rem;
        border-radius: 12px;
        text-decoration: none;
        color: rgba(255,255,255,0.9);
        font-weight: 500;
        transition: var(--admin-transition);
        position: relative;
    }

    .nav-link:hover {
        color: white;
        background: rgba(255,255,255,0.1);
        transform: translateY(-2px);
    }

    .nav-link.active {
        color: white;
        background: rgba(255,255,255,0.2);
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    }

    .navbar-user {
        display: flex;
        align-items: center;
    }

    .user-button {
        display: flex;
        align-items: center;
        gap: 1rem;
        padding: 0.75rem 1rem;
        background: rgba(255,255,255,0.1);
        border: 1px solid rgba(255,255,255,0.2);
        border-radius: 12px;
        color: white;
        cursor: pointer;
        transition: var(--admin-transition);
    }

    .user-button:hover {
        background: rgba(255,255,255,0.2);
        transform: translateY(-2px);
    }

    .user-avatar {
        width: 40px;
        height: 40px;
        background: rgba(255,255,255,0.2);
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.1rem;
    }

    .user-info {
        display: flex;
        flex-direction: column;
        text-align: left;
    }

    .user-name {
        font-weight: 600;
        font-size: 0.9rem;
        line-height: 1;
    }

    .user-role {
        font-size: 0.75rem;
        opacity: 0.8;
        font-weight: 500;
    }

    .user-dropdown {
        background: white;
        border: none;
        border-radius: 12px;
        box-shadow: var(--admin-shadow);
        padding: 0.5rem;
        margin-top: 0.5rem;
    }

    .user-dropdown .dropdown-item {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding: 0.75rem 1rem;
        border-radius: 8px;
        color: #2d3748;
        font-weight: 500;
        transition: var(--admin-transition);
    }

    .user-dropdown .dropdown-item:hover {
        background: #f7fafc;
        color: var(--admin-primary);
    }

    /* Main Content */
    .admin-main {
        min-height: calc(100vh - 100px);
    }

    /* Responsive */
    @media (max-width: 768px) {
        .nav-links {
            gap: 1rem;
        }
        
        .nav-link span {
            display: none;
        }
        
        .brand-text {
            display: none;
        }
        
        .user-info {
            display: none;
        }
    }
    </style>
</body>
</html>
