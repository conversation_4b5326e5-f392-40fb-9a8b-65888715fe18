@extends('layouts.app')

@section('title', 'Admin Dashboard - IELTS AI Scoring System')

@section('content')
<div class="modern-admin-dashboard">
    <!-- Hero Section -->
    <div class="admin-hero-section">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <div class="hero-content">
                        <h1 class="hero-title">
                            <span class="admin-badge">ADMIN</span>
                            <span class="title-text">Dashboard Quản Trị</span>
                        </h1>
                        <p class="hero-subtitle">
                            Quản lý toàn bộ hệ thống chấm thi IELTS AI một cách hiệu quả và chuyên nghiệp
                        </p>
                        <div class="hero-actions">
                            <a href="{{ route('admin.users.create') }}" class="btn-modern btn-primary-modern">
                                <i class="fas fa-user-plus"></i>
                                <span>Thêm người dùng</span>
                            </a>
                            <a href="{{ route('admin.users.index') }}" class="btn-modern btn-secondary-modern">
                                <i class="fas fa-users"></i>
                                <span>Quản lý users</span>
                            </a>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4">
                    <div class="admin-illustration">
                        <div class="floating-admin-card">
                            <i class="fas fa-crown"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Admin Stats -->
    <div class="container-fluid">
        <div class="admin-stats-section">
            <div class="row g-4">
                <div class="col-xl-3 col-md-6">
                    <div class="admin-stat-card" data-aos="fade-up" data-aos-delay="100">
                        <div class="stat-icon-wrapper">
                            <div class="stat-icon bg-gradient-primary">
                                <i class="fas fa-users"></i>
                            </div>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number">{{ $totalUsers }}</div>
                            <div class="stat-label">Tổng người dùng</div>
                            <div class="stat-trend positive">
                                <i class="fas fa-arrow-up"></i>
                                <span>+{{ $totalUsers > 10 ? '12%' : '100%' }} tháng này</span>
                            </div>
                        </div>
                        <div class="stat-action">
                            <a href="{{ route('admin.users.index') }}" class="action-link">
                                <i class="fas fa-arrow-right"></i>
                            </a>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6">
                    <div class="admin-stat-card" data-aos="fade-up" data-aos-delay="200">
                        <div class="stat-icon-wrapper">
                            <div class="stat-icon bg-gradient-success">
                                <i class="fas fa-file-alt"></i>
                            </div>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number">{{ $totalAttempts }}</div>
                            <div class="stat-label">Tổng bài thi</div>
                            <div class="stat-trend positive">
                                <i class="fas fa-arrow-up"></i>
                                <span>+{{ $totalAttempts > 50 ? '25%' : '200%' }} tháng này</span>
                            </div>
                        </div>
                        <div class="stat-action">
                            <a href="#" class="action-link">
                                <i class="fas fa-arrow-right"></i>
                            </a>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6">
                    <div class="admin-stat-card" data-aos="fade-up" data-aos-delay="300">
                        <div class="stat-icon-wrapper">
                            <div class="stat-icon bg-gradient-warning">
                                <i class="fas fa-check-circle"></i>
                            </div>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number">{{ $completedAttempts }}</div>
                            <div class="stat-label">Bài đã hoàn thành</div>
                            <div class="stat-trend positive">
                                <i class="fas fa-arrow-up"></i>
                                <span>+{{ $completedAttempts > 30 ? '18%' : '150%' }} tháng này</span>
                            </div>
                        </div>
                        <div class="stat-action">
                            <a href="#" class="action-link">
                                <i class="fas fa-arrow-right"></i>
                            </a>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6">
                    <div class="admin-stat-card" data-aos="fade-up" data-aos-delay="400">
                        <div class="stat-icon-wrapper">
                            <div class="stat-icon bg-gradient-info">
                                <i class="fas fa-coins"></i>
                            </div>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number">{{ $totalCreditsUsed }}</div>
                            <div class="stat-label">Credits đã sử dụng</div>
                            <div class="stat-trend positive">
                                <i class="fas fa-arrow-up"></i>
                                <span>+{{ $totalCreditsUsed > 100 ? '15%' : '50%' }} tháng này</span>
                            </div>
                        </div>
                        <div class="stat-action">
                            <a href="#" class="action-link">
                                <i class="fas fa-arrow-right"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Management Sections -->
    <div class="container-fluid">
        <div class="row g-4">
            <!-- Recent Users -->
            <div class="col-lg-6">
                <div class="modern-card" data-aos="fade-up" data-aos-delay="500">
                    <div class="modern-card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="section-title">
                                <h3 class="title">Người dùng mới</h3>
                                <p class="subtitle">Các tài khoản được tạo gần đây</p>
                            </div>
                            <a href="{{ route('admin.users.index') }}" class="btn-modern btn-outline-modern">
                                <i class="fas fa-users"></i>
                                <span>Xem tất cả</span>
                            </a>
                        </div>
                    </div>
                    <div class="modern-card-body">
                        @if($recentUsers->count() > 0)
                            <div class="users-list">
                                @foreach($recentUsers as $index => $user)
                                <div class="user-item" data-aos="fade-up" data-aos-delay="{{ 600 + ($index * 100) }}">
                                    <div class="user-avatar">
                                        <div class="avatar-circle bg-gradient-{{ $user->role === 'admin' ? 'danger' : 'primary' }}">
                                            <i class="fas fa-{{ $user->role === 'admin' ? 'crown' : 'user' }}"></i>
                                        </div>
                                    </div>
                                    <div class="user-info">
                                        <div class="user-name">{{ $user->name }}</div>
                                        <div class="user-email">{{ $user->email }}</div>
                                        <div class="user-meta">
                                            <span class="role-badge role-{{ $user->role }}">
                                                {{ ucfirst($user->role) }}
                                            </span>
                                            @if($user->credit)
                                                <span class="credits-info">
                                                    <i class="fas fa-coins"></i>
                                                    {{ $user->getAvailableCredits() }} credits
                                                </span>
                                            @endif
                                        </div>
                                    </div>
                                    <div class="user-actions">
                                        <a href="{{ route('admin.users.show', $user->id) }}" class="btn-action btn-view">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ route('admin.users.edit', $user->id) }}" class="btn-action btn-edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                    </div>
                                </div>
                                @endforeach
                            </div>
                        @else
                            <div class="empty-state">
                                <div class="empty-illustration">
                                    <i class="fas fa-users"></i>
                                </div>
                                <h4 class="empty-title">Chưa có người dùng mới</h4>
                                <p class="empty-subtitle">Các tài khoản mới sẽ hiển thị ở đây</p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Recent Attempts -->
            <div class="col-lg-6">
                <div class="modern-card" data-aos="fade-up" data-aos-delay="600">
                    <div class="modern-card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="section-title">
                                <h3 class="title">Bài thi gần đây</h3>
                                <p class="subtitle">Hoạt động chấm thi mới nhất</p>
                            </div>
                            <a href="#" class="btn-modern btn-outline-modern">
                                <i class="fas fa-file-alt"></i>
                                <span>Xem tất cả</span>
                            </a>
                        </div>
                    </div>
                    <div class="modern-card-body">
                        @if($recentAttempts->count() > 0)
                            <div class="attempts-list">
                                @foreach($recentAttempts as $index => $attempt)
                                <div class="attempt-item" data-aos="fade-up" data-aos-delay="{{ 700 + ($index * 100) }}">
                                    <div class="attempt-info">
                                        <div class="attempt-user">{{ $attempt->user->name }}</div>
                                        <div class="attempt-type">
                                            <span class="type-badge">{{ ucfirst(str_replace('_', ' ', $attempt->task_type)) }}</span>
                                        </div>
                                        <div class="attempt-time">{{ $attempt->created_at->diffForHumans() }}</div>
                                    </div>
                                    <div class="attempt-score">
                                        @if($attempt->isCompleted())
                                            <div class="score-display">
                                                <div class="score-number">{{ $attempt->overall_band_score }}</div>
                                                <div class="score-label">Band</div>
                                            </div>
                                        @else
                                            <div class="score-pending">
                                                <i class="fas fa-clock"></i>
                                                <span>Đang xử lý</span>
                                            </div>
                                        @endif
                                    </div>
                                    <div class="attempt-status">
                                        <span class="status-badge status-{{ $attempt->status }}">
                                            @if($attempt->status === 'completed')
                                                <i class="fas fa-check"></i>
                                                Hoàn thành
                                            @elseif($attempt->status === 'pending')
                                                <i class="fas fa-clock"></i>
                                                Đang xử lý
                                            @else
                                                <i class="fas fa-times"></i>
                                                Lỗi
                                            @endif
                                        </span>
                                    </div>
                                </div>
                                @endforeach
                            </div>
                        @else
                            <div class="empty-state">
                                <div class="empty-illustration">
                                    <i class="fas fa-file-alt"></i>
                                </div>
                                <h4 class="empty-title">Chưa có bài thi nào</h4>
                                <p class="empty-subtitle">Các bài thi mới sẽ hiển thị ở đây</p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Admin Actions -->
    <div class="container-fluid">
        <div class="quick-admin-actions">
            <div class="row">
                <div class="col-12">
                    <div class="modern-card" data-aos="fade-up" data-aos-delay="800">
                        <div class="modern-card-header">
                            <div class="section-title">
                                <h3 class="title">Hành động quản trị</h3>
                                <p class="subtitle">Các tính năng quản lý hệ thống</p>
                            </div>
                        </div>
                        <div class="modern-card-body">
                            <div class="row g-4">
                                <div class="col-lg-3 col-md-6">
                                    <a href="{{ route('admin.users.create') }}" class="admin-action-card" data-aos="zoom-in" data-aos-delay="900">
                                        <div class="action-icon bg-gradient-primary">
                                            <i class="fas fa-user-plus"></i>
                                        </div>
                                        <div class="action-content">
                                            <h4 class="action-title">Thêm người dùng</h4>
                                            <p class="action-description">Tạo tài khoản mới cho hệ thống</p>
                                        </div>
                                        <div class="action-arrow">
                                            <i class="fas fa-arrow-right"></i>
                                        </div>
                                    </a>
                                </div>
                                <div class="col-lg-3 col-md-6">
                                    <a href="{{ route('admin.users.index') }}" class="admin-action-card" data-aos="zoom-in" data-aos-delay="1000">
                                        <div class="action-icon bg-gradient-success">
                                            <i class="fas fa-users-cog"></i>
                                        </div>
                                        <div class="action-content">
                                            <h4 class="action-title">Quản lý users</h4>
                                            <p class="action-description">Xem và chỉnh sửa thông tin người dùng</p>
                                        </div>
                                        <div class="action-arrow">
                                            <i class="fas fa-arrow-right"></i>
                                        </div>
                                    </a>
                                </div>
                                <div class="col-lg-3 col-md-6">
                                    <div class="admin-action-card action-disabled" data-aos="zoom-in" data-aos-delay="1100">
                                        <div class="action-icon bg-gradient-warning">
                                            <i class="fas fa-chart-bar"></i>
                                        </div>
                                        <div class="action-content">
                                            <h4 class="action-title">Báo cáo</h4>
                                            <p class="action-description">Thống kê chi tiết (Sắp ra mắt)</p>
                                        </div>
                                        <div class="action-arrow">
                                            <i class="fas fa-clock"></i>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-3 col-md-6">
                                    <div class="admin-action-card action-disabled" data-aos="zoom-in" data-aos-delay="1200">
                                        <div class="action-icon bg-gradient-info">
                                            <i class="fas fa-cogs"></i>
                                        </div>
                                        <div class="action-content">
                                            <h4 class="action-title">Cài đặt</h4>
                                            <p class="action-description">Cấu hình hệ thống (Sắp ra mắt)</p>
                                        </div>
                                        <div class="action-arrow">
                                            <i class="fas fa-clock"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Modern Admin Dashboard Styles */
:root {
    --admin-gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --admin-gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --admin-gradient-warning: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    --admin-gradient-info: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    --admin-gradient-danger: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    --admin-shadow-soft: 0 10px 40px rgba(0,0,0,0.1);
    --admin-shadow-medium: 0 20px 60px rgba(0,0,0,0.15);
    --admin-border-radius: 20px;
    --admin-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modern-admin-dashboard {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 0;
}

/* Admin Hero Section */
.admin-hero-section {
    background: var(--admin-gradient-primary);
    padding: 80px 0;
    position: relative;
    overflow: hidden;
}

.admin-hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="admin-grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23admin-grain)"/></svg>');
    opacity: 0.3;
}

.hero-content {
    position: relative;
    z-index: 2;
}

.hero-title {
    color: white;
    font-size: 3.5rem;
    font-weight: 800;
    margin-bottom: 1rem;
    line-height: 1.2;
    display: flex;
    align-items: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.admin-badge {
    background: rgba(255,255,255,0.2);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255,255,255,0.3);
    padding: 0.5rem 1rem;
    border-radius: 50px;
    font-size: 0.8rem;
    font-weight: 600;
    letter-spacing: 2px;
}

.title-text {
    background: linear-gradient(45deg, #fff, #f0f8ff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    color: rgba(255,255,255,0.9);
    font-size: 1.2rem;
    margin-bottom: 2rem;
    font-weight: 300;
}

.hero-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.admin-illustration {
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
}

.floating-admin-card {
    width: 200px;
    height: 200px;
    background: rgba(255,255,255,0.1);
    backdrop-filter: blur(20px);
    border-radius: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 4rem;
    color: #ffd700;
    animation: adminFloat 6s ease-in-out infinite;
    border: 1px solid rgba(255,255,255,0.2);
    box-shadow: 0 20px 60px rgba(0,0,0,0.2);
}

@keyframes adminFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(5deg); }
}

/* Modern Buttons */
.btn-modern {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 2rem;
    border-radius: 50px;
    font-weight: 600;
    text-decoration: none;
    transition: var(--admin-transition);
    border: none;
    cursor: pointer;
    font-size: 1rem;
}

.btn-primary-modern {
    background: rgba(255,255,255,0.2);
    color: white;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255,255,255,0.3);
}

.btn-primary-modern:hover {
    background: rgba(255,255,255,0.3);
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    color: white;
}

.btn-secondary-modern {
    background: transparent;
    color: white;
    border: 2px solid rgba(255,255,255,0.3);
}

.btn-secondary-modern:hover {
    background: rgba(255,255,255,0.1);
    border-color: rgba(255,255,255,0.5);
    transform: translateY(-2px);
    color: white;
}

.btn-outline-modern {
    background: transparent;
    color: #667eea;
    border: 2px solid #667eea;
    padding: 0.75rem 1.5rem;
}

.btn-outline-modern:hover {
    background: #667eea;
    color: white;
    transform: translateY(-2px);
}
</style>

<!-- AOS Animation Library -->
<link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
<script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    AOS.init({
        duration: 800,
        easing: 'ease-out-cubic',
        once: true,
        offset: 100
    });
});
</script>
@endsection
