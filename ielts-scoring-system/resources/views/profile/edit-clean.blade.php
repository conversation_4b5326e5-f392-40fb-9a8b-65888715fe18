@extends('layouts.app')

@section('title', 'Chỉnh sửa thông tin cá nhân')

@push('styles')
<link rel="stylesheet" href="{{ asset('css/profile.css') }}">
@endpush

@section('content')
<div class="profile-page">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <!-- Page Header -->
                <div class="page-header">
                    <div class="header-content">
                        <h1 class="page-title">
                            <i class="fas fa-user-edit"></i>
                            Chỉnh sửa thông tin cá nhân
                        </h1>
                        <p class="page-subtitle">
                            Cập nhật thông tin tài khoản của bạn
                        </p>
                    </div>
                    <div class="header-actions">
                        <a href="{{ route('profile.show') }}" class="btn-back">
                            <i class="fas fa-arrow-left"></i>
                            Quay lại
                        </a>
                    </div>
                </div>

                <!-- Success Message -->
                @if(session('success'))
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle me-2"></i>
                        {{ session('success') }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                @endif

                <!-- Edit Form -->
                <form method="POST" action="{{ route('profile.update') }}">
                    @csrf
                    @method('PUT')

                    <!-- Form Fields -->
                    <div class="form-section">
                        <h3 class="section-title">
                            <i class="fas fa-info-circle"></i>
                            Thông tin cơ bản
                        </h3>

                        <div class="row g-4">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="name" class="form-label">
                                        <i class="fas fa-user"></i>
                                        Họ và tên
                                    </label>
                                    <input type="text"
                                           class="form-control @error('name') is-invalid @enderror"
                                           id="name"
                                           name="name"
                                           value="{{ old('name', $user->name) }}"
                                           required>
                                    @error('name')
                                        <div class="invalid-feedback">
                                            {{ $message }}
                                        </div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="email" class="form-label">
                                        <i class="fas fa-envelope"></i>
                                        Email
                                    </label>
                                    <input type="email"
                                           class="form-control @error('email') is-invalid @enderror"
                                           id="email"
                                           name="email"
                                           value="{{ old('email', $user->email) }}"
                                           required>
                                    @error('email')
                                        <div class="invalid-feedback">
                                            {{ $message }}
                                        </div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Account Info (Read-only) -->
                    <div class="form-section">
                        <h3 class="section-title">
                            <i class="fas fa-shield-alt"></i>
                            Thông tin tài khoản
                        </h3>

                        <div class="row g-4">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="role" class="form-label">
                                        <i class="fas fa-user-tag"></i>
                                        Vai trò
                                    </label>
                                    <input type="text"
                                           class="form-control"
                                           id="role"
                                           value="{{ ucfirst($user->role) }}"
                                           readonly>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="created_at" class="form-label">
                                        <i class="fas fa-calendar-alt"></i>
                                        Ngày tham gia
                                    </label>
                                    <input type="text"
                                           class="form-control"
                                           id="created_at"
                                           value="{{ $user->created_at->format('d/m/Y H:i') }}"
                                           readonly>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="available_credits" class="form-label">
                                        <i class="fas fa-coins"></i>
                                        Credits khả dụng
                                    </label>
                                    <input type="text"
                                           class="form-control"
                                           id="available_credits"
                                           value="{{ $user->getAvailableCredits() }} credits"
                                           readonly>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="total_credits" class="form-label">
                                        <i class="fas fa-chart-line"></i>
                                        Tổng credits
                                    </label>
                                    <input type="text"
                                           class="form-control"
                                           id="total_credits"
                                           value="{{ $user->credit ? $user->credit->credits : 0 }} credits"
                                           readonly>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            Lưu thay đổi
                        </button>
                        <a href="{{ route('profile.show') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times"></i>
                            Hủy bỏ
                        </a>
                    </div>
                </form>

                <!-- Quick Actions -->
                <div class="quick-actions">
                    <h3>
                        <i class="fas fa-bolt"></i>
                        Hành động nhanh
                    </h3>
                    <a href="{{ route('profile.change-password') }}" class="action-link">
                        <i class="fas fa-key"></i>
                        <div>
                            <strong>Đổi mật khẩu</strong>
                            <small class="d-block text-muted">Cập nhật mật khẩu bảo mật cho tài khoản của bạn</small>
                        </div>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
